'use client';
import { Add, AddCircleOutline, ArrowBackOutlined, DeleteOutlineOutlined, InfoOutlined } from '@mui/icons-material';
import { Avatar, Box, Button, Divider, IconButton, Stack, Typography } from '@mui/material';
import { Breadcrumbs, DetailRow, Dialog, FormTextInput, ImageReviewModal, PlotOptionBox } from 'components';
import { DurianVarietyBox } from 'components/durian-variety';
import { flatMap } from 'lodash-es';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { FC, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { clientRoutes } from 'routes/client-routes';
import { theme } from 'styles/theme';
import {
  CreateHarvesting,
  Cutter,
  CutterVehicle,
  DurianVariety,
  Farm,
  PackingHouseDetail,
  Plot,
  Province,
  ReceiveUpdatePayload,
} from 'types';
import { formatNumberWithCommas, getImageUrl, REGEX_INPUT_NAME_FIELD } from 'utils';
import {
  convertModalDataToVarieties,
  convertResponseToWeights,
  reorderToOriginalOrder,
  transformDurianDataEvent,
} from 'utils/event';
import { renderImageContent, TotalWeightInfoDrawer } from '../_components';
import { DD_MMMM_YYYY_WITH_DASH, FIXED_LOCATION } from 'constant/common';
import EditIcon from '@mui/icons-material/BorderColorOutlined';
import { CustomDatePicker } from 'components/date-picker/custom-date-picker';
import dayjs, { Dayjs } from 'dayjs';
import { CreateVarietiesModal } from '../_components/create-variety-modal';
import emptyStateIcon from 'assets/icons/empty-state.svg';
import Image from 'next/image';
import { CreateCutterDrawer } from '../_components/create-cutter/create-cutter-drawer';
import { EditSelectCutterInfoDrawer } from '../_components/edit-select-cutter-info-drawer';
import { colors } from 'styles/colors';
import { AddFarmDrawer } from '../_components/add-farm/add-farm-drawer';
import UploadPlotImage from '../_components/add-farm/upload-plot-image';
import { AddPlotDrawer } from '../_components/add-farm/add-plot-drawer';
import { useCreateHarvestingMutate } from 'hooks/mutates/useCreateHarvestingMutate';
import { getCookieLocale } from 'utils/cookie-client';
import { useQuery } from '@tanstack/react-query';
import { fetchProvinceService } from 'services/resource.service';
import { useUpdateHarvestingMutate } from 'hooks/mutates/useUpdateHarvestingMutate';
import { sendEvent } from 'utils/gtag';

type CreateEventProps = {
  eventDetail?: PackingHouseDetail;
};

type BatchNameForm = {
  batchName: string;
};

export const CreateEvent: FC<CreateEventProps> = ({ eventDetail: data }) => {
  const receiveTranslation = useTranslations('receive');
  const commonTranslation = useTranslations('common');
  const formTranslation = useTranslations('form');

  const { mutateAsync: createHarvestingMutate } = useCreateHarvestingMutate();
  const { mutateAsync: updateHarvestingMutate } = useUpdateHarvestingMutate();

  const locale = getCookieLocale() ?? 'th';

  const {
    control: batchNameControl,
    formState: { errors: batchNameErrors },
    watch,
    setValue: setBatchNameValue,
  } = useForm<BatchNameForm>({
    defaultValues: {
      batchName: '',
    },
  });

  const batchName = watch('batchName');
  const [cuttingDay, setCuttingDay] = useState<Dayjs>(dayjs());
  const [currentVarieties, setCurrentVarieties] = useState<DurianVariety[] | null>();
  const [selectedCutter, setSelectedCutter] = useState<Cutter | null>(null);
  const [selectedCutterVehicle, setSelectedCutterVehicle] = useState<CutterVehicle | null>(null);
  const [selectedPlots, setSelectedPlots] = useState<Plot[]>([]);
  const [selectedFarm, setSelectedFarm] = useState<Farm | null>(null);
  const [selectedDeletePlot, setSelectedDeletePlot] = useState<string | null>(null);
  const [selectedVarietyId, setSelectedVarietyId] = useState<string | null>(null);

  const [hasDataChanged, setHasDataChanged] = useState<boolean>(false);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);

  const { data: provinces } = useQuery({
    queryKey: ['provinces'],
    queryFn: () => {
      return fetchProvinceService();
    },
  });

  const formatWalkInPhoneNumber = (phoneNumber: string) => {
    if (!phoneNumber) {
      return '';
    }

    if (phoneNumber.startsWith('+66') || phoneNumber.startsWith('66')) {
      return phoneNumber;
    }

    if (!phoneNumber.startsWith('0')) {
      return `0${phoneNumber}`;
    }

    return phoneNumber;
  };

  useEffect(() => {
    if (data) {
      const batchNameValue = data.name;
      const cuttingDayValue =
        data.meta.cuttingDay && Number(data.meta.cuttingDay) ? dayjs(Number(data.meta.cuttingDay) * 1000) : dayjs();
      const varietiesValue = data.varieties;

      let cutterValue = null;
      if (data.meta.cutter) {
        cutterValue = {
          ...data.meta.cutter,
          profileId: data.meta.cutter.existingCutterProfileId,
          id: data.meta.cutter.existingCutterId,
          phoneNumber: data.meta.cutter.existingCutterId
            ? data.meta.cutter.suggestedPhoneNumber
            : formatWalkInPhoneNumber(data.meta.cutter.suggestedPhoneNumber?.toString() ?? ''),
        };
      }

      const cutterVehicleValue = data.meta.cutterVehicle || null;
      const farmValue = data.farm?.id && data.farm?.address && data.farm?.name ? data.farm : null;

      let plotsValue: Plot[] = [];
      if (data.meta.farm && data.farm.id === data.meta.farm.farmId && data.meta.farm.plots.length > 0) {
        plotsValue = data.meta.farm.plots.map((plot) => {
          return {
            ...plot,
            image: plot.image?.filenameDisk,
            areaUnit: plot.areaUnit?.[locale] ?? '',
          };
        });
      }

      // Set form values
      setBatchNameValue('batchName', batchNameValue);
      setCuttingDay(cuttingDayValue);
      setCurrentVarieties(varietiesValue);
      setSelectedCutter(cutterValue);
      setSelectedCutterVehicle(cutterVehicleValue);
      setSelectedFarm(farmValue);
      setSelectedPlots(plotsValue);

      setIsInitialized(true);
    }

    // toto: need to fix
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, locale]);

  useEffect(() => {
    if (isInitialized && data && batchName !== data.name) {
      setHasDataChanged(true);
    }
  }, [batchName, data, isInitialized]);

  const [isOpen, setIsOpen] = useState<'back' | 'cutter' | 'farm' | 'delete' | 'remove' | null>(null);
  const [isOpenDrawer, setIsOpenDrawer] = useState<'total-weight' | 'cutter' | 'farm' | 'plot' | 'edit-cutter' | null>(
    null
  );

  const [isOpenReceive, setIsOpenReceive] = useState<boolean>(false); // for create varieties modal
  const [dialogData, setDialogData] = useState<{
    title: string;
    content: string;
    onConfirm: () => void;
    type: 'info' | 'danger';
  }>({
    title: '',
    content: '',
    onConfirm: () => {},
    type: 'info',
  });

  const router = useRouter();

  const handleBackClick = () => {
    if (hasDataChanged) {
      setIsOpen('back');
    } else {
      setIsOpen(null);
      router.back();
    }
  };

  useEffect(() => {
    if (isOpen === 'back') {
      setDialogData({
        title: receiveTranslation('back-to-prev-page-title'),
        content: receiveTranslation('back-to-prev-page-content'),
        onConfirm: () => {
          router.back();
        },
        type: 'info',
      });
    }
    if (isOpen === 'cutter') {
      setDialogData({
        title: receiveTranslation('change-cutter-title'),
        content: receiveTranslation('change-cutter-content'),
        onConfirm: () => {
          setSelectedCutter(null);
          setSelectedCutterVehicle(null);
          setIsOpen(null);
          setIsOpenDrawer('cutter');
          setHasDataChanged(true);
        },
        type: 'info',
      });
    }
    if (isOpen === 'farm') {
      setDialogData({
        title: receiveTranslation('change-farm-title'),
        content: receiveTranslation('change-farm-content'),
        onConfirm: () => {
          setSelectedFarm(null);
          setSelectedPlots([]);
          setIsOpen(null);
          setIsOpenDrawer('farm');
          setHasDataChanged(true);
        },
        type: 'info',
      });
    }
    if (isOpen === 'delete') {
      setDialogData({
        title: receiveTranslation('delete-plot-title'),
        content: receiveTranslation('delete-plot-content'),
        type: 'danger',
        onConfirm: () => {
          if (selectedDeletePlot) {
            const newSelectedPlots = selectedPlots.filter((plot) => {
              return plot.id !== selectedDeletePlot;
            });
            setSelectedPlots(newSelectedPlots);
            setHasDataChanged(true);
          }
          setIsOpen(null);
          setSelectedDeletePlot(null);
        },
      });
    }
    if (isOpen === 'remove') {
      const variety = currentVarieties?.find((v) => v.id === selectedVarietyId);
      let varietyName = variety?.label[locale] ?? '';
      if (variety?.value === 'other') {
        varietyName = variety?.name ?? '';
      }
      setDialogData({
        title: receiveTranslation('delete-variety-title'),
        content: receiveTranslation('delete-variety-content', {
          varietyName: varietyName,
        }),
        type: 'danger',
        onConfirm: () => {
          const newVarieties = currentVarieties?.filter((v) => v.id !== selectedVarietyId);
          setCurrentVarieties(newVarieties);
          setHasDataChanged(true);
          setSelectedVarietyId(null);
          setIsOpen(null);
        },
      });
    }
  }, [
    isOpen,
    receiveTranslation,
    router,
    selectedPlots,
    selectedDeletePlot,
    selectedVarietyId,
    currentVarieties,
    locale,
  ]);

  const handleCancel = () => {
    setIsOpen(null);
  };

  const handleCloseReceive = () => {
    setIsOpenReceive(false);
  };

  const clientName = useMemo(() => {
    const otherVariety = currentVarieties?.find((v) => v.value === 'other');
    return otherVariety?.name ?? '';
  }, [currentVarieties]);

  const totalWeight = useMemo(() => {
    if (!currentVarieties?.length) return 0;

    const grades = flatMap(currentVarieties, (it) => it.grades);

    const total = grades.reduce((pre, current) => {
      const sumWeight = pre + (current?.weight ?? 0);

      return sumWeight;
    }, 0);

    return total;
  }, [currentVarieties]);

  const varietyBloomDays = useMemo(() => {
    const bloomDays: Record<string, number> = {};
    currentVarieties?.forEach((v) => {
      if (v.id && v.flowerBloomingDay) {
        bloomDays[`${v.id}`] = v.flowerBloomingDay;
      }
    });
    return bloomDays;
  }, [currentVarieties]);

  const transformedVarieties = useMemo(() => {
    if (Array.isArray(currentVarieties) && currentVarieties.length > 0) {
      return transformDurianDataEvent(currentVarieties);
    }
    return [];
  }, [currentVarieties]);

  const modalInitialWeights = useMemo(() => {
    if (Array.isArray(currentVarieties) && currentVarieties.length > 0) {
      return convertResponseToWeights(currentVarieties);
    }
  }, [currentVarieties]);

  const modalInitialClientName = useMemo(() => {
    return clientName;
  }, [clientName]);

  const handleSaveVarieties = (_data: ReceiveUpdatePayload, varietyData: DurianVariety[]) => {
    const updatedVarieties = convertModalDataToVarieties(_data, varietyData);
    setCurrentVarieties(reorderToOriginalOrder(varietyData, updatedVarieties) as DurianVariety[]);
    setHasDataChanged(true);
    handleCloseReceive();
    sendEvent('enter_variety_grade_weight');
  };

  const breadcrumbItems = useMemo(() => {
    return [
      {
        label: receiveTranslation('incomingBatchLot'),
        href: clientRoutes.eventIncoming,
        showConfirmation: hasDataChanged,
      },
      {
        label: receiveTranslation('create-harvest-log'),
        href: clientRoutes.event,
      },
    ];
  }, [receiveTranslation, hasDataChanged]);

  const renderUploadImage = (plotId: string) => {
    return (
      <UploadPlotImage
        onChange={(img: string) => {
          const newSelectedPlots = selectedPlots.map((plot) => {
            if (plot.id === plotId) {
              plot.image = img;
            }
            return plot;
          });
          setSelectedPlots(newSelectedPlots);
          setHasDataChanged(true);
        }}
      />
    );
  };

  const handleRemoveImageFromPlot = (plotId: string) => {
    const newSelectedPlots = selectedPlots.map((plot) => {
      if (plot.id === plotId) {
        plot.image = '';
      }
      return plot;
    });
    setSelectedPlots(newSelectedPlots);
    setHasDataChanged(true);
  };

  const renderDeletePlotContent = (plotId: string) => {
    return (
      <IconButton
        color="error"
        onClick={() => {
          setSelectedDeletePlot(plotId);
          setIsOpen('delete');
        }}
      >
        <DeleteOutlineOutlined fontSize="inherit" color="inherit" />
      </IconButton>
    );
  };

  const enabledSaveAsDraft = useMemo(() => {
    return Boolean(batchName.trim());
  }, [batchName]);

  const isFutureCuttingDay = useMemo(() => {
    return cuttingDay && cuttingDay.isAfter(dayjs(), 'day');
  }, [cuttingDay]);

  const enabledReceive = useMemo(() => {
    return Boolean(
      batchName.trim() &&
        cuttingDay &&
        currentVarieties &&
        currentVarieties.length > 0 &&
        selectedCutter &&
        selectedCutterVehicle &&
        selectedFarm &&
        selectedPlots.length > 0 &&
        !selectedPlots.some((plot) => Boolean(plot.gap && !plot.image)) &&
        !isFutureCuttingDay
    );
  }, [
    batchName,
    cuttingDay,
    currentVarieties,
    selectedCutter,
    selectedCutterVehicle,
    selectedFarm,
    selectedPlots,
    isFutureCuttingDay,
  ]);

  const prepareCutterVehiclePayload = useMemo(() => {
    if (!selectedCutterVehicle) return undefined;
    let img = null;
    if (
      selectedCutterVehicle.image?.filenameDisk?.split?.('.')[0] &&
      selectedCutterVehicle.image?.filenameDisk?.split?.('.')[0] !== ''
    ) {
      img = selectedCutterVehicle.image.filenameDisk.split('.')[0];
    }
    return {
      image: img,
      provinceRegistrationNumber: selectedCutterVehicle.provinceRegistrationNumber,
      vehicleRegistrationNumber: selectedCutterVehicle.vehicleRegistrationNumber,
    };
  }, [selectedCutterVehicle]);

  const prepareCutterPayload = useMemo(() => {
    if (!selectedCutter) return undefined;
    let avatarImg = null;
    if (
      selectedCutter?.avatar?.filenameDisk?.split?.('.')[0] &&
      selectedCutter?.avatar?.filenameDisk?.split('.')[0] !== ''
    ) {
      avatarImg = selectedCutter.avatar.filenameDisk.split('.')[0];
    }
    return {
      name: selectedCutter.name,
      avatar: avatarImg,
      isCertified: Boolean(selectedCutter.isCertified),
      existingCutterId: selectedCutter.id !== '' ? selectedCutter.id : null,
      existingCutterProfileId:
        selectedCutter.profileId !== '' || selectedCutter !== undefined ? selectedCutter.profileId : null,
      suggestedPhoneNumber: selectedCutter.phoneNumber,
    };
  }, [selectedCutter]);

  const preparePayload = (asDraft: boolean): CreateHarvesting => {
    return {
      batchName: batchName,
      status: asDraft ? 'draft' : 'consumed',
      latitude: FIXED_LOCATION.SHIPMENT.latitude,
      longitude: FIXED_LOCATION.SHIPMENT.longitude,
      harvestImage: [],
      cuttingDay: cuttingDay.isValid() ? cuttingDay.unix() : undefined,
      cutterVehicle: prepareCutterVehiclePayload,
      farm: selectedFarm
        ? {
            farmId: selectedFarm.id,
            plots: selectedPlots.map((plot) => {
              return {
                id: plot.id,
                image: plot.image?.split?.('.')[0] || undefined,
              };
            }),
          }
        : undefined,
      varieties: currentVarieties ?? undefined,
      cutter: prepareCutterPayload,
    };
  };

  const handleSave = async (asDraft: boolean) => {
    const trimmedBatchName = batchName.trim();

    // Check if batch name is empty or doesn't match regex pattern
    if (!trimmedBatchName || !REGEX_INPUT_NAME_FIELD.test(trimmedBatchName)) {
      setBatchNameValue('batchName', trimmedBatchName || '', { shouldValidate: true });
      return;
    }

    const payload = preparePayload(Boolean(asDraft));
    if (data?.id && data?.eventId) {
      await updateHarvestingMutate({ productId: data.eventId, formValues: payload });
    } else {
      await createHarvestingMutate(payload);
    }
    sendEvent(asDraft ? 'save_as_draft' : 'submit_harvest_record');
    return router.push(clientRoutes.eventIncoming);
  };

  return (
    <>
      <Box sx={{ padding: '20px', backgroundColor: theme.palette.customColors.lightGray }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Breadcrumbs items={breadcrumbItems} />
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              <IconButton onClick={handleBackClick}>
                <ArrowBackOutlined fontSize="small" />
              </IconButton>
              <Typography variant="h3">{receiveTranslation('create-harvest-log')}</Typography>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              sx={{ height: '40px', width: '200px' }}
              disabled={!enabledSaveAsDraft}
              onClick={() => handleSave(true)}
            >
              {receiveTranslation('save-as-draft')}
            </Button>
            <Button
              variant="contained"
              sx={{ height: '40px', width: '200px' }}
              disabled={!enabledReceive}
              onClick={() => {
                handleSave(false);
              }}
            >
              {receiveTranslation('receive')}
            </Button>
          </Box>
        </Box>
        {/* Detail */}

        <Stack flexDirection="row" gap={2} sx={{ marginTop: 2 }}>
          <Stack component="div" flexDirection="column" id="column-left" sx={{ gap: 2, flex: 1 }}>
            <Box
              id="durian-harvesting-summary"
              sx={{
                borderRadius: '8px',
                border: '1px solid #0000001A',
                background: 'white',
                height: 'auto',
                padding: 3,
                display: 'flex',
                flexDirection: 'column',
                gap: 3,
              }}
              component={'div'}
            >
              <Typography sx={{ fontWeight: 600 }} variant="body1">
                {receiveTranslation('durian-harvesting-summary')}
              </Typography>
              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 2,
                  height: 'auto',
                  overflowY: 'auto',
                }}
              >
                <Box sx={{ display: 'flex', gap: 0.25, flexDirection: 'column' }}>
                  <Typography sx={{ fontSize: '18px' }} variant="caption">
                    {receiveTranslation('batch-name')}{' '}
                    <Typography sx={{ visibility: 'visible' }} variant="caption" color="error">
                      *
                    </Typography>
                  </Typography>

                  <Box sx={{ display: 'flex', gap: '12px' }}>
                    <FormTextInput<BatchNameForm>
                      name="batchName"
                      required
                      requiredMessage={formTranslation('required', {
                        formName: 'Batch name',
                      })}
                      pattern={REGEX_INPUT_NAME_FIELD}
                      patternMessage={formTranslation('not-allow-characters')}
                      errors={batchNameErrors}
                      control={batchNameControl}
                      placeholder={receiveTranslation('batch-name-placeholder')}
                      maxLength={100}
                    />
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', gap: 0.25, flexDirection: 'column' }}>
                  <Typography sx={{ fontSize: '18px' }} variant="caption">
                    {receiveTranslation('durian-cutting-day')}{' '}
                    <Typography sx={{ visibility: 'visible' }} variant="caption" color="error">
                      *
                    </Typography>
                  </Typography>

                  <Box sx={{ display: 'flex', gap: '12px' }}>
                    <CustomDatePicker
                      value={cuttingDay}
                      onChange={(date) => {
                        setCuttingDay(dayjs(date));
                        setHasDataChanged(true);
                      }}
                      sx={{ width: '100%' }}
                      format={DD_MMMM_YYYY_WITH_DASH}
                      slotProps={{
                        textField: {},
                      }}
                    />
                  </Box>
                  {isFutureCuttingDay && (
                    <Typography
                      variant="caption"
                      sx={{
                        fontWeight: 300,
                        color: colors.neutral500,
                        fontSize: 16,
                      }}
                    >
                      {receiveTranslation('cutting-day-in-future-helper-text')}
                    </Typography>
                  )}
                </Box>
              </Box>
            </Box>

            <Box
              id="durian-variety-grade-weight"
              sx={{
                borderRadius: '8px',
                border: '1px solid #0000001A',
                background: 'white',
                height: 'auto',
                gap: 3,
                p: 3,
              }}
              component={'div'}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', pb: 2 }}>
                <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>
                    {receiveTranslation('durian-variety-grade-weight')}
                  </Typography>
                  <Typography sx={{ fontWeight: 300, color: colors.neutral500, fontSize: 16 }}>
                    ({receiveTranslation('required')})
                  </Typography>
                </Box>
                {transformedVarieties.length > 0 && (
                  <IconButton
                    sx={{
                      '&:hover': {
                        backgroundColor: 'transparent',
                      },
                      p: 0,
                    }}
                    disabled={!cuttingDay.isValid()}
                    onClick={() => setIsOpenReceive(true)}
                  >
                    <EditIcon
                      sx={{ color: cuttingDay.isValid() ? theme.palette.primary.main : theme.palette.grey[500] }}
                    />
                  </IconButton>
                )}
              </Box>
              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  height: 'auto',
                  overflowY: 'auto',
                }}
              >
                {transformedVarieties.length > 0 ? (
                  transformedVarieties.map((variety) => (
                    <DurianVarietyBox
                      key={variety.id}
                      variety={variety}
                      sx={{ textAlign: 'left' }}
                      showGrades={true}
                      onDelete={() => {
                        setSelectedVarietyId(variety.id);
                        setIsOpen('remove');
                      }}
                    />
                  ))
                ) : (
                  <Box
                    component="div"
                    sx={{
                      width: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      flexDirection: 'column',
                    }}
                  >
                    <Image src={emptyStateIcon} width={160} height={160} unoptimized alt="empty-state-icon" />
                    <Typography my="12px" fontSize="16px" color="text.secondary">
                      {receiveTranslation('no-variety-added')}
                    </Typography>
                  </Box>
                )}
                <Box
                  component="div"
                  sx={{
                    width: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    flexDirection: 'column',
                  }}
                >
                  <Button
                    onClick={() => setIsOpenReceive(true)}
                    disabled={!cuttingDay.isValid()}
                    variant="outlined"
                    sx={{
                      minWidth: '140px',
                      px: 3,
                      py: 1,
                      mt: 1,
                      fontSize: '0.875rem',
                      fontWeight: 500,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: 1,
                    }}
                  >
                    <Add />
                    <Typography>{receiveTranslation('select-durian-variety')}</Typography>
                  </Button>
                </Box>
                <DetailRow
                  title={receiveTranslation('total-weight')}
                  noPadding
                  sx={{ flexDirection: 'row', mt: 2 }}
                  content={
                    <Box sx={{ display: 'flex', gap: 3, alignItems: 'center' }}>
                      <Typography variant="body1">
                        {formatNumberWithCommas(totalWeight)} {commonTranslation('kg')}
                      </Typography>
                      {totalWeight !== 0 && (
                        <IconButton
                          size="small"
                          sx={{ color: theme.palette.info.main }}
                          onClick={() => setIsOpenDrawer('total-weight')}
                        >
                          <InfoOutlined />
                        </IconButton>
                      )}
                    </Box>
                  }
                />
              </Box>
            </Box>
          </Stack>

          <Stack component="div" flexDirection="column" id="column-right" sx={{ gap: 2, flex: 1 }}>
            <Box
              sx={{
                borderRadius: '8px',
                border: '1px solid #0000001A',
                background: 'white',
                height: 'auto',
                p: 3,
                gap: 3,
                display: 'flex',
                flexDirection: 'column',
                width: '100%',
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                <Typography sx={{ fontWeight: 600 }} variant="body1">
                  {receiveTranslation('cutter-information')}
                </Typography>
                {selectedCutter && selectedCutterVehicle && (
                  <Box sx={{ display: 'flex', gap: 0.5 }}>
                    <Button
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        color: colors.primary,
                        gap: 0.5,
                        alignItems: 'start',
                        paddingY: 0,
                      }}
                      onClick={() => {
                        setIsOpenDrawer('edit-cutter');
                      }}
                    >
                      <Typography sx={{ fontWeight: 400 }} variant="body1">
                        {commonTranslation('edit')}
                      </Typography>
                    </Button>
                    <Divider orientation="vertical" flexItem />
                    <Button
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        color: colors.primary,
                        gap: 0.5,
                        alignItems: 'start',
                        paddingY: 0,
                      }}
                      onClick={() => {
                        setIsOpen('cutter');
                      }}
                    >
                      <Typography sx={{ fontWeight: 400 }} variant="body1">
                        {receiveTranslation('change-cutter')}
                      </Typography>
                    </Button>
                  </Box>
                )}
              </Box>

              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  overflowY: 'auto',
                  height: 'auto',
                }}
              >
                {selectedCutter && selectedCutterVehicle ? (
                  <Box
                    sx={{
                      flex: 1,
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 2,
                      overflowY: 'auto',
                      height: 'auto',
                    }}
                  >
                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                      <Avatar
                        src={getImageUrl(selectedCutter?.avatar?.filenameDisk ?? '') ?? ''}
                        sx={{ height: 40, width: 40 }}
                      />
                      <DetailRow
                        title={receiveTranslation('cutter-name')}
                        content={
                          <Box display="flex" gap={1} alignItems="center">
                            <span>{selectedCutter.name}</span>
                          </Box>
                        }
                        noBorder
                      />
                    </Box>
                    <DetailRow
                      title={receiveTranslation('type')}
                      content={
                        selectedCutter.isCertified
                          ? receiveTranslation('registered-cutter')
                          : receiveTranslation('unregistered-cutter')
                      }
                      noBorder
                      noPadding
                    />

                    <DetailRow
                      title={receiveTranslation('vehicle-registration-number')}
                      content={selectedCutterVehicle.vehicleRegistrationNumber ?? ''}
                      noBorder
                      noPadding
                    />
                    <DetailRow
                      title={receiveTranslation('province-vehicle')}
                      content={
                        selectedCutterVehicle.provinceRegistrationNumber && provinces?.data
                          ? (() => {
                              const province = provinces.data.find(
                                (item: Province) =>
                                  item.provinceVehicleCode === selectedCutterVehicle.provinceRegistrationNumber
                              );
                              return province?.label?.[locale] ?? '';
                            })()
                          : ''
                      }
                      noBorder
                      noPadding
                    />
                    <ImageReviewModal imageUrl={getImageUrl(selectedCutterVehicle?.image?.filenameDisk ?? '') ?? ''} />
                  </Box>
                ) : (
                  <Button
                    sx={{
                      height: '120px',
                      border: `1px dashed ${theme.palette.customColors.gray5}`,
                      display: 'flex',
                      flexDirection: 'column',
                      color: theme.palette.text.primary,
                      padding: 2,
                      gap: 0.5,
                    }}
                    onClick={() => setIsOpenDrawer('cutter')}
                  >
                    <AddCircleOutline sx={{ width: 32, height: 32 }} />
                    <Typography sx={{ fontWeight: 500, fontSize: 16 }}>
                      {receiveTranslation('add-cutter-information')}
                    </Typography>
                    <Typography sx={{ fontWeight: 300, color: colors.neutral500, fontSize: 14 }}>
                      ({receiveTranslation('required')})
                    </Typography>
                  </Button>
                )}
              </Box>
            </Box>

            <Box
              sx={{
                width: '100%',
                borderRadius: '8px',
                border: '1px solid #0000001A',
                background: 'white',
                height: 'auto',
                p: 3,
                gap: 3,
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', pb: 2 }}>
                <Typography sx={{ fontWeight: 600 }} variant="body1">
                  {receiveTranslation('farm-information')}
                </Typography>
                {selectedFarm && (
                  <Button
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      color: colors.primary,
                      gap: 0.5,
                      alignItems: 'start',
                      paddingY: 0,
                    }}
                    onClick={() => {
                      setIsOpen('farm');
                    }}
                  >
                    <Typography sx={{ fontWeight: 400 }} variant="body1">
                      {receiveTranslation('change-farm')}
                    </Typography>
                  </Button>
                )}
              </Box>
              <Box
                sx={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 2,
                  overflowY: 'auto',
                  height: 'auto',
                }}
              >
                {selectedFarm ? (
                  <>
                    <DetailRow noPadding title={receiveTranslation('farm-name')} content={selectedFarm.name} noBorder />
                    <DetailRow
                      noPadding
                      title={receiveTranslation('farm-address')}
                      content={selectedFarm.address}
                      noBorder
                    />
                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        {receiveTranslation('farm-plot')}
                        <Typography sx={{ fontWeight: 300, color: colors.neutral500, fontSize: 16 }}>
                          ({receiveTranslation('required')})
                        </Typography>
                      </Box>
                      <Typography sx={{ fontWeight: 300, color: theme.palette.customColors.gray, fontSize: 14 }}>
                        {receiveTranslation('farm-plot-img-support')}
                      </Typography>
                    </Box>

                    {selectedPlots.length > 0 ? (
                      <>
                        {selectedPlots.map((plot) => (
                          <Box key={plot.id} sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                            <PlotOptionBox
                              key={plot.id}
                              plotNumber={plot?.name ?? ''}
                              plotId={plot?.plotId ?? ''}
                              plotGap={plot?.gap ?? ''}
                              plotArea={plot?.area ?? 0}
                              plotAreaUnit={plot?.areaUnit ?? ''}
                              renderActionButtons={() => (
                                <Box sx={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                                  {plot.image
                                    ? renderImageContent(plot, handleRemoveImageFromPlot)
                                    : renderUploadImage(plot.id)}
                                  {renderDeletePlotContent(plot.id)}
                                </Box>
                              )}
                              isError={Boolean(plot.gap && !plot.image)}
                            />
                            {Boolean(plot.gap && !plot.image) && (
                              <Typography variant="caption" color="error" sx={{ fontSize: '12px', mt: 0.5 }}>
                                {receiveTranslation('gap-img-require')}
                              </Typography>
                            )}
                          </Box>
                        ))}
                      </>
                    ) : (
                      <Box
                        sx={{
                          width: '100%',
                          display: 'flex',
                          alignItems: 'center',
                          flexDirection: 'column',
                        }}
                      >
                        <Image src={emptyStateIcon} width={160} height={160} unoptimized alt="empty-state-icon" />
                        <Typography my="12px" fontSize="16px" color="text.secondary">
                          {receiveTranslation('no-plot-selected')}
                        </Typography>
                      </Box>
                    )}
                    <Box
                      sx={{
                        width: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        flexDirection: 'column',
                      }}
                    >
                      <Button
                        onClick={() => setIsOpenDrawer('plot')}
                        variant="outlined"
                        sx={{
                          minWidth: '140px',
                          px: 3,
                          py: 1,
                          fontSize: '0.875rem',
                          fontWeight: 500,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          gap: 1,
                        }}
                      >
                        <Add />
                        <Typography>{receiveTranslation('select-plot')}</Typography>
                      </Button>
                    </Box>
                  </>
                ) : (
                  <Button
                    sx={{
                      height: '120px',
                      border: `1px dashed ${theme.palette.customColors.gray5}`,
                      display: 'flex',
                      flexDirection: 'column',
                      color: theme.palette.text.primary,
                      padding: 2,
                      gap: 0.5,
                    }}
                    onClick={() => setIsOpenDrawer('farm')}
                  >
                    <AddCircleOutline sx={{ width: 32, height: 32 }} />
                    <Typography sx={{ fontWeight: 500, fontSize: 16 }}>
                      {receiveTranslation('add-farm-information')}
                    </Typography>
                    <Typography sx={{ fontWeight: 300, color: colors.neutral500, fontSize: 14 }}>
                      ({receiveTranslation('required')})
                    </Typography>
                  </Button>
                )}
              </Box>
            </Box>
          </Stack>
        </Stack>
      </Box>
      <Dialog
        isOpen={isOpen !== null}
        title={dialogData?.title ?? ''}
        content={dialogData?.content ?? ''}
        type={dialogData.type}
        okButtonText={commonTranslation('ok-modal-btn')}
        onConfirm={dialogData?.onConfirm ?? (() => {})}
        onCancel={handleCancel}
      />

      {isOpenReceive && (
        <CreateVarietiesModal
          open={isOpenReceive}
          onClose={handleCloseReceive}
          onSave={handleSaveVarieties}
          initialWeights={modalInitialWeights}
          initialCustomName={modalInitialClientName}
          varietyBloomDays={varietyBloomDays}
          cuttingDay={cuttingDay}
        />
      )}

      {currentVarieties && (
        <TotalWeightInfoDrawer
          content={currentVarieties}
          total={totalWeight}
          open={isOpenDrawer === 'total-weight'}
          toggle={() => setIsOpenDrawer(isOpenDrawer === 'total-weight' ? null : 'total-weight')}
        />
      )}

      {isOpenDrawer === 'cutter' && (
        <CreateCutterDrawer
          open={isOpenDrawer === 'cutter'}
          toggle={() => setIsOpenDrawer(isOpenDrawer === 'cutter' ? null : 'cutter')}
          onAddCutter={(cutter: Cutter, vehicle: CutterVehicle) => {
            if (cutter && vehicle) {
              setSelectedCutter(cutter);
              setSelectedCutterVehicle(vehicle);
              setHasDataChanged(true);
            }
          }}
        />
      )}

      {isOpenDrawer === 'edit-cutter' && selectedCutter && selectedCutterVehicle && (
        <EditSelectCutterInfoDrawer
          open={isOpenDrawer === 'edit-cutter'}
          toggle={() => setIsOpenDrawer(isOpenDrawer === 'edit-cutter' ? null : 'edit-cutter')}
          currentCutter={selectedCutter}
          currentCutterVehicle={selectedCutterVehicle}
          onSave={(cutter: Cutter, vehicle: CutterVehicle) => {
            setSelectedCutter(cutter);
            setSelectedCutterVehicle(vehicle);
            setHasDataChanged(true);
          }}
        />
      )}

      {isOpenDrawer === 'farm' && (
        <AddFarmDrawer
          open={isOpenDrawer === 'farm'}
          toggle={() => setIsOpenDrawer(isOpenDrawer === 'farm' ? null : 'farm')}
          onAddFarm={(farm: Farm, plot: Plot[]) => {
            if (farm.address && farm.name && plot.length > 0) {
              setSelectedFarm(farm);
              setSelectedPlots(plot);
              setHasDataChanged(true);
            }
          }}
        />
      )}

      {selectedFarm && (
        <AddPlotDrawer
          open={isOpenDrawer === 'plot'}
          farm={selectedFarm}
          plot={selectedPlots}
          onAddPlot={(plot: Plot[]) => {
            const newSelectedPlot = [
              ...selectedPlots.filter((itemA) => plot.some((itemB) => itemB.id === itemA.id)),
              ...plot.filter((itemB) => !selectedPlots.some((itemA) => itemA.id === itemB.id)),
            ];
            setSelectedPlots(newSelectedPlot);
            setHasDataChanged(true);
            sendEvent('add_farm_plot');
          }}
          toggle={() => setIsOpenDrawer(isOpenDrawer === 'plot' ? null : 'plot')}
        />
      )}
    </>
  );
};
