import { Box, FormControl, TextFieldProps, Typography } from '@mui/material';
import { Checkbox } from 'components/input-form';
import { JSX, ReactElement } from 'react';
import { Control, Controller, FieldErrors, FieldValues, Path } from 'react-hook-form';

export interface FormCheckboxProps<TFormValues extends FieldValues> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  errors?: FieldErrors<TFormValues>;
  label?: string;
  required?: boolean;
  requiredMessage?: string;
  max?: number;
  min?: number;
  patternMessage?: string;
  pattern?: RegExp;
  loading?: boolean;
  minLength?: number;
  maxLength?: number;
  minLengthMessage?: string;
  maxLengthMessage?: string;
  validate?: Record<string, (value: unknown) => boolean | string>;
  fullWidth?: boolean;
  sx?: TextFieldProps['sx'];
  customBtn?: JSX.Element;
}

export function FormCheckbox<TFormValues extends FieldValues>({
  name,
  control,
  errors,
  label,
  required = false,
  requiredMessage,
  loading,
  pattern,
  patternMessage,
  minLength,
  maxLength = 100,
  minLengthMessage,
  maxLengthMessage,
  validate,
  fullWidth = true,
  sx,
  customBtn,
}: FormCheckboxProps<TFormValues>): ReactElement {
  const rules = {
    ...(required && { required: requiredMessage || `${label} is required` }),
    ...(pattern && { pattern: { value: pattern, message: patternMessage || `Invalid format for ${label}` } }),
    ...(minLength && {
      minLength: { value: minLength, message: minLengthMessage || `${label} must be at least ${minLength} characters` },
    }),
    ...(maxLength && {
      maxLength: { value: maxLength, message: maxLengthMessage || `${label} must not exceed ${maxLength} characters` },
    }),
    ...(validate && { validate }),
  };

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field }) => {
        return (
          <FormControl fullWidth={fullWidth} error={!!errors?.[name]}>
            <Box sx={{ display: 'flex', gap: '12px' }}>
              <Checkbox
                loading={loading}
                name={name}
                label={
                  label ? (
                    <Typography sx={{ fontSize: '18px', userSelect: 'none' }} variant="caption">
                      {label}{' '}
                      <Typography sx={{ visibility: required ? 'visible' : 'hidden' }} variant="caption" color="error">
                        *
                      </Typography>
                    </Typography>
                  ) : null
                }
                checked={field.value}
                onChange={(e) => field.onChange(e.target.checked)}
                sx={sx}
              />
              {customBtn}
            </Box>
          </FormControl>
        );
      }}
    />
  );
}
