# 📝 Changelog (Latest v1.0.6-prod — MCDT tasks by category)

## v1.0.6-prod - 2025-07-14

### ✨ Features
- **2025-07-11** - **MCDT-4070**: Merge branch 'main' into feat/mcdt-4070-update
- **2025-07-11** - **MCDT-4125**: feat: mcdt-4125 add comprehensive unit tests for mutation hooks
- **2025-07-10** - **MCDT-3149**: feat: mcdt-3149 implement delete draft shipment functionality
- **2025-07-10** - **MCDT-4072**: feat: mcdt-4072 add comprehensive unit tests for filter-table component
- **2025-07-09** - **MCDT-4012**: Merge branch 'main' into feat/mcdt-4012
- **2025-07-09** - **MCDT-4064**: feat: mcdt-4064 - update localization and improve test stability
- **2025-07-07** - **MCDT-3564**: feat: mcdt-3564-ut-create-list
- **2025-07-07** - **MCDT-3862**: feat: mcdt-3862: update unit test
- **2025-07-03** - **MCDT-3718**: feat: mcdt-3718 update send event
- **2025-07-01** - **MCDT-3586**: fix: update province code references to province vehicle code
- **2025-06-11** - **MCDT-2188**: update translation
- **2025-06-10** - **MCDT-2616**: fix: correct capitalization of "Modified on" in localization files
- **2025-05-30** - **MCDT-2112**: Merge branch 'main' into feat/MCDT-2112
- **2025-05-16** - **MCDT-1860**: feat(pdpa): Update flow
- **2025-05-16** - **MCDT-1859**: feat(wording): Update wording
- **2025-05-14** - **MCDT-1675**: feat(origin-id): Updatr css and mapping
- **2025-05-12** - **MCDT-1495**: fix: Update UI
- **2025-05-06** - **MCDT-1000**: feat(css): Update
- **2025-05-02** - **MCDT-882**: feat(api): Integrate API
- **2025-04-24** - **MCDT-863**: feat(event): Update feedback#2
- **2025-04-23** - **MCDT-860**: feat(receiving): Implement Event Detail UI - Init Route - Implement Wordings - Implement Components - Implement Services - Implement Hook - Integrate API

### 🐛 Bug Fixes
- **2025-07-09** - **MCDT-4123**: fix: mcdt-4123 fix cannot load shipment name (#506)
- **2025-07-09** - **MCDT-3959**: Merge branch 'main' into fix/mcdt-3959
- **2025-07-08** - **MCDT-3954**: Merge branch 'main' into fix/mcdt-3954
- **2025-07-08** - **MCDT-3907**: fix: mcdt-3907 improve vehicle province autocomplete handling
- **2025-07-08** - **MCDT-3905**: fix(cutter): mcdt-3950 implement Thai phone number validation
- **2025-07-04** - **MCDT-3822**: fix: mcdt-3833 update warning message for early harvest in Thai and English translations
- **2025-07-04** - **MCDT-3437**: fix: fix bug variety and receive
- **2025-07-04** - **MCDT-3436**: Merge branch 'main' into fix/mcdt-3436
- **2025-07-02** - **MCDT-3723**: fix: make ownerName and ownerPhone optional in PackingHouseInfo type
- **2025-07-02** - **MCDT-3575**: feat: add filter and reload icons, update FilterTable component to use new icons
- **2025-07-02** - **MCDT-3610**: Merge branch 'main' into fix/MCDT-3610-wording
- **2025-07-01** - **MCDT-3609**: fix: trim whitespace for save button enable conditions
- **2025-07-01** - **MCDT-3098**: Merge branch 'main' into fix/MCDT-3098
- **2025-07-01** - **MCDT-3570**: fix: remove unused farm name column from event list
- **2025-06-30** - **MCDT-2436**: fix: add optional chaining for avatar filename processing in CreateEvent component
- **2025-06-10** - **MCDT-1825**: refactor: streamline filter table component and implement debounce in search input
- **2025-06-04** - **MCDT-1607**: fix: update terminology for pagination and privacy notice links in multiple locales
- **2025-06-04** - **MCDT-1830**: fix: add email contact for cookie policy inquiries
- **2025-06-04** - **MCDT-2430**: fix: update OTP error message for better clarity
- **2025-06-03** - **MCDT-2393**: refactor: improve scroll end detection logic in PDPA component
- **2025-06-03** - **MCDT-2421**: add success message for temporary variety updates in English and Thai locales
- **2025-05-30** - **MCDT-1747**: Update logic
- **2025-05-30** - **MCDT-2357**: fix: decimal input weight
- **2025-05-30** - **MCDT-2344**: fix: Number input
- **2025-05-27** - **MCDT-2272**: Merge branch 'main' into fix/MCDT-2272
- **2025-05-27** - **MCDT-2196**: fix: Update hyperlink
- **2025-05-27** - **MCDT-2270**: Merge branch 'main' into fix/MCDT-2270
- **2025-05-27** - **MCDT-2034**: fix: Update padding
- **2025-05-27** - **MCDT-2092**: fix
- **2025-05-26** - **MCDT-2114**: Merge branch 'main' into fix/MCDT-2114
- **2025-05-26** - **MCDT-2115**: Merge branch 'master' into fix/MCDT-2115
- **2025-05-26** - **MCDT-2098**: Merge branch 'master' into fix/MCDT-2098
- **2025-05-26** - **MCDT-2117**: fix: update isReceive and waiting change logic
- **2025-05-26** - **MCDT-2111**: fix: Update image, error and wording
- **2025-05-23** - **MCDT-2159**: fix: Update code
- **2025-05-22** - **MCDT-1975**: fix: Update bug
- **2025-05-22** - **MCDT-1721**: fix: Update draft disabled seal and validate
- **2025-05-21** - **MCDT-1568**: fix: Update Reminder Popup UI
- **2025-05-20** - **MCDT-1696**: fix: Rename
- **2025-05-20** - **MCDT-1733**: fix: Add loading
- **2025-05-20** - **MCDT-1582**: fix: Update layout drawer and seal shipment
- **2025-05-19** - **MCDT-1888**: fix: Optimize Filter
- **2025-05-19** - **MCDT-1672**: fix(MCDT-1762): Fix Bug
- **2025-05-16** - **MCDT-1836**: fix/MCDT-1836
- **2025-05-15** - **MCDT-1768**: fix: Fix bug MCDT-1768
- **2025-05-14** - **MCDT-1762**: fix(MCDT-1762)
- **2025-05-13** - **MCDT-1579**: fix(toast): Custom Toast
