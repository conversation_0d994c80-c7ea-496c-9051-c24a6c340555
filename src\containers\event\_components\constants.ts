import { getCookieLocale } from 'utils/cookie-client';

const rejectReasonOptionsThai = [
  'ทุเรียนเก็บเกี่ยวเร็วเกินไป',
  'ทุเรียนสุกเกินไป',
  'ทุเรียนมีโรค (จุดดำ รา หรืออาการเน่าเสีย)',
  'ทุเรียนมีปัญหาแมลงศัตรูพืช (รอยเจาะหรือความเสียหายจากหนอน/แมลง)',
  'น้ำหนักที่เก็บเกี่ยวและน้ำหนักที่จัดส่งต่างกันเกิน 3%',
  'ทุเรียนไม่มีหมายเลข GAP ที่ถูกต้อง',
  'ทุเรียนโดนแดดหรือฝนนานเกินไป',
  'ทุเรียนหลังจากเก็บเกี่ยวมาถึงช้าเกินไป ทำให้ไม่สดแล้ว',
  'ข้อมูลการเก็บเกี่ยว (เช่น วันที่หรือปริมาณ) ไม่ถูกต้อง',
  'อื่นๆ',
];

const rejectReasonOptionsEnglish = [
  'Durians are harvested too early.',
  'Durians are overripe.',
  'Durians have diseases (black spots, mold, or signs of rot).',
  'Durians have pest problems (holes/ damage from insects/worms).',
  'The harvested weight and delivery weight differ by more than 3%.',
  'Durian does not have the correct GAP number.',
  'Durians were exposed to sun or rain for too long.',
  'Durians arrived too long after harvest and are no longer fresh.',
  'Harvest information (e.g., date or quantity) is incorrect.',
  'Other',
];

const locale = getCookieLocale();

export const isOtherRejectReason = (value?: string) => {
  if (!value) {
    return false;
  }

  return value === (locale === 'th' ? rejectReasonOptionsThai[9] : rejectReasonOptionsEnglish[9]);
};

export const rejectReasonOptions = locale === 'th' ? rejectReasonOptionsThai : rejectReasonOptionsEnglish;
