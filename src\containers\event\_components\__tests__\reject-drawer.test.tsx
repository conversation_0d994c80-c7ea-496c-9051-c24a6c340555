/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-require-imports */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { RejectDrawer } from '../reject-drawer';
import { theme } from 'styles/theme';
import { PackingHouseDetail } from 'types';

// Mock dependencies
jest.mock('hooks/mutates/useRejectHarvestMutate', () => ({
  useRejectHarvestMutate: jest.fn(() => ({
    mutateAsync: jest.fn(),
    isPending: false,
  })),
}));

jest.mock('next-intl', () => ({
  useTranslations: jest.fn((namespace: string) => (key: string) => {
    const translations: Record<string, Record<string, string>> = {
      receive: {
        'reject-drawer-title': 'Reject Batch',
        'reject-drawer-content': 'Please select a reason for rejecting this batch',
        'reject-reason-error': 'Please select a reject reason',
        'reject-drawer-reason-placeholder': 'Please specify other reason...',
      },
      common: {
        submit: 'Submit',
      },
    };
    return translations[namespace]?.[key] || key;
  }),
}));

jest.mock('react-hook-form', () => {
  const actual = jest.requireActual('react-hook-form');
  return {
    ...actual,
    useForm: jest.fn(() => ({
      control: {},
      handleSubmit: jest.fn((fn) => fn),
      reset: jest.fn(),
      watch: jest.fn((field: string) => {
        if (field === 'rejectReason') return '';
        if (field === 'customReason') return '';
        return '';
      }),
      setValue: jest.fn(),
      setError: jest.fn(),
      formState: { errors: {} },
    })),
  };
});

jest.mock('components', () => ({
  Drawer: ({
    children,
    open,
    onClose,
    drawerTitle,
    hasActionBtn,
    onConfirm,
    confirmButtonText,
    isDisableConfirm,
    isLoading,
  }: any) => (
    <div
      data-testid="drawer"
      data-open={open}
      data-title={drawerTitle}
      data-has-action-btn={hasActionBtn}
      data-is-disable-confirm={isDisableConfirm}
      data-is-loading={isLoading}
    >
      {open && (
        <div>
          <div data-testid="drawer-title">{drawerTitle}</div>
          <div data-testid="drawer-content">{children}</div>
          <button data-testid="drawer-close" onClick={onClose}>
            Close
          </button>
          <button data-testid="drawer-confirm" onClick={onConfirm} disabled={isDisableConfirm}>
            {confirmButtonText}
          </button>
        </div>
      )}
    </div>
  ),
}));

jest.mock('components/hook-form', () => ({
  FormRadioGroup: ({ name, required, requiredMessage, options }: any) => (
    <div data-testid="form-radio-group">
      <div data-testid="radio-group-name">{name}</div>
      <div data-testid="radio-group-required">{required ? 'true' : 'false'}</div>
      <div data-testid="radio-group-required-message">{requiredMessage}</div>
      {options?.map((option: any, index: number) => (
        <label key={index} data-testid={`radio-option-${index}`}>
          <input type="radio" name={name} value={option.value} data-testid={`radio-input-${index}`} />
          {option.label}
        </label>
      ))}
    </div>
  ),
  FormTextInputArea: ({ name, required, placeholder, maxLength, minRows }: any) => (
    <div data-testid="form-text-input-area">
      <textarea
        data-testid="custom-reason-textarea"
        name={name}
        placeholder={placeholder}
        maxLength={maxLength}
        rows={minRows}
        required={required}
      />
      <div data-testid="textarea-max-length">{maxLength}</div>
    </div>
  ),
}));

jest.mock('../constants', () => ({
  isOtherRejectReason: jest.fn((value?: string) => value === 'Other'),
  rejectReasonOptions: [
    'Durians are harvested too early.',
    'Durians are overripe.',
    'Durians have diseases (black spots, mold, or signs of rot).',
    'Other',
  ],
}));

jest.mock('utils/posthog', () => ({
  capturePosthog: jest.fn(),
}));

jest.mock('utils/gtag', () => ({
  sendEvent: jest.fn(),
}));

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider theme={theme}>{children}</ThemeProvider>
);

describe('RejectDrawer', () => {
  const mockToggle = jest.fn();
  const mockMutateAsync = jest.fn();
  const mockReset = jest.fn();
  const mockSetValue = jest.fn();
  const mockSetError = jest.fn();
  const mockWatch = jest.fn();
  const mockHandleSubmit = jest.fn();

  const mockData: PackingHouseDetail = {
    id: 'test-id-123',
    batchlot: 'BATCH-001',
    status: 'waiting',
    type: 'receiving',
    name: 'Test Batch',
    description: 'Test Description',
    address: 'Test Address',
    country: 'Thailand',
    dateCreated: '2023-12-25T10:00:00Z',
    images: [],
    logo: { id: '1', filenameDisk: 'logo.jpg', filenameDownload: 'logo.jpg' },
    farm: {
      address: 'Farm Address',
      id: 'farm-1',
      name: 'Test Farm',
    },
    gln: '1234567890123',
    positionLongitude: 100.5,
    positionLatitude: 13.7,
    role: 'packing_house_staff',
    packingHouse: {} as any,
    userCreated: {
      id: 'user-1',
      firstName: 'John',
      lastName: 'Doe',
      role: 'farmer',
    } as any,
    packing: [],
    meta: {} as any,
    varieties: [],
    immutable: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock scrollIntoView for DOM elements
    Element.prototype.scrollIntoView = jest.fn();

    // Setup default mocks
    const { useRejectHarvestMutate } = require('hooks/mutates/useRejectHarvestMutate');
    useRejectHarvestMutate.mockReturnValue({
      mutateAsync: mockMutateAsync,
      isPending: false,
    });

    const { useForm } = require('react-hook-form');
    useForm.mockReturnValue({
      control: {},
      handleSubmit: mockHandleSubmit.mockImplementation((fn) => fn),
      reset: mockReset,
      watch: mockWatch,
      setValue: mockSetValue,
      setError: mockSetError,
      formState: { errors: {} },
    });

    mockWatch.mockImplementation((field: string) => {
      if (field === 'rejectReason') return '';
      if (field === 'customReason') return '';
      return '';
    });
  });

  it('should render drawer with correct title when open', () => {
    render(
      <TestWrapper>
        <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
      </TestWrapper>
    );

    expect(screen.getByTestId('drawer')).toHaveAttribute('data-open', 'true');
    expect(screen.getByTestId('drawer-title')).toHaveTextContent('Reject Batch BATCH-001');
    expect(screen.getByTestId('drawer')).toHaveAttribute('data-has-action-btn', 'true');
  });

  it('should not render drawer content when closed', () => {
    render(
      <TestWrapper>
        <RejectDrawer data={mockData} open={false} toggle={mockToggle} />
      </TestWrapper>
    );

    expect(screen.getByTestId('drawer')).toHaveAttribute('data-open', 'false');
    expect(screen.queryByTestId('drawer-title')).not.toBeInTheDocument();
  });

  it('should call toggle with false when close button is clicked', () => {
    render(
      <TestWrapper>
        <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
      </TestWrapper>
    );

    const closeButton = screen.getByTestId('drawer-close');
    fireEvent.click(closeButton);

    expect(mockToggle).toHaveBeenCalledWith(false);
    expect(mockReset).toHaveBeenCalled();
  });

  it('should display drawer content and form elements', () => {
    render(
      <TestWrapper>
        <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
      </TestWrapper>
    );

    expect(screen.getByText('Please select a reason for rejecting this batch')).toBeInTheDocument();
    expect(screen.getByTestId('form-radio-group')).toBeInTheDocument();
    expect(screen.getByTestId('radio-group-name')).toHaveTextContent('rejectReason');
    expect(screen.getByTestId('radio-group-required')).toHaveTextContent('true');
  });

  it('should render radio options correctly', () => {
    render(
      <TestWrapper>
        <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
      </TestWrapper>
    );

    expect(screen.getByTestId('radio-option-0')).toHaveTextContent('Durians are harvested too early.');
    expect(screen.getByTestId('radio-option-1')).toHaveTextContent('Durians are overripe.');
    expect(screen.getByTestId('radio-option-2')).toHaveTextContent(
      'Durians have diseases (black spots, mold, or signs of rot).'
    );
    expect(screen.getByTestId('radio-option-3')).toHaveTextContent('Other');
  });

  it('should show custom reason textarea when "Other" is selected', () => {
    // Mock "Other" being selected
    mockWatch.mockImplementation((field: string) => {
      if (field === 'rejectReason') return 'Other';
      if (field === 'customReason') return '';
      return '';
    });

    render(
      <TestWrapper>
        <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
      </TestWrapper>
    );

    expect(screen.getByTestId('form-text-input-area')).toBeInTheDocument();
    expect(screen.getByTestId('custom-reason-textarea')).toBeInTheDocument();
    expect(screen.getByTestId('textarea-max-length')).toHaveTextContent('500');
  });

  it('should not show custom reason textarea when "Other" is not selected', () => {
    // Mock non-"Other" option being selected
    mockWatch.mockImplementation((field: string) => {
      if (field === 'rejectReason') return 'Durians are harvested too early.';
      if (field === 'customReason') return '';
      return '';
    });

    render(
      <TestWrapper>
        <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
      </TestWrapper>
    );

    expect(screen.queryByTestId('form-text-input-area')).not.toBeInTheDocument();
    expect(screen.queryByTestId('custom-reason-textarea')).not.toBeInTheDocument();
  });

  it('should display character count when custom reason is provided', () => {
    // Mock "Other" being selected with custom reason
    mockWatch.mockImplementation((field: string) => {
      if (field === 'rejectReason') return 'Other';
      if (field === 'customReason') return 'Custom reject reason';
      return '';
    });

    render(
      <TestWrapper>
        <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
      </TestWrapper>
    );

    // The character count should be displayed
    expect(screen.getByText('20/500')).toBeInTheDocument();
  });

  it('should disable confirm button when no reason is selected', () => {
    // Mock no reason selected
    mockWatch.mockImplementation((field: string) => {
      if (field === 'rejectReason') return '';
      if (field === 'customReason') return '';
      return '';
    });

    render(
      <TestWrapper>
        <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
      </TestWrapper>
    );

    expect(screen.getByTestId('drawer')).toHaveAttribute('data-is-disable-confirm', 'true');
  });

  it('should enable confirm button when reason is selected', () => {
    // Mock reason selected
    mockWatch.mockImplementation((field: string) => {
      if (field === 'rejectReason') return 'Durians are harvested too early.';
      if (field === 'customReason') return '';
      return '';
    });

    render(
      <TestWrapper>
        <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
      </TestWrapper>
    );

    expect(screen.getByTestId('drawer')).toHaveAttribute('data-is-disable-confirm', 'false');
  });

  it('should enable confirm button when "Other" is selected with custom reason', () => {
    // Mock "Other" selected with custom reason
    mockWatch.mockImplementation((field: string) => {
      if (field === 'rejectReason') return 'Other';
      if (field === 'customReason') return 'Custom reject reason';
      return '';
    });

    render(
      <TestWrapper>
        <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
      </TestWrapper>
    );

    expect(screen.getByTestId('drawer')).toHaveAttribute('data-is-disable-confirm', 'false');
  });

  it('should show loading state when mutation is pending', () => {
    const { useRejectHarvestMutate } = require('hooks/mutates/useRejectHarvestMutate');
    useRejectHarvestMutate.mockReturnValue({
      mutateAsync: mockMutateAsync,
      isPending: true,
    });

    render(
      <TestWrapper>
        <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
      </TestWrapper>
    );

    expect(screen.getByTestId('drawer')).toHaveAttribute('data-is-loading', 'true');
  });

  it('should call setValue to clear custom reason when "Other" is deselected', () => {
    // Mock "Other" not being selected
    mockWatch.mockImplementation((field: string) => {
      if (field === 'rejectReason') return 'Durians are harvested too early.';
      if (field === 'customReason') return '';
      return '';
    });

    render(
      <TestWrapper>
        <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
      </TestWrapper>
    );

    // setValue should be called to clear customReason when "Other" is not selected
    expect(mockSetValue).toHaveBeenCalledWith('customReason', '');
  });

  describe('Form Submission', () => {
    it('should handle submit when data is not provided', () => {
      render(
        <TestWrapper>
          <RejectDrawer data={null as any} open={true} toggle={mockToggle} />
        </TestWrapper>
      );

      // The component should still render even with null data
      expect(screen.getByTestId('drawer')).toBeInTheDocument();
      expect(screen.getByTestId('drawer-title')).toHaveTextContent('Reject Batch undefined');
    });

    it('should handle submit when no reason is provided', () => {
      // Mock no reason selected
      mockWatch.mockImplementation((field: string) => {
        if (field === 'rejectReason') return '';
        if (field === 'customReason') return '';
        return '';
      });

      render(
        <TestWrapper>
          <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
        </TestWrapper>
      );

      // The confirm button should be disabled when no reason is selected
      expect(screen.getByTestId('drawer')).toHaveAttribute('data-is-disable-confirm', 'true');
    });

    it('should submit with regular reason when non-Other option is selected', async () => {
      const selectedReason = 'Durians are harvested too early.';

      // Mock reason selected
      mockWatch.mockImplementation((field: string) => {
        if (field === 'rejectReason') return selectedReason;
        if (field === 'customReason') return '';
        return '';
      });

      mockHandleSubmit.mockImplementation((fn) => () => fn());
      mockMutateAsync.mockResolvedValue(undefined);

      const { sendEvent } = require('utils/gtag');

      render(
        <TestWrapper>
          <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
        </TestWrapper>
      );

      const confirmButton = screen.getByTestId('drawer-confirm');
      fireEvent.click(confirmButton);

      await waitFor(() => {
        expect(sendEvent).toHaveBeenCalledWith('reject_incomning_harvest');
        expect(mockMutateAsync).toHaveBeenCalledWith({
          productId: 'test-id-123',
          reason: selectedReason,
        });
      });
    });

    it('should submit with custom reason when "Other" is selected', async () => {
      const customReason = 'Custom reject reason for testing';

      // Mock "Other" selected with custom reason
      mockWatch.mockImplementation((field: string) => {
        if (field === 'rejectReason') return 'Other';
        if (field === 'customReason') return customReason;
        return '';
      });

      mockHandleSubmit.mockImplementation((fn) => () => fn());
      mockMutateAsync.mockResolvedValue(undefined);

      const { sendEvent } = require('utils/gtag');

      render(
        <TestWrapper>
          <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
        </TestWrapper>
      );

      const confirmButton = screen.getByTestId('drawer-confirm');
      fireEvent.click(confirmButton);

      await waitFor(() => {
        expect(sendEvent).toHaveBeenCalledWith('reject_incomning_harvest');
        expect(mockMutateAsync).toHaveBeenCalledWith({
          productId: 'test-id-123',
          reason: customReason,
        });
      });
    });

    it('should disable confirm button when "Other" is selected but no custom reason is provided', () => {
      // Mock "Other" selected but no custom reason
      mockWatch.mockImplementation((field: string) => {
        if (field === 'rejectReason') return 'Other';
        if (field === 'customReason') return '';
        return '';
      });

      render(
        <TestWrapper>
          <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
        </TestWrapper>
      );

      // The confirm button should be disabled when "Other" is selected but no custom reason
      expect(screen.getByTestId('drawer')).toHaveAttribute('data-is-disable-confirm', 'true');
    });
  });

  describe('Component Behavior', () => {
    it('should display correct confirm button text', () => {
      render(
        <TestWrapper>
          <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
        </TestWrapper>
      );

      expect(screen.getByTestId('drawer-confirm')).toHaveTextContent('Submit');
    });

    it('should handle form submission through form onSubmit', () => {
      mockHandleSubmit.mockImplementation((fn) => fn);

      render(
        <TestWrapper>
          <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
        </TestWrapper>
      );

      // The form should be present in the DOM
      const form = screen.getByTestId('drawer-content').querySelector('form');
      expect(form).toBeInTheDocument();
    });

    it('should display character count as 0 when no custom reason is provided', () => {
      // Mock "Other" selected but no custom reason
      mockWatch.mockImplementation((field: string) => {
        if (field === 'rejectReason') return 'Other';
        if (field === 'customReason') return '';
        return '';
      });

      render(
        <TestWrapper>
          <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
        </TestWrapper>
      );

      expect(screen.getByText('0/500')).toBeInTheDocument();
    });

    it('should handle undefined custom reason length', () => {
      // Mock "Other" selected with undefined custom reason
      mockWatch.mockImplementation((field: string) => {
        if (field === 'rejectReason') return 'Other';
        if (field === 'customReason') return undefined;
        return '';
      });

      render(
        <TestWrapper>
          <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
        </TestWrapper>
      );

      expect(screen.getByText('0/500')).toBeInTheDocument();
    });

    it('should call isOtherRejectReason with watched reject reason', () => {
      const { isOtherRejectReason } = require('../constants');
      const testReason = 'Test reason';

      mockWatch.mockImplementation((field: string) => {
        if (field === 'rejectReason') return testReason;
        if (field === 'customReason') return '';
        return '';
      });

      render(
        <TestWrapper>
          <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
        </TestWrapper>
      );

      expect(isOtherRejectReason).toHaveBeenCalledWith(testReason);
    });

    it('should reset form and close drawer on close', () => {
      render(
        <TestWrapper>
          <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
        </TestWrapper>
      );

      const closeButton = screen.getByTestId('drawer-close');
      fireEvent.click(closeButton);

      expect(mockReset).toHaveBeenCalled();
      expect(mockToggle).toHaveBeenCalledWith(false);
    });

    it('should enable confirm button when reason is selected for error handling test', () => {
      const selectedReason = 'Durians are harvested too early.';

      mockWatch.mockImplementation((field: string) => {
        if (field === 'rejectReason') return selectedReason;
        if (field === 'customReason') return '';
        return '';
      });

      render(
        <TestWrapper>
          <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
        </TestWrapper>
      );

      // The confirm button should be enabled when a reason is selected
      expect(screen.getByTestId('drawer')).toHaveAttribute('data-is-disable-confirm', 'false');
    });
  });

  describe('Constants Integration', () => {
    it('should use rejectReasonOptions from constants', () => {
      const { rejectReasonOptions } = require('../constants');

      render(
        <TestWrapper>
          <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
        </TestWrapper>
      );

      // Should render all options from constants
      rejectReasonOptions.forEach((option: string, index: number) => {
        expect(screen.getByTestId(`radio-option-${index}`)).toHaveTextContent(option);
      });
    });

    it('should call isOtherRejectReason function correctly', () => {
      const { isOtherRejectReason } = require('../constants');

      render(
        <TestWrapper>
          <RejectDrawer data={mockData} open={true} toggle={mockToggle} />
        </TestWrapper>
      );

      expect(isOtherRejectReason).toHaveBeenCalled();
    });
  });
});
