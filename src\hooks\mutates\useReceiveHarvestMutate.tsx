import { useMutation, useQueryClient } from '@tanstack/react-query';
import { receivingHarvest } from 'services/event.service';
import { useRouter } from 'next/navigation';
import { clientRoutes } from 'routes/client-routes';
import { queryKeys } from 'hooks/queries/_key';
import { ReceivingBatchLotRequest } from 'types';
import { useToastStore } from 'store/useToastStore';
import { useTranslations } from 'next-intl';
import toastMessages from 'utils/toastMessages';

export function useReceiveHarvestMutate() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const setToast = useToastStore((state) => state.setToast);
  const receivingTranslation = useTranslations('receive');
  const commonTranslation = useTranslations('common');

  return useMutation({
    mutationFn: (args: ReceivingBatchLotRequest) => receivingHarvest(args),
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: [queryKeys.EVENT, queryKeys.HARVEST, queryKeys.RECEIVING],
      });

      setToast({
        message: receivingTranslation('batch-received-success'),
        type: 'success',
      });

      router.push(clientRoutes.eventIncoming);
    },
    onError: () => {
      toastMessages.error(commonTranslation('data-outdated-error'));
    },
  });
}
