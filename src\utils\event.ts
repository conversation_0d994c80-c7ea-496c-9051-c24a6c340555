/* eslint-disable @typescript-eslint/no-explicit-any */
import { flatMap } from 'lodash-es';
import { DurianGrade, DurianVariety, PackingHouse, ReceiveUpdatePayload } from 'types';

export function calculateTotalWeight(item?: PackingHouse | null): number {
  if (!item?.varieties.length) {
    return 0;
  }

  const grades = flatMap(item.varieties, (it) => it.grades) ?? [];

  if (!grades?.length) return 0;

  return grades.reduce((sum: number, grade) => {
    return sum + (Number(grade.weight) || 0);
  }, 0);
}

export function convertResponseToWeights(responseData?: DurianVariety[]): Record<string, string> {
  const weights: Record<string, string> = {};

  if (!responseData || !Array.isArray(responseData)) {
    return weights;
  }

  // Handle the new data structure
  responseData.forEach((variety) => {
    if (variety?.id && variety?.grades) {
      // For each grade in the variety
      variety.grades.forEach((grade) => {
        if (grade?.id) {
          // Create a unique key that includes the flower_blooming_day
          const key = `${variety.id}-${grade.id}`;
          weights[key] = grade.weight?.toString() || '0';
        }
      });
    }
  });

  return weights;
}

interface ComparisonResult {
  hasChanges: boolean;
  changedVarietyIds: string[];
}

export function compareVarieties(
  originalVarieties: DurianVariety[],
  updatedVarieties: DurianVariety[]
): ComparisonResult {
  const result: ComparisonResult = {
    hasChanges: false,
    changedVarietyIds: [],
  };

  if (originalVarieties.length !== updatedVarieties.length) {
    result.hasChanges = true;
    result.changedVarietyIds = updatedVarieties.map((variety) => variety.id);
    return result;
  }

  const originalVarietiesMap = new Map();
  originalVarieties.forEach((variety) => {
    const key = `${variety.id}`;

    const gradesMap = new Map();
    if (variety.grades && Array.isArray(variety.grades)) {
      variety.grades.forEach((grade: DurianGrade) => {
        gradesMap.set(grade.id, grade);
      });
    }

    originalVarietiesMap.set(key, {
      variety,
      gradesMap,
    });
  });

  // Compare each updated variety with its original counterpart
  updatedVarieties.forEach((updatedVariety) => {
    const key = `${updatedVariety.id}`;
    const originalData = originalVarietiesMap.get(key);

    // If the variety doesn't exist in the original data, it's a change
    if (!originalData) {
      result.hasChanges = true;
      result.changedVarietyIds.push(updatedVariety.id);
      return;
    }

    const originalVariety = originalData.variety;
    const originalGradesMap = originalData.gradesMap;
    // Check if the number of grades has changed
    if (
      !updatedVariety.grades ||
      !Array.isArray(updatedVariety.grades) ||
      (originalVariety.grades &&
        Array.isArray(originalVariety.grades) &&
        updatedVariety.grades.length !== originalVariety.grades.length)
    ) {
      result.hasChanges = true;
      result.changedVarietyIds.push(updatedVariety.id);
      return;
    }

    // Compare each grade
    let gradesChanged = false;
    updatedVariety.grades.forEach((updatedGrade: DurianGrade) => {
      const originalGrade = originalGradesMap.get(updatedGrade.id);

      // If the grade doesn't exist in the original data, it's a change
      if (!originalGrade) {
        gradesChanged = true;
        return;
      }

      // Check if the weight has changed
      // Convert to numbers for comparison to handle string vs number differences
      if (Number(updatedGrade.weight) !== Number(originalGrade.weight)) {
        gradesChanged = true;
      }
    });

    if (gradesChanged) {
      result.hasChanges = true;
      result.changedVarietyIds.push(updatedVariety.id);
    }
  });

  return result;
}

export const convertModalDataToVarieties = (modalData: ReceiveUpdatePayload, originalData: DurianVariety[]) => {
  if (!modalData?.update?.varieties) {
    return [];
  }

  return modalData.update.varieties.map((variety: any) => {
    // Create a base variety object
    const baseVariety = {
      id: variety.id,
      value: originalData.find((v) => v.id === variety.id)?.value ?? 'unknown',
      label: originalData.find((v) => v.id === variety.id)?.label || { th: 'Unknown', en: 'Unknown' },
      flowerBloomingDay: variety.flowerBloomingDay,
      grades: [] as any[],
      name: variety.name,
    };

    if (variety.grades && Array.isArray(variety.grades)) {
      baseVariety.grades = variety.grades.map((grade: any) => {
        const varietyFromMock = originalData.find((v) => v.id === variety.id);
        const gradeFromMock = varietyFromMock?.grades.find((g) => g.id === grade.id);

        return {
          id: grade.id,
          value: gradeFromMock?.value ?? 'unknown',
          label: gradeFromMock?.label || { th: 'Unknown Grade', en: 'Unknown Grade' },
          weight: grade.weight,
        };
      });
    }

    return baseVariety;
  });
};

export function transformDurianDataEvent(varieties: DurianVariety[]) {
  if (!varieties || !Array.isArray(varieties)) {
    return [];
  }

  const transformedVarieties = varieties.map((variety) => {
    const displayText = variety.label?.th ?? variety.label?.en ?? 'Unknown';
    const grades = (variety.grades ?? []).map((grade: any) => ({
      displayText: grade.label?.th ?? grade.label?.en ?? 'Unknown Grade',
      weight: grade.weight?.toString() ?? '0',
      id: grade.id,
    }));

    return {
      id: variety.id,
      displayText,
      value: variety.value,
      flowerBloomingDay: variety.flowerBloomingDay,
      grades,
      name: variety?.name,
    };
  });

  return transformedVarieties;
}

export function reorderToOriginalOrder(original: DurianVariety[], transformed: any[]) {
  const transformedMap = new Map<string, any>();
  transformed.forEach((item) => {
    transformedMap.set(item.id, item);
  });

  return original.map(({ id }) => transformedMap.get(id)!).filter(Boolean);
}

export function transformArray(inputArray: DurianVariety[]) {
  return inputArray.map((item) => ({
    id: item.id,
    grades: item.grades.map((grade) => {
      const transformedGrade: {
        id: string;
        weight: number;
        name?: string;
      } = {
        id: grade.id,
        weight: grade.weight,
      };
      if (grade.name) {
        transformedGrade.name = grade.name;
      }
      return transformedGrade;
    }),
    flowerBloomingDay: item.flowerBloomingDay,
    name: item.name,
  }));
}
