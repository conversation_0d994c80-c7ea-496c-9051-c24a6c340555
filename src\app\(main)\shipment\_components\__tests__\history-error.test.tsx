import { render, screen, fireEvent } from '@testing-library/react';
import { useTranslations } from 'next-intl';
import { HistoryError } from '../history-error';

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: jest.fn(),
}));

const mockUseTranslations = useTranslations as jest.MockedFunction<typeof useTranslations>;

describe('HistoryError', () => {
  const mockCommonT = jest.fn();
  const mockShipmentT = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockCommonT.mockImplementation((key: string) => {
      const translations: Record<string, string> = {
        'common-error': 'Something went wrong, please try again later',
        'retry': 'Retry',
      };
      return translations[key] || key;
    });
    mockShipmentT.mockImplementation((key: string) => {
      const translations: Record<string, string> = {
        'history-error-title': 'Failed to load history',
      };
      return translations[key] || key;
    });
    mockUseTranslations.mockImplementation((namespace: string) => {
      return namespace === 'common' ? mockCommonT : mockShipmentT;
    });
  });

  it('renders error title and default message', () => {
    render(<HistoryError />);
    
    expect(screen.getByText('Failed to load history')).toBeInTheDocument();
    expect(screen.getByText('Something went wrong, please try again later')).toBeInTheDocument();
  });

  it('renders custom error message when provided', () => {
    const customError = 'Network connection failed';
    render(<HistoryError error={customError} />);
    
    expect(screen.getByText('Failed to load history')).toBeInTheDocument();
    expect(screen.getByText(customError)).toBeInTheDocument();
  });

  it('renders retry button when onRetry is provided', () => {
    const mockOnRetry = jest.fn();
    render(<HistoryError onRetry={mockOnRetry} />);
    
    const retryButton = screen.getByText('Retry');
    expect(retryButton).toBeInTheDocument();
    expect(retryButton.closest('button')).toBeInTheDocument();
  });

  it('does not render retry button when onRetry is not provided', () => {
    render(<HistoryError />);
    
    expect(screen.queryByText('Retry')).not.toBeInTheDocument();
  });

  it('calls onRetry when retry button is clicked', () => {
    const mockOnRetry = jest.fn();
    render(<HistoryError onRetry={mockOnRetry} />);
    
    const retryButton = screen.getByText('Retry');
    fireEvent.click(retryButton);
    
    expect(mockOnRetry).toHaveBeenCalledTimes(1);
  });

  it('renders error icon', () => {
    render(<HistoryError />);
    
    // Check for error icon (Material-UI icons are rendered as SVGs)
    const errorIcon = document.querySelector('svg[data-testid="ErrorOutlineIcon"]');
    expect(errorIcon || document.querySelector('svg')).toBeInTheDocument();
  });

  it('renders with proper container styling', () => {
    const { container } = render(<HistoryError />);
    
    const errorContainer = container.firstChild as HTMLElement;
    expect(errorContainer).toHaveStyle({
      backgroundColor: '#FFFFFF',
      borderRadius: '8px',
      border: '1px solid #e5e5ea',
      padding: '32px',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '300px',
    });
  });

  it('uses correct translation namespaces', () => {
    render(<HistoryError />);
    
    expect(mockUseTranslations).toHaveBeenCalledWith('common');
    expect(mockUseTranslations).toHaveBeenCalledWith('shipment');
  });

  it('calls correct translation keys', () => {
    render(<HistoryError />);
    
    expect(mockCommonT).toHaveBeenCalledWith('common-error');
    expect(mockShipmentT).toHaveBeenCalledWith('history-error-title');
  });

  it('calls retry translation key when retry button is shown', () => {
    const mockOnRetry = jest.fn();
    render(<HistoryError onRetry={mockOnRetry} />);
    
    expect(mockCommonT).toHaveBeenCalledWith('retry');
  });

  it('handles both custom error and retry together', () => {
    const mockOnRetry = jest.fn();
    const customError = 'API timeout error';
    
    render(<HistoryError error={customError} onRetry={mockOnRetry} />);
    
    expect(screen.getByText('Failed to load history')).toBeInTheDocument();
    expect(screen.getByText(customError)).toBeInTheDocument();
    expect(screen.getByText('Retry')).toBeInTheDocument();
    
    fireEvent.click(screen.getByText('Retry'));
    expect(mockOnRetry).toHaveBeenCalledTimes(1);
  });
});
