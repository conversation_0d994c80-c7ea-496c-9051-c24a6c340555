import { Autocomplete, Box, IconButton, TextField, Typography } from '@mui/material';
import { ImageReviewModal, TextInput } from 'components';
import { CutterVehicle, Province } from 'types';
import { FC, useMemo } from 'react';
import UploadImage from './upload-images';
import { useTranslations } from 'next-intl';
import { useQuery } from '@tanstack/react-query';
import { fetchProvinceService } from 'services/resource.service';
import { getCookieLocale } from 'utils/cookie-client';
import { getImageUrl } from 'utils';
import { ClearRounded } from '@mui/icons-material';

interface CreateCutterVehicleProps {
  vehicle: CutterVehicle;
  setVehicle: (vehicle: CutterVehicle) => void;
}

export const CreateCutterVehicle: FC<CreateCutterVehicleProps> = ({ vehicle, setVehicle }) => {
  const { data: provinces } = useQuery({
    queryKey: ['provinces'],
    queryFn: () => {
      return fetchProvinceService();
    },
  });

  const locale = getCookieLocale() ?? 'th';

  const provinceList = useMemo(() => {
    if (provinces?.data) {
      return provinces?.data.map((province: Province) => {
        return {
          label: province?.label?.[locale] ?? '',
          provinceVehicleCode: province.provinceVehicleCode,
        };
      });
    }
    return [];
  }, [provinces, locale]);

  const receiveTranslation = useTranslations('receive');
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <Typography fontWeight="bold" sx={{ fontSize: '18px', mb: { xs: 2, sm: 0 } }}>
        {receiveTranslation('vehicle-information')}
      </Typography>
      <Box sx={{ display: 'flex', gap: 0.25, flexDirection: 'column' }}>
        <Typography sx={{ fontSize: '18px' }} variant="caption">
          {receiveTranslation('vehicle-photo')}
        </Typography>
        <Typography sx={{ fontSize: '14px' }} variant="caption">
          {receiveTranslation('file-size-limit')}
        </Typography>

        <Box sx={{ display: 'flex', gap: '12px' }}>
          {vehicle.image?.filenameDisk ? (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, position: 'relative' }}>
              <ImageReviewModal imageUrl={getImageUrl(vehicle?.image?.filenameDisk) ?? null} imageSize={120} />
              <IconButton
                size="small"
                sx={{ position: 'absolute', right: 0, top: 0, p: 0 }}
                color="error"
                onClick={() => {
                  setVehicle({
                    ...vehicle,
                    image: {
                      ...vehicle.image,
                      filenameDisk: '',
                      id: '',
                      filenameDownload: '',
                    },
                  });
                }}
              >
                <ClearRounded fontSize="inherit" color="inherit" />
              </IconButton>
            </Box>
          ) : (
            <UploadImage
              onChange={(uploadImage: string) => {
                setVehicle({
                  ...vehicle,
                  image: {
                    ...vehicle.image,
                    filenameDisk: uploadImage,
                    id: vehicle?.image?.id ?? '',
                    filenameDownload: vehicle?.image?.filenameDownload ?? '',
                  },
                });
              }}
            />
          )}
        </Box>
      </Box>

      <Box sx={{ display: 'flex', gap: 0.25, flexDirection: 'column' }}>
        <Typography sx={{ fontSize: '18px' }} variant="caption">
          {receiveTranslation('vehicle-registration-number')}{' '}
          <Typography sx={{ visibility: 'visible' }} variant="caption" color="error">
            *
          </Typography>
        </Typography>

        <Box sx={{ display: 'flex', gap: '12px' }}>
          <TextInput
            required={true}
            value={vehicle.vehicleRegistrationNumber ?? ''}
            onChange={(event) => {
              const inputValue = event.target.value;
              const filteredValue = inputValue.replace(/[^a-zA-Z0-9\sก-๙]/g, '');
              setVehicle({
                ...vehicle,
                vehicleRegistrationNumber: filteredValue,
              });
            }}
            maxLength={10}
          />
        </Box>
      </Box>
      <Box sx={{ display: 'flex', gap: 0.25, flexDirection: 'column' }}>
        <Typography sx={{ fontSize: '18px' }} variant="caption">
          {receiveTranslation('province-vehicle')}{' '}
          <Typography sx={{ visibility: 'visible' }} variant="caption" color="error">
            *
          </Typography>
        </Typography>

        <Box sx={{ display: 'flex', gap: '12px' }}>
          <Autocomplete
            value={
              provinceList.filter((item) => item.provinceVehicleCode === vehicle.provinceRegistrationNumber)[0] ?? null
            }
            onChange={(event, value) => {
              setVehicle({
                ...vehicle,
                provinceRegistrationNumber: value?.provinceVehicleCode || '',
              });
            }}
            autoComplete
            sx={{ width: '100%' }}
            renderInput={(params) => (
              <TextField
                {...params}
                slotProps={{
                  input: {
                    ...params.InputProps,
                    inputProps: { ...params.inputProps, maxLength: 100 },
                  },
                }}
              />
            )}
            options={provinceList}
          />
        </Box>
      </Box>
    </Box>
  );
};
