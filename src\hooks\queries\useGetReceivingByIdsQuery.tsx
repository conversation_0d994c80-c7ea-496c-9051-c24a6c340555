'use client';

import { useQuery } from '@tanstack/react-query';
import { queryKeys } from './_key';
import { getListEventsByIdsService } from 'services/event.service';

export const useGetReceivingByIdsQuery = (ids: string[]) => {

  return useQuery({
    queryKey: [queryKeys.EVENT, ...ids],
    queryFn: () =>
      getListEventsByIdsService({
        eventType: 'receiving',
        ids,
      }),
    select: (response) => {
      return response.data;
    },
    enabled: ids.length > 0,
    gcTime: 5 * 60,
  });
};
