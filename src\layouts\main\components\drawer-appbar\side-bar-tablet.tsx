'use client';

import { Box, Drawer as MuiDrawer, styled, Typography, useTheme } from '@mui/material';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { FC, PropsWithChildren } from 'react';

import logo from 'assets/icons/side-logo-icon.svg';
import { useGlobalStore } from 'store/useGlobalStore';
import { ListMenuItems } from './list-menu-items';

import { drawerWidth, headerHeight } from '../../constant';

const Main = styled('main', {
  shouldForwardProp: (prop) => prop !== 'open',
})<{ open: boolean }>(({ theme, open }) => ({
  flexGrow: 1,
  boxSizing: 'border-box',
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: open ? theme.transitions.duration.enteringScreen : theme.transitions.duration.leavingScreen,
  }),
  width: '100vw',
}));

export const DrawerAppBarTablet: FC<PropsWithChildren & { isPDPA?: boolean }> = ({ children, isPDPA }) => {
  const theme = useTheme();
  const { isOpen, close } = useGlobalStore();
  const commonT = useTranslations('common');

  const logoSize = 36;

  return (
    <Box component="div" id="drawer-app-bar-container" sx={{ display: 'flex', width: '100%', flex: 1, height: '100vh' }}>
      {!isPDPA && (
        <MuiDrawer
          open={isOpen}
          onClose={close}
          sx={{
            width: drawerWidth,
            flexShrink: 0,
            '& .MuiDrawer-paper': {
              width: drawerWidth,
              overflowX: 'hidden',
              color: theme.palette.customColors.white,
              background: theme.palette.customColors.gradientAppBgColorNew,
            },
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 1 }}>
            <Box
              sx={{
                display: 'flex',
                gap: 1,
                alignItems: 'center',
                height: 64,
                width: '100%',
                backgroundColor: 'transparent',
                transition: theme.transitions.create(['opacity', 'width'], {
                  easing: theme.transitions.easing.easeInOut,
                  duration: theme.transitions.duration.complex,
                }),
                justifyContent: 'center',
                position: 'relative',
              }}
            >
              <Image unoptimized src={logo} width={logoSize} height={logoSize} alt="logo" />
              <Typography
                variant="body1"
                fontWeight={600}
                fontSize={18}
                color="white"
                noWrap
                sx={{
                  opacity: isOpen ? 1 : 0,
                  width: isOpen ? 'auto' : 0,
                  transition: theme.transitions.create(['opacity', 'width'], {
                    easing: theme.transitions.easing.easeInOut,
                    duration: theme.transitions.duration.complex,
                  }),
                  position: isOpen ? 'relative' : 'absolute',
                  textWrap: 'wrap',
                  color: '#BFDBFE',
                  pl: 1,
                }}
              >
                {commonT('short-app-name-e')} -
                <Typography
                  component="span"
                  sx={{
                    color: '#EFF6FF',
                  }}
                >
                  {commonT('short-app-name-trace')}
                </Typography>
              </Typography>
            </Box>
          </Box>
          <ListMenuItems isOpen={isOpen} />
        </MuiDrawer>
      )}

      <Main
        open={isOpen}
        style={{
          display: 'flex',
          flexDirection: 'column',
          marginTop: '50px'
        }}
      >
        <Box
          sx={{
            height: `calc(100vh - ${headerHeight}px)`,
            overflow: 'auto',
          }}
        >
          {children}
        </Box>
      </Main>
    </Box>
  );
};
