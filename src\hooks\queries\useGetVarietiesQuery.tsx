'use client';

import { useQuery } from '@tanstack/react-query';
import { fetchVarietiesService } from 'services/resource.service';
import { useUserStore } from 'store/useUserStore';

export const useGetVarietiesQuery = () => {
  const user = useUserStore((state) => state.user);
  return useQuery({
    queryKey: ['variety-list'],
    queryFn: () => fetchVarietiesService(),
    select: (response) => {
      return response.data;
    },
    enabled: !!user,
  });
};
