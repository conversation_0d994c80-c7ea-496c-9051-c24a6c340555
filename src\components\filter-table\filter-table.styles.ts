import { SxProps } from '@mui/material';
import { theme } from 'styles/theme';

const buttonSelectionStyle: SxProps = {
  borderRadius: '4px',
  flex: 1,
};

export const activeButtonSelectStyle: SxProps = {
  ...buttonSelectionStyle,
  color: `${theme.palette.customColors.primary}`,
  border: `1px solid ${theme.palette.customColors.primary}`,
  borderLeft: `8px solid ${theme.palette.customColors.primary}`,
  paddingLeft: `1px`,
};

export const normalButtonSelectStyle: SxProps = {
  ...buttonSelectionStyle,
  color: `${theme.palette.customColors.black}`,
  border: `1px solid ${theme.palette.customColors.gray5}`,
  borderLeft: ``,
  paddingLeft: `8px`,
};
