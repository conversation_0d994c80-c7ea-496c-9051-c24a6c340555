import { SvgIcon, SvgIconProps } from '@mui/material';
import { FC } from 'react';

export const ReceiveSolidIcon: FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon {...props}>
      <svg xmlns="http://www.w3.org/2000/svg" width={props.width} height={props.height} viewBox="0 0 20 20" fill="none">
        <g clipPath="url(#clip0_2143_2199)">
          <path
            d="M12.668 1.11133C14.8224 1.11137 16.519 1.53761 17.6582 2.67676C18.7973 3.81597 19.2236 5.51254 19.2236 7.66699V12.667C19.2236 14.8215 18.7973 16.518 17.6582 17.6572C16.519 18.7964 14.8224 19.2226 12.668 19.2227H7.66797C5.51351 19.2227 3.81694 18.7963 2.67773 17.6572C1.53853 16.518 1.11133 14.8215 1.11133 12.667V7.66699C1.11133 5.51243 1.53852 3.81597 2.67773 2.67676C3.81694 1.53766 5.5135 1.11133 7.66797 1.11133H12.668ZM5.64746 8.7998C5.40016 8.68073 5.11612 8.68952 4.87793 8.83594C4.63978 8.98249 4.50208 9.23012 4.50195 9.50488V12.5381C4.50195 13.0603 4.79491 13.5283 5.25293 13.7666L8.46094 15.3701C8.58002 15.4251 8.69928 15.4521 8.81836 15.4521C8.9648 15.4521 9.10231 15.4163 9.23047 15.334C9.46873 15.1874 9.60645 14.939 9.60645 14.6641H9.61523V11.6309C9.61507 11.1087 9.32172 10.6415 8.85449 10.4033L5.64746 8.7998ZM15.123 8.83594C14.8849 8.6894 14.6009 8.68081 14.3535 8.7998L11.1455 10.4033C10.6784 10.6324 10.3849 11.1088 10.3848 11.6309V14.6641C10.3848 14.939 10.5225 15.1874 10.7607 15.334C10.8889 15.4163 11.0264 15.4521 11.1729 15.4521C11.301 15.4521 11.4204 15.425 11.5303 15.3701L14.7383 13.7666C15.2055 13.5375 15.499 13.0604 15.499 12.5381V9.50488C15.4989 9.23012 15.3612 8.98249 15.123 8.83594ZM10.5771 4.62988C10.2108 4.43772 9.78041 4.43767 9.41406 4.62988L5.96777 6.49023C5.7205 6.62774 5.56543 6.90315 5.56543 7.19629C5.56554 7.4984 5.72067 7.76389 5.96777 7.90137L9.41406 9.76172C9.5973 9.86246 9.78966 9.9082 9.99121 9.9082C10.1927 9.90819 10.394 9.86239 10.5771 9.76172L14.0234 7.90137C14.2706 7.76392 14.4266 7.49846 14.4268 7.19629C14.4268 6.89393 14.2708 6.62771 14.0234 6.49023L10.5771 4.62988Z"
            fill="currentColor"
          />
        </g>
        <defs>
          <clipPath id="clip0_2143_2199">
            <rect width="20" height="20" fill="white" />
          </clipPath>
        </defs>
      </svg>
    </SvgIcon>
  );
};
