name: Pull Request Checks

on:
  pull_request:
    types:
      - opened
      - edited
      - synchronize
    branches:
      - main

permissions:
  # Allow access to commit list
  contents: read
  # Allow access to adding comments
  discussions: write
  pull-requests: write
jobs:
  Code-Leak-Check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GITLEAKS_LICENSE: ${{ secrets.GITLEAKS_LICENSE}}
  test-and-build:
    name: Run Tests and Build with Dockerfile
    runs-on: ubuntu-latest
    env:
      NEXT_PUBLIC_ASSET_DOMAIN: 'directus-asset.example.com'
      NEXT_PUBLIC_ASSET_HOST: 'directus-asset.example.com'
      NEXT_PUBLIC_PATH: '/fe/packaging-house'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Disable <PERSON><PERSON> in CI
        run: |
          export HUSKY=0

      - name: Build Docker image
        run: |
          docker build -t dt-packaging-house-app:latest .

      - name: Run Docker container
        run: |
          docker run -d --name dt-packaging-house-container -p 3000:3000 \
          -e NEXT_PUBLIC_ASSET_DOMAIN=${{ env.NEXT_PUBLIC_ASSET_DOMAIN }} \
          -e NEXT_PUBLIC_ASSET_HOST=${{ env.NEXT_PUBLIC_ASSET_HOST }} \
          -e NEXT_PUBLIC_PATH=${{ env.NEXT_PUBLIC_PATH }} \
          dt-packaging-house-app:latest

      - name: Wait for the server to start
        run: sleep 10

      - name: Check running services
        run: docker ps

      - name: Stop and remove Docker container
        run: |
          docker stop dt-packaging-house-container
          docker rm dt-packaging-house-container
