import { Box, TextField, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC, useEffect, useState } from 'react';
import { Drawer } from 'components';
import { PackingHouseDetail, SealShipmentRequest } from 'types';
import { useSealShipmentMutate } from 'hooks/mutates/useSealShipmentMutate';
import { capturePosthog } from 'utils/posthog';
import { sendEvent } from 'utils/gtag';

interface SealDrawerProps {
  data: PackingHouseDetail;
  open: boolean;
  toggle: (open: boolean) => void;
}

export const SealDrawer: FC<SealDrawerProps> = ({ data, open, toggle }) => {
  const shipmentTranslation = useTranslations('shipment');

  const commonTranslation = useTranslations('common');
  const { mutateAsync } = useSealShipmentMutate();

  const [formData, setFormData] = useState<SealShipmentRequest>({
    productId: data.id,
    doaOfficerName: '',
    sealNumbers: [''],
  });

  useEffect(() => {
    if (!open) {
      setFormData({
        productId: data.id,
        doaOfficerName: '',
        sealNumbers: [''],
      });
    }
  }, [data.id, open]);

  useEffect(() => {
    if (!data?.packing) return;
  }, [data]);

  const handleOfficerNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    const isValid = value?.length <=100;

    if (!isValid) {
      return;
    }

    setFormData({
      ...formData,
      doaOfficerName: e.target.value,
    });
  };

  const handleSealNumberChange = (value: string) => {
    const curSealNumber = value;

    const isValid = value?.length <=100;

    if (!isValid) {
      return;
    }

    setFormData({
      ...formData,
      sealNumbers: [curSealNumber],
    });
  };

  const onClose = () => {
    toggle(false);
  };

  const handleSeal = async () => {
    mutateAsync({
      productId: data.id,
      doaOfficerName: formData.doaOfficerName,
      sealNumbers: formData.sealNumbers,
    });
    sendEvent('seal_container');
    capturePosthog('seal_shipment');
    sendEvent('complete_shipment');
  };

  const drawerTitle = `${shipmentTranslation('seal-shipment')} ${data?.batchlot}`;

  return (
    <Drawer
      anchor="right"
      drawerTitle={drawerTitle}
      open={open}
      onClose={onClose}
      hasActionBtn={true}
      onConfirm={handleSeal}
      confirmButtonText={commonTranslation('save-modal-btn')}
    >
      <Box
        component="div"
        sx={{ width: '100%', p: '24px 16px', display: 'flex', flexDirection: 'column', gap: '16px' }}
      >
        <Typography sx={{ fontSize: '18px', mb: 1 }} variant="caption">
          {shipmentTranslation('doa-title')}
        </Typography>
        <TextField
          value={formData.doaOfficerName}
          onChange={handleOfficerNameChange}
          placeholder={shipmentTranslation('doa-placeholder')}
          slotProps={{
            htmlInput: {
              maxLength: 100,
            },
          }}
        />
        <Typography sx={{ fontSize: '18px', mb: 1 }} variant="caption">
          {shipmentTranslation('seal-title')}
        </Typography>
        <TextField
          value={formData?.sealNumbers?.[0] ?? ''}
          onChange={(e) => {
            handleSealNumberChange(e.target.value);
          }}
          placeholder={`${shipmentTranslation('seal-placeholder')}`}
          slotProps={{
            htmlInput: {
              maxLength: 100,
            },
          }}
        />
      </Box>
    </Drawer>
  );
};
