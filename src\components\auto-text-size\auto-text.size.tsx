import { Typography } from '@mui/material';
import { useEffect, useRef, useState } from 'react';

export const AutoFitTextSize: React.FC<{
  text: string;
  maxFontSize?: number;
  minFontSize?: number;
  style?: React.CSSProperties;
  sx?: React.CSSProperties;
  labelProps?: React.CSSProperties;
  label?: string;
  fontWeight?: number;
  lineHeight?: string;
  fontFamily?: string;
  onFontSizeChange?: (size: number) => void;
}> = ({
  text,
  maxFontSize = 18,
  minFontSize = 8,
  style,
  sx,
  label,
  labelProps,
  onFontSizeChange,
  fontFamily,
  fontWeight,
  lineHeight,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const textRef = useRef<HTMLDivElement>(null);
  const [fontSize, setFontSize] = useState(maxFontSize);

  useEffect(() => {
    const resizeText = () => {
      const container = containerRef.current;
      const textEl = textRef.current;
      if (!container || !textEl) return;

      let size = maxFontSize;
      textEl.style.fontSize = `${size}px`;

      while (size > minFontSize && textEl.scrollWidth >= container.offsetWidth) {
        size -= 1;
        textEl.style.fontSize = `${size}px`;
      }

      if (size !== fontSize) {
        setFontSize(size);
        onFontSizeChange?.(size);
      }
    };

    resizeText();

    const observer = new ResizeObserver(resizeText);
    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [text, maxFontSize, minFontSize, fontSize, onFontSizeChange]);

  return (
    <div
      ref={containerRef}
      style={{
        width: '100%',
        alignItems: 'baseline',
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        display: 'flex',
        gap: '4px',
        ...style,
      }}
    >
      {label && (
        <Typography fontWeight={fontWeight} lineHeight={lineHeight} fontFamily={fontFamily} variant="caption" sx={{ fontSize: maxFontSize, display: 'inline-block', ...labelProps }} noWrap>
          {label}
        </Typography>
      )}
      <Typography fontWeight={fontWeight} lineHeight={lineHeight} fontFamily={fontFamily} variant="caption" ref={textRef} sx={{ fontSize, display: 'inline-block', ...sx }} noWrap>
        {text}
      </Typography>
    </div>
  );
};
