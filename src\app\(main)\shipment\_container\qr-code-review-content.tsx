import { Box, Grid } from '@mui/material';
import PackingQrCode from 'components/qr-code-review/qr-code-review';
import { get } from 'lodash-es';
import { useCreateShipmentStore } from 'store/useCreateShipmentStore';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { useUserStore } from 'store/useUserStore';
import { DurianVariety } from 'types';
import { formatDate, safeJSONParse } from 'utils';
import { getLabelByLocale } from 'utils/cookie-client';
import { formatOrchardNo } from 'utils/format';

export const QrCodeReviewContent = () => {
  const { informationFormValues, qrCodeBatchlots, ocrFile } = useCreateShipmentStore();
  const { getGradeLabel, getProductTypeLabel } = useMasterDataStore();
  const user = useUserStore((state) => state.user);

  const supplier = get(user, 'profile.supplierId', {});

  const packingHouse = get(supplier, 'packingHouse');

  const doaNumber = get(packingHouse, 'doaNumber.number', '');

  return (
    <Box sx={{ width: '100%', overflowY: 'auto', height: 'auto' }}>
      <Grid container spacing={2} mt="16px">
        {informationFormValues.map((it, idx) => {
          const variety = safeJSONParse<DurianVariety>(it.variety);
          const qrData = qrCodeBatchlots[idx];

          return (
            <Grid
              key={it.id}
              size={{
                md: 12,
                lg: 6,
                xl: 6,
              }}
            >
              <PackingQrCode
                data={{
                  batchNumber: qrData?.batchlot,
                  variety: getLabelByLocale(variety),
                  grade: getGradeLabel(it?.grade),
                  totalBoxes: it?.totalBoxes,
                  brand: 'Fresh Durian',
                  netWeight: it?.netWeight,
                  boxType: getProductTypeLabel(it?.boxType),
                  qrUrl: (it.qrUrl || qrData?.qrUrl) ?? '',
                  packingDate: formatDate(it?.packingDate ?? ''),
                  exportTo: ocrFile?.destinationCountry,
                  productOf: 'Thailand',
                  orchardRegisterNumber: ocrFile?.orchardNo ? `AC ${formatOrchardNo(ocrFile?.orchardNo)}` : undefined,
                  exportCompany: ocrFile?.nameOfExportingCompany,
                  packingHouseRegisterNumber: doaNumber,
                }}
              />
            </Grid>
          );
        })}
      </Grid>
    </Box>
  );
};
