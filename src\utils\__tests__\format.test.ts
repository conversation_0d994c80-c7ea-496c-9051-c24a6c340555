import { formatDate, formatDateTime, formatOrchardNo } from '../format';
import { DD_MMMM_YYYY_WITH_DASH, FORMAT_DATE_HOUR_MINUTE } from 'constant/common';
import dayjs from 'dayjs';

describe('formatDate', () => {
  it('should format date correctly with default format', () => {
    const date = '2023-01-15';
    const result = formatDate(date);
    expect(result).toBe(dayjs(date).format(DD_MMMM_YYYY_WITH_DASH));
  });

  it('should format date correctly with custom format', () => {
    const date = '2023-01-15';
    const customFormat = 'YYYY/MM/DD';
    const result = formatDate(date, customFormat);
    expect(result).toBe(dayjs(date).format(customFormat));
  });

  it('should return empty string for null or undefined', () => {
    expect(formatDate(null)).toBe('');
    expect(formatDate(undefined)).toBe('');
  });

  it('should return empty string for invalid date', () => {
    expect(formatDate('invalid-date')).toBe('');
  });
});

describe('formatDateTime', () => {
  it('should format datetime correctly with default format', () => {
    const datetime = '2023-01-15 14:30';
    const result = formatDateTime(datetime);
    expect(result).toBe(dayjs(datetime).format(FORMAT_DATE_HOUR_MINUTE));
  });

  it('should format datetime correctly with custom format', () => {
    const datetime = '2023-01-15 14:30';
    const customFormat = 'YYYY/MM/DD HH:mm:ss';
    const result = formatDateTime(datetime, customFormat);
    expect(result).toBe(dayjs(datetime).format(customFormat));
  });

  it('should return empty string for null or undefined', () => {
    expect(formatDateTime('')).toBe('');
    expect(formatDateTime(undefined)).toBe('');
  });
});

describe('formatOrchardNo', () => {
  it('should return empty string for null or undefined', () => {
    expect(formatOrchardNo(undefined)).toBe('');
    expect(formatOrchardNo('')).toBe('');
  });

  it('should return the same string if length is less than 2', () => {
    expect(formatOrchardNo('1')).toBe('1');
  });

  it('should format string with length 2', () => {
    expect(formatOrchardNo('12')).toBe('12');
  });

  it('should format string with length between 3 and 5', () => {
    expect(formatOrchardNo('123')).toBe('12-3');
    expect(formatOrchardNo('12345')).toBe('12-345');
  });

  it('should format string with length between 6 and 7', () => {
    expect(formatOrchardNo('123456')).toBe('12-3456');
    expect(formatOrchardNo('1234567')).toBe('12-3456-7');
  });

  it('should format string with length between 8 and 10', () => {
    expect(formatOrchardNo('12345678')).toBe('12-3456-78');
    expect(formatOrchardNo('1234567890')).toBe('12-3456-78-90');
  });

  it('should format string with length 11', () => {
    expect(formatOrchardNo('12345678901')).toBe('12-3456-78-901');
  });

  it('should format string with length greater than 11', () => {
    expect(formatOrchardNo('123456789012')).toBe('12-3456-78-901-2');
    expect(formatOrchardNo('1234567890123')).toBe('12-3456-78-901-23');
  });
});
