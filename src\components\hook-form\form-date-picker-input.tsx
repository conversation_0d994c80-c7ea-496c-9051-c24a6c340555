import { Fade, FormControl, TextFieldProps, Typography } from '@mui/material';
import { ReactElement } from 'react';
import { Control, Controller, FieldErrors, FieldValues, Path } from 'react-hook-form';
import dayjs from 'dayjs';
import { DD_MMMM_YYYY_WITH_DASH } from 'constant/common';
import { CustomDatePicker } from 'components/date-picker/custom-date-picker';

export interface FormDatePickerInputProps<TFormValues extends FieldValues> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  errors?: FieldErrors<TFormValues>;
  label: string;
  required?: boolean;
  requiredMessage?: string;
  placeholder?: string;
  variant?: TextFieldProps['variant'];
  max?: number;
  min?: number;
  patternMessage?: string;
  pattern?: RegExp;
  loading?: boolean;
}

export function FormDatePickerInput<TFormValues extends FieldValues>({
  name,
  control,
  label,
  required = false,
  requiredMessage = 'This field is required',
  loading,
  errors,
}: FormDatePickerInputProps<TFormValues>): ReactElement {
  const rules = {
    ...(required && { required: requiredMessage }),
  };

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field, fieldState }) => {
        return (
          <FormControl fullWidth>
            <Typography sx={{ fontSize: '18px', mb: 1 }} variant="caption">
              {label}{' '}
              <Typography sx={{ visibility: required ? 'visible' : 'hidden' }} variant="caption" color="error">
                *
              </Typography>
            </Typography>
            <CustomDatePicker
              error={errors?.[name] ? true : false}
              loading={loading}
              name={name}
              value={dayjs(field.value).isValid() ? dayjs(field.value) : null}
              onChange={(date) => {
                field.onChange(date);
              }}
              format={DD_MMMM_YYYY_WITH_DASH}
            />

            {!!fieldState.error && (
              <Fade in={true} timeout={300}>
                <Typography variant="caption" color="error">
                  {fieldState?.error?.message}
                </Typography>
              </Fade>
            )}
          </FormControl>
        );
      }}
    />
  );
}
