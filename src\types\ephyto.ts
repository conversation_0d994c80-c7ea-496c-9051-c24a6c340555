export type PortAlert = {
  portName: string;
  alertMessage: string;
};

export type EphytoExportDocument = {
 receiptNumber: string;
  ePhytoNumber: string;
  sealNumbers: string[];
  exporterName: string;
  packingHouse: string;
  packingHouseDoaNumber: string;
  exportCountry: string;
  portOfExit: string;
  commodityType: string;
  gaPs: string[];
  quantity: number;
  numberOfPackage: number;
  submissionDate: number; // Unix timestamp (seconds)
  exportDate: number;     // Unix timestamp (seconds)
  portAlerts: PortAlert[];
  conveyance: string;
};
