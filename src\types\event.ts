import { DurianVariety, TranslateLabel, UpdateVarietyRequest } from './durian';
import { Receipt } from './master-data';

export type RejectHarvestRequest = {
  productId: string;
  reason: string;
};

export type SealShipmentRequest = {
  productId: string;
  doaOfficerName?: string;
  sealNumbers?: string[];
};

export type ReceivingBatchLotRequest = {
  harvestProductId: string;
  latitude: number;
  longitude: number;
  update?: {
    varieties: DurianVariety[];
  };
};

export interface Image {
  id: string;
  filenameDisk: string;
  filenameDownload: string;
}

export interface PackingHouseInfo {
  address: string;
  doaNumber: string;
  doaValidFrom: number;
  doaValidTo: number;
  gmpNumber: string;
  gmpValidFrom: number;
  gmpValidTo: number;
  name: string;
  ownerName?: string;
  ownerNickname?: string;
  ownerPhone?: string;
}

export interface Farm {
  id: string;
  name: string;
  address: string;
  gln?: string;
  gap?: string;
}

export interface Plot {
  id: string;
  gap?: string;
  area?: number;
  areaUnit?: string;
  name?: string;
  plotId?: string;
  image?: string;
}

export interface Province {
  label: TranslateLabel;
  provinceVehicleCode?: string;
  provinceCode?: string;
  regionCode?: string;
}

export interface FarmPlotInfo {
  farmId: string;
  plots: {
    id: string;
    area?: number;
    areaUnit?: {
      en: string;
      th: string;
    };
    name?: string;
    gap?: string;
    plotId?: string;
    image?: {
      id: string;
      filenameDisk: string;
      filenameDownload: string;
    };
  }[];
}

export interface CutterVehicle {
  image?: Image;
  provinceRegistrationNumber: string;
  vehicleRegistrationNumber: string;
}

export interface Metadata {
  startTime: string;
  endTime: string;
  weight: number;
  trunkPlateNumber: string;
  grade?: string;
  gtinNumber?: string;
  productName?: string;
  quantity?: number;
  unit?: string;
  gtinId?: string;
  productId?: string;
  diffVarieties?: {
    value: string;
    label: {
      th: string;
      en: string;
    };
    grades: {
      value: string;
      diff: {
        origin: number | null;
        current: number | null;
      };
      label: {
        th: string;
        en: string;
      };
    }[];
  }[];
  sourceMaterial: string;
  quantityPerPackage?: number;
  harbor?: string;
  destination?: string;
  name?: string;
  documents?: string[];
  declarationNumber?: string;
  farm?: FarmPlotInfo;
  truckPlate?: string;
  trailerPlate?: string;
  shipmentId?: string;
  sourceBatchlot?: string;
  flowerBloomingDay: string;
  cuttingDay?: string;
  cutterVehicle?: CutterVehicle;
  cutter?: Cutter;
  packingHouse?: PackingHouse;
}

export interface Cutter {
  id: string | null;
  name: string;
  licenseNumber: string;
  isCertified?: boolean;
  avatar?: Image;
  phoneNumber?: string;
  suggestedPhoneNumber?: string;
  firstName?: string;
  lastName?: string;
  profileId: string | null;
  existingCutterProfileId: string | null;
  existingCutterId: string | null;
  vehicleNumber: string | null;
  provinceVehicleCode: string | null;
  vehicleImage: string | null;
}

export interface PackingHouse {
  id: string;
  eventStatus: string;
  eventType: string;
  name: string;
  description: string;
  dateCreated: string;
  dateUpdated?: string;
  eventTimeUnix: string;
  eventTime: string;
  address: string;
  country: string;
  images: Image[];
  logo: Image;
  gln: string;
  productId: string;
  status: string;
  type: string;
  positionLongitude: number;
  positionLatitude: number;
  farm: {
    address: string;
    gln: null | string;
    id: string;
    name: string;
    gap: string;
  };
  role: string;
  userCreated: {
    id: string;
    firstName: string;
    lastName: string;
    role: string;
    roleLabel?: {
      en: string;
      th: string;
    };
    profile: {
      nickname?: string;
    };
  };
  userUpdated?: {
    id: string;
    firstName: string;
    lastName: string;
  };
  totalVarietiesWeight?: number;
  meta: Metadata;
  batchlot: string;
  originBatchlot: string;
  varieties: DurianVariety[];

  // QR code management
  qrcodeId: string;
  orchardRegisterNumber: string;
  shipmentAssigned: string;
  packageAssigned: string;
  nameOfExportingCompany: string;
  productType: string;
  product: null;
  packingDate: number | null;
  packingHouseDoaNumber: string;
}

export interface PackingHouseDetail {
  id: string;
  eventId?: string;
  status: string;
  type: string;
  name: string;
  description: string;
  address: string;
  country: string;
  dateCreated: string;
  images: Image[];
  originBatchlot?: string;
  originBatchlotId?: string;
  logo: Image;
  farm: {
    address: string;
    gln?: string;
    id: string;
    name: string;
    gap?: string;
  };
  gln: string;
  positionLongitude: number;
  positionLatitude: number;
  role: string;
  batchlot: string;
  packingHouse: PackingHouse;
  pq7ReceiptDetails?: Receipt;
  sourceMaterials?: PackingHouseSourceMaterialData[];
  userCreated: {
    id: string;
    firstName: string;
    lastName: string;
    role: string;
    roleLabel?: {
      en: string;
      th: string;
    };
    profile: {
      nickname?: string;
    };
  };
  files?: {
    product: CustomFile[];
    shipment: CustomFile[];
  };
  originUserCreated?: {
    id: string;
    firstName: string;
    lastName: string;
    role: string;
    roleLabel?: {
      en: string;
      th: string;
    };
    profile: {
      nickname?: string;
    };
  };
  packing: PackingHousePackingData[];
  meta: Metadata;
  formStep?: number;
  varieties: DurianVariety[];
  immutable: boolean;
  rejectReason?: string;
}

export type QrcodeBatchLot = { qrId: string; batchlot: string; qrUrl: string, id: string };

interface PackingHousePackingData {
  id: string;
  brandNameInfo: {
    id: string;
    label: TranslateLabel;
  };
  productTypeInfo: {
    id: string;
    label: TranslateLabel;
  };
  varietyGradeJoinId: number;
  weightKg: number;
  quantity: string;
  numberOfBoxes: number;
  sealNumber: string;
  varietyGrade: {
    gradeValue: string;
    gradeDisplayText: string;
    varietyValue: string;
    varietyDisplayText: string;
  };
  qrCodes: QrcodeBatchLot[];
  varieties: DurianVariety[];
  batchlot: string;
  varietyName?: string;
  packingDate: number | null;
}

interface PackingHouseSourceMaterialData {
  id: string;
  batchlot: string;
  originBatchlot: string;
  originBatchlotId: string;
  farm: {
    address: string;
    gln: null | string;
    id: string;
    name: string;
    gap: string;
  };
  name: string;
  type: EventType;
  weight: number;
  fruitVarietyGradeId: number;
  logo: Image;
  varieties: DurianVariety[];
}

interface CustomFile {
  filenameDisk: string;
  id: string;
  filesize: number;
  filenameDownload: string;
  mimeType: string;
  url?: string;
}

export interface GroupedSelectedDurian {
  id: string;
  grades: {
    id: string;
    weight: number;
  }[];
  flowerBloomingDay: number;
}

export interface CreateHarvesting {
  batchName: string;
  status: 'draft' | 'consumed';
  latitude: number;
  longitude: number;
  harvestImage: string[];
  cuttingDay?: number; // Unix timestamp
  cutterVehicle?: {
    image: string | null;
    provinceRegistrationNumber: string;
    vehicleRegistrationNumber: string;
  };
  farm?: {
    farmId: string;
    plots: {
      id: string;
      image?: string;
    }[];
  };
  varieties?: DurianVariety[];
  cutter?: {
    name: string;
    avatar: string | null;
    isCertified: boolean;
    existingCutterId: string | null;
    existingCutterProfileId: string | null;
  };
}

export type EventType = 'receiving' | 'shipment' | 'harvesting' | 'qr';
export interface ReceiveUpdatePayload {
  update: {
    varieties: UpdateVarietyRequest[];
    clientName?: string;
  };
}

export type ReceiveUpdateRequest = {
  productId: string;
  update?: {
    varieties: DurianVariety[];
  };
};

export enum RecordedByEnum {
  FARMER = 'farmer',
  CUTTER = 'cutter',
  PACKING_HOUSE = 'packing_house_staff',
}

export enum EventStatusEnum {
  DRAFT = 'draft',
  RECEIVED = 'received',
  REJECTED = 'rejected',
  SEALED = 'sealed',
  WAITING = 'waiting',

  // product status type
  RECEIVING = 'receiving',
  INCOMING = 'incoming',
  NA = 'N/A',

  // QR code status
  AVAILABLE = 'available',
  ASSIGNED = 'assigned',
}
