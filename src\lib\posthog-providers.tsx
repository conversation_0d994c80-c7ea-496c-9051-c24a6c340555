'use client';

import { Suspense, useEffect } from 'react';

import { usePathname, useSearchParams } from 'next/navigation';
import posthog from 'posthog-js';
import { PostHogProvider as PHProvider, usePostHog } from 'posthog-js/react';
import { useUserStore } from 'store/useUserStore';
import { logger } from 'utils/logger';

export function PostHogProvider({ children }: { readonly children: React.ReactNode }) {
  const user = useUserStore((state) => state.user);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const posthogKey = window.__ENV__?.NEXT_PUBLIC_POSTHOG_KEY;
    const posthogHost = window.__ENV__?.NEXT_PUBLIC_POSTHOG_HOST;

    if (!posthogKey || !posthogHost) {
      logger.warn(
        'PostHog is not configured. Please set NEXT_PUBLIC_POSTHOG_KEY and NEXT_PUBLIC_POSTHOG_HOST in your environment variables.'
      );
      return;
    }

    if (user && posthogKey && posthogHost) {
      posthog.identify(user.id, {
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phoneNumber,
        packingHouseId: user.profile.roleLabel,
      });
    }

    if (posthogKey && posthogHost) {
      posthog.init(posthogKey as string, {
        api_host: posthogHost,
        person_profiles: 'identified_only',
        capture_pageview: false,
        capture_pageleave: true,
        secure_cookie: true,
        persistence: 'sessionStorage',
        capture_dead_clicks: true,
        capture_heatmaps: true,
        autocapture: true,
        capture_exceptions: true,
        capture_performance: true,
      });
    }
  }, [user]);

  return (
    <PHProvider client={posthog}>
      <SuspendedPostHogPageView />
      {children}
    </PHProvider>
  );
}

function PostHogPageView() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const posthogConfig = usePostHog();

  // Track pageviews
  useEffect(() => {
    if (pathname && posthog) {
      let url = window.origin + pathname;
      if (searchParams?.toString()) {
        url = url + '?' + searchParams.toString();
      }

      posthogConfig.capture('$pageview', { $current_url: url });
    }
  }, [pathname, searchParams, posthogConfig]);

  return null;
}

// Wrap PostHogPageView in Suspense to avoid the useSearchParams usage above
// from de-opting the whole app into client-side rendering
// See: https://nextjs.org/docs/messages/deopted-into-client-rendering
function SuspendedPostHogPageView() {
  return (
    <Suspense fallback={null}>
      <PostHogPageView />
    </Suspense>
  );
}
