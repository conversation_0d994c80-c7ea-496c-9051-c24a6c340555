import { CheckCircleOutline, ErrorOutline, InfoOutline } from '@mui/icons-material';
import { CustomContentProps, SnackbarContent, VariantType, closeSnackbar } from 'notistack';
import { CSSProperties, forwardRef } from 'react';
import { theme } from 'styles/theme';

export const SnackbarIcon = ({ variant }: { variant: VariantType }) => {
  switch (variant) {
    case 'success':
      return <CheckCircleOutline style={{ color: theme.palette.customColors.toastSuccess }} />;
    case 'error':
      return <ErrorOutline style={{ color: theme.palette.customColors.toastError }} />;
    case 'warning':
      return <ErrorOutline style={{ color: '#fff' }} />;
    case 'info':
      return <InfoOutline style={{ color: theme.palette.customColors.toastInfo }} />;
    default:
      return null;
  }
};

// Common base style for all toasts
const baseToastStyle: CSSProperties = {
  display: 'flex',
  alignItems: 'center',
  minWidth: '380px',
  padding: '8px 16px',
  borderRadius: '8px',
  marginTop: '64px',
  marginRight: '24px',
  boxShadow: '0 3px 5px rgba(0,0,0,0.2)',
  cursor: 'pointer',
};

const SuccessToast = forwardRef<HTMLDivElement, CustomContentProps>(({ message, id }, ref) => (
  <SnackbarContent ref={ref}>
    <div
      onClick={() => closeSnackbar?.(id)}
      style={{
        ...baseToastStyle,
        backgroundColor: theme.palette.customColors.toastSuccessBg,
        color: theme.palette.customColors.toastSuccess,
        border: `1px solid ${theme.palette.customColors.toastSuccess}`,
      }}
    >
      <SnackbarIcon variant="success" />
      <span style={{ marginLeft: '8px' }}>{message}</span>
    </div>
  </SnackbarContent>
));

// Error toast component with ref forwarding
const ErrorToast = forwardRef<HTMLDivElement, CustomContentProps>(({ message, id }, ref) => (
  <SnackbarContent ref={ref}>
    <div
      onClick={() => closeSnackbar?.(id)}
      style={{
        ...baseToastStyle,
        backgroundColor: theme.palette.customColors.toastErrorBg,
        color: theme.palette.customColors.toastError,
      }}
    >
      <SnackbarIcon variant="error" />
      <span style={{ marginLeft: '8px' }}>{message}</span>
    </div>
  </SnackbarContent>
));

// Warning toast component with ref forwarding
const WarningToast = forwardRef<HTMLDivElement, CustomContentProps>(({ message, id }, ref) => (
  <SnackbarContent ref={ref}>
    <div
      onClick={() => closeSnackbar?.(id)}
      style={{
        ...baseToastStyle,
        backgroundColor: theme.palette.customColors.toastWarningBg,
        color: theme.palette.customColors.toastWarning,
      }}
    >
      <SnackbarIcon variant="warning" />
      <span style={{ marginLeft: '8px' }}>{message}</span>
    </div>
  </SnackbarContent>
));

// Info toast component with ref forwarding
const InfoToast = forwardRef<HTMLDivElement, CustomContentProps>(({ message, id }, ref) => (
  <SnackbarContent ref={ref}>
    <div
      onClick={() => closeSnackbar?.(id)}
      style={{
        ...baseToastStyle,
        backgroundColor: theme.palette.customColors.toastInfoBg,
        color: theme.palette.customColors.toastInfo,
      }}
    >
      <SnackbarIcon variant="info" />
      <span style={{ marginLeft: '8px' }}>{message}</span>
    </div>
  </SnackbarContent>
));

SuccessToast.displayName = 'SuccessToast';
ErrorToast.displayName = 'ErrorToast';
WarningToast.displayName = 'WarningToast';
InfoToast.displayName = 'InfoToast';

export const customToast = {
  success: SuccessToast,
  error: ErrorToast,
  warning: WarningToast,
  info: InfoToast,
};
