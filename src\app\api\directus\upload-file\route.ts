import { AxiosError } from 'axios';
import { AppConfig } from 'configs/app-config';
import { clearAllCookie, getCookieServer } from 'configs/cookie';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  const formData = await req.formData();

  const token = await getCookieServer('access_token_pkg_house');

  try {
    const directusRes = await fetch(`${AppConfig.S3_PROXY_URL}/files`, {
      method: 'POST',
      body: formData,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (directusRes.status < 200 || directusRes.status >= 300) {
      if (directusRes.status === 401) {
        clearAllCookie();
      }
      return NextResponse.json(directusRes, { status: directusRes.status });
    }

    const data = await directusRes.json();

    return NextResponse.json(data, { status: directusRes.status });
  } catch (error) {
    if (error instanceof AxiosError) {
      if (error.response?.status === 401) {
        clearAllCookie();
      }
      return NextResponse.json({
        error: error.message,
        status: error.status,
      });
    }
  }
}
