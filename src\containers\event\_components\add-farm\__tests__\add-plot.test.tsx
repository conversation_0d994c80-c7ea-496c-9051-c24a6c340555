/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-require-imports */
import React from 'react';
import { screen, fireEvent } from '@testing-library/react';
import { useQuery } from '@tanstack/react-query';
import { AddPlot } from '../add-plot';
import { Farm, Plot } from 'types';
import { render } from 'configs/test-util';

// Mock the useQuery hook
jest.mock('@tanstack/react-query', () => ({
  ...jest.requireActual('@tanstack/react-query'),
  useQuery: jest.fn(),
}));

// Mock the service
jest.mock('services/resource.service', () => ({
  fetchFarmPlotService: jest.fn(),
}));

// Mock the utils
jest.mock('utils', () => ({
  formatNumberWithCommas: jest.fn((value) => value?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')),
  formatGap: jest.fn((value) => {
    if (!value) return '';
    const digits = value.toString().replaceAll('-', '').trim();
    if (digits.length !== 17) return digits;
    return `${digits.slice(0, 2)}-${digits.slice(2, 6)}-${digits.slice(6, 8)}-${digits.slice(8, 11)}-${digits.slice(11, 17)}`;
  }),
  formatPlotId: jest.fn((value) => {
    if (!value) return '';
    const digits = value.toString().replaceAll('-', '');
    if (digits.length !== 16) return digits;
    return `${digits.slice(0, 8)}-${digits.slice(8, 12)}-${digits.slice(12, 16)}-${digits.slice(16, 20)}`;
  }),
}));

// Mock the theme
jest.mock('styles/theme', () => ({
  theme: {
    palette: {
      customColors: {
        gray: '#666666',
      },
    },
  },
}));

// Mock the styles
jest.mock('components/filter-table/filter-table.styles', () => ({
  activeButtonSelectStyle: {
    color: '#4285F4',
    border: '1px solid #4285F4',
    borderLeft: '8px solid #4285F4',
  },
  normalButtonSelectStyle: {
    color: '#000000',
    border: '1px solid #e5e5ea',
  },
}));

// Assets are handled by Jest moduleNameMapper

// Mock Next.js Image component
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: { src: string; alt: string; [key: string]: any }) {
    return <img src={src} alt={alt} {...props} />;
  };
});

const mockUseQuery = useQuery as jest.MockedFunction<typeof useQuery>;

// Helper function to create complete mock query result
const createMockQueryResult = (overrides: any = {}) => ({
  data: undefined,
  isLoading: false,
  isFetching: false,
  error: null,
  isError: false,
  isSuccess: true,
  status: 'success' as const,
  isPending: false,
  isLoadingError: false,
  isRefetchError: false,
  isPlaceholderData: false,
  isPaused: false,
  isStale: false,
  isRefetching: false,
  isInitialLoading: false,
  failureCount: 0,
  failureReason: null,
  errorUpdateCount: 0,
  dataUpdatedAt: Date.now(),
  errorUpdatedAt: 0,
  fetchStatus: 'idle' as const,
  refetch: jest.fn(),
  ...overrides,
});

describe('AddPlot', () => {
  const mockFarm: Farm = {
    id: 'farm-1',
    name: 'Test Farm',
    address: '123 Test Street',
    gln: 'GLN123',
    gap: 'GAP123',
  };

  const mockPlots: Plot[] = [
    {
      id: 'plot-1',
      name: 'Plot 1',
      plotId: '1234567890123456',
      area: 1000,
      areaUnit: 'rai',
      gap: 'GAP001',
    },
    {
      id: 'plot-2',
      name: 'Plot 2',
      plotId: '2345678901234567',
      area: 1500,
      areaUnit: 'rai',
      gap: 'GAP002',
    },
  ];

  const mockSetSelectedPlots = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Loading State', () => {
    it('should show loading indicator when data is loading', () => {
      mockUseQuery.mockReturnValue(createMockQueryResult({
        data: undefined,
        isLoading: true,
        isFetching: true,
        isSuccess: false,
        status: 'pending',
      }));

      render(<AddPlot farm={mockFarm} selectedPlots={[]} setSelectedPlots={mockSetSelectedPlots} />);

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });
  });

  describe('Empty State', () => {
    it('should show empty state when no plots are available', () => {
      mockUseQuery.mockReturnValue(createMockQueryResult({
        data: { data: [] },
      }));

      render(<AddPlot farm={mockFarm} selectedPlots={[]} setSelectedPlots={mockSetSelectedPlots} />);

      expect(screen.getByAltText('empty-state-icon')).toBeInTheDocument();
      expect(screen.getByText('no-plot')).toBeInTheDocument();
    });
  });

  describe('Plot List Rendering', () => {
    beforeEach(() => {
      mockUseQuery.mockReturnValue(createMockQueryResult({
        data: { data: mockPlots },
      }));
    });

    it('should render plot list correctly', () => {
      render(<AddPlot farm={mockFarm} selectedPlots={[]} setSelectedPlots={mockSetSelectedPlots} />);

      expect(screen.getByText('Plot 1')).toBeInTheDocument();
      expect(screen.getByText('Plot 2')).toBeInTheDocument();
      expect(screen.getByText('id - 12345678-9012-3456-')).toBeInTheDocument();
      expect(screen.getByText('id - 23456789-0123-4567-')).toBeInTheDocument();
      expect(screen.getByText('area - 1,000 rai')).toBeInTheDocument();
      expect(screen.getByText('area - 1,500 rai')).toBeInTheDocument();
      expect(screen.getByText('gap - prefix-plot GAP001')).toBeInTheDocument();
      expect(screen.getByText('gap - prefix-plot GAP002')).toBeInTheDocument();
    });

    it('should handle missing plot data gracefully', () => {
      const plotsWithMissingData: Plot[] = [
        {
          id: 'plot-3',
          name: 'Plot 3',
          // Missing plotId, area, areaUnit, gap
        },
      ];

      mockUseQuery.mockReturnValue(createMockQueryResult({
        data: { data: plotsWithMissingData },
      }));

      render(<AddPlot farm={mockFarm} selectedPlots={[]} setSelectedPlots={mockSetSelectedPlots} />);

      expect(screen.getByText('Plot 3')).toBeInTheDocument();
      expect(screen.getByText(/id.*--/)).toBeInTheDocument();
      expect(screen.getByText(/area.*--/)).toBeInTheDocument();
      expect(screen.getByText(/gap.*--/)).toBeInTheDocument();
    });
  });

  describe('Plot Selection', () => {
    beforeEach(() => {
      mockUseQuery.mockReturnValue(createMockQueryResult({
        data: { data: mockPlots },
      }));
    });

    it('should select a plot when clicked', () => {
      render(<AddPlot farm={mockFarm} selectedPlots={[]} setSelectedPlots={mockSetSelectedPlots} />);

      const plotButton = screen.getByText('Plot 1').closest('button');
      fireEvent.click(plotButton!);

      expect(mockSetSelectedPlots).toHaveBeenCalledWith([mockPlots[0]]);
    });

    it('should deselect a plot when clicked again', () => {
      render(<AddPlot farm={mockFarm} selectedPlots={[mockPlots[0]]} setSelectedPlots={mockSetSelectedPlots} />);

      const plotButton = screen.getByText('Plot 1').closest('button');
      fireEvent.click(plotButton!);

      expect(mockSetSelectedPlots).toHaveBeenCalledWith([]);
    });

    it('should add multiple plots to selection', () => {
      render(<AddPlot farm={mockFarm} selectedPlots={[mockPlots[0]]} setSelectedPlots={mockSetSelectedPlots} />);

      const plotButton = screen.getByText('Plot 2').closest('button');
      fireEvent.click(plotButton!);

      expect(mockSetSelectedPlots).toHaveBeenCalledWith([mockPlots[0], mockPlots[1]]);
    });

    it('should apply correct styles for selected and unselected plots', () => {
      render(<AddPlot farm={mockFarm} selectedPlots={[mockPlots[0]]} setSelectedPlots={mockSetSelectedPlots} />);

      const selectedPlotButton = screen.getByText('Plot 1').closest('button');
      const unselectedPlotButton = screen.getByText('Plot 2').closest('button');

      // Check if selected plot has active style (border-left with primary color)
      expect(selectedPlotButton).toHaveStyle('border-left: 8px solid #4285F4');

      // Check if unselected plot has normal style
      expect(unselectedPlotButton).toHaveStyle('border: 1px solid #e5e5ea');
    });
  });

  describe('Query Integration', () => {
    it('should call useQuery with correct parameters', () => {
      mockUseQuery.mockReturnValue(createMockQueryResult({
        data: { data: mockPlots },
      }));

      render(<AddPlot farm={mockFarm} selectedPlots={[]} setSelectedPlots={mockSetSelectedPlots} />);

      expect(mockUseQuery).toHaveBeenCalledWith({
        queryKey: ['farm', 'farm-1'],
        queryFn: expect.any(Function),
      });
    });

    it('should call fetchFarmPlotService with correct farm ID in queryFn', () => {
      const mockFetchFarmPlotService = require('services/resource.service').fetchFarmPlotService;

      mockUseQuery.mockImplementation(({ queryFn }) => {
        // Execute the queryFn to test it
        if (typeof queryFn === 'function') {
          queryFn({} as any);
        }
        return createMockQueryResult({
          data: { data: mockPlots },
        });
      });

      render(<AddPlot farm={mockFarm} selectedPlots={[]} setSelectedPlots={mockSetSelectedPlots} />);

      expect(mockFetchFarmPlotService).toHaveBeenCalledWith('farm-1');
    });
  });

  describe('Translations', () => {
    it('should display translated text', () => {
      mockUseQuery.mockReturnValue(createMockQueryResult({
        data: { data: mockPlots },
      }));

      render(<AddPlot farm={mockFarm} selectedPlots={[]} setSelectedPlots={mockSetSelectedPlots} />);

      expect(screen.getByText('plot-require')).toBeInTheDocument();
      expect(screen.getAllByText(/id/).length).toBeGreaterThan(0);
      expect(screen.getAllByText(/area/).length).toBeGreaterThan(0);
      expect(screen.getAllByText(/gap/).length).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle query error gracefully', () => {
      mockUseQuery.mockReturnValue(createMockQueryResult({
        data: undefined,
        error: new Error('Network error'),
        isError: true,
        isSuccess: false,
        status: 'error',
      }));

      render(<AddPlot farm={mockFarm} selectedPlots={[]} setSelectedPlots={mockSetSelectedPlots} />);

      // Component should still render the header text
      expect(screen.getByText('plot-require')).toBeInTheDocument();
    });

    it('should handle undefined data response', () => {
      mockUseQuery.mockReturnValue(createMockQueryResult({
        data: undefined,
      }));

      render(<AddPlot farm={mockFarm} selectedPlots={[]} setSelectedPlots={mockSetSelectedPlots} />);

      expect(screen.getByText('plot-require')).toBeInTheDocument();
      // Should not show empty state or plots
      expect(screen.queryByAltText('empty-state-icon')).not.toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle plots with zero area', () => {
      const plotsWithZeroArea: Plot[] = [
        {
          id: 'plot-zero',
          name: 'Zero Area Plot',
          area: 0,
          areaUnit: 'rai',
        },
      ];

      mockUseQuery.mockReturnValue(createMockQueryResult({
        data: { data: plotsWithZeroArea },
      }));

      render(<AddPlot farm={mockFarm} selectedPlots={[]} setSelectedPlots={mockSetSelectedPlots} />);

      expect(screen.getByText('Zero Area Plot')).toBeInTheDocument();
      expect(screen.getByText(/area.*0.*rai/)).toBeInTheDocument();
    });

    it('should handle very long plot names', () => {
      const plotsWithLongNames: Plot[] = [
        {
          id: 'plot-long',
          name: 'This is a very long plot name that might cause layout issues if not handled properly',
          area: 500,
          areaUnit: 'rai',
        },
      ];

      mockUseQuery.mockReturnValue(createMockQueryResult({
        data: { data: plotsWithLongNames },
      }));

      render(<AddPlot farm={mockFarm} selectedPlots={[]} setSelectedPlots={mockSetSelectedPlots} />);

      expect(
        screen.getByText('This is a very long plot name that might cause layout issues if not handled properly')
      ).toBeInTheDocument();
    });

    it('should format plot ID correctly when available', () => {
      const plotsWithFormattedId: Plot[] = [
        {
          id: 'plot-formatted',
          name: 'Formatted Plot',
          plotId: '1234567890123456',
          area: 500,
          areaUnit: 'rai',
          gap: '12345678901234567',
        },
      ];

      mockUseQuery.mockReturnValue(createMockQueryResult({
        data: { data: plotsWithFormattedId },
      }));

      render(<AddPlot farm={mockFarm} selectedPlots={[]} setSelectedPlots={mockSetSelectedPlots} />);

      expect(screen.getByText('Formatted Plot')).toBeInTheDocument();
      expect(screen.getByText('id - 12345678-9012-3456-')).toBeInTheDocument();
      expect(screen.getByText('gap - prefix-plot 12-3456-78-901-234567')).toBeInTheDocument();
    });

    it('should handle plots with invalid plot ID length', () => {
      const plotsWithInvalidId: Plot[] = [
        {
          id: 'plot-invalid',
          name: 'Invalid ID Plot',
          plotId: '123456789', // Too short
          area: 500,
          areaUnit: 'rai',
        },
      ];

      mockUseQuery.mockReturnValue(createMockQueryResult({
        data: { data: plotsWithInvalidId },
      }));

      render(<AddPlot farm={mockFarm} selectedPlots={[]} setSelectedPlots={mockSetSelectedPlots} />);

      expect(screen.getByText('Invalid ID Plot')).toBeInTheDocument();
      expect(screen.getByText('id - 123456789')).toBeInTheDocument(); // Should return original value
    });
  });
});
