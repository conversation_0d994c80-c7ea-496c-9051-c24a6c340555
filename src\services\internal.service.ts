import { basePathProd } from 'configs/app-config';
import { createApiInstance } from 'services/api/clientApi';
import { UploadFileResponse } from './common.service';
import { User } from 'types/user';

const base = `${basePathProd}`;

export const getCookieLocaleService = (): Promise<{ locale: string }> => {
  const apiService = createApiInstance(base);
  return apiService.get('/api/update-locale');
};

export const updateCookieLocaleService = (locale: string): Promise<void> => {
  const apiService = createApiInstance(base);
  return apiService.post('/api/update-locale', { locale });
};

export const setAuthService = (accessToken: string): Promise<void> => {
  const apiService = createApiInstance(base);
  return apiService.post('/api/auth/set-auth', { accessToken });
};

export const clearAuthService = async () => {
  const response = await fetch(`${base}/api/auth/clear-auth`, {
    method: 'POST',
  });
  return response.json();
};

export const uploadFileService = (data: FormData): Promise<UploadFileResponse> => {
  const apiService = createApiInstance(base);
  return apiService.post('/api/directus/upload-file', data);
};

export const fetchUserInfoService = (): Promise<{ data: User }> => {
  const apiService = createApiInstance(base);
  return apiService.get('/api/user-info');
};
