import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider } from '@mui/material/styles';
import UploadCutterImage from '../upload-cutter-img';
import { theme } from 'styles/theme';
import { uploadFileService } from 'services/internal.service';
import { validateFileImage, getImageUrl } from 'utils';
import toastMessages from 'utils/toastMessages';
import { v4 } from 'uuid';

// Mock dependencies
jest.mock('services/internal.service');
jest.mock('utils', () => ({
  validateFileImage: jest.fn(),
  getImageUrl: jest.fn(),
}));
jest.mock('utils/toastMessages');
jest.mock('uuid', () => ({
  v4: jest.fn(),
}));

const mockUploadFileService = uploadFileService as jest.MockedFunction<typeof uploadFileService>;
const mockValidateFileImage = validateFileImage as jest.MockedFunction<typeof validateFileImage>;
const mockGetImageUrl = getImageUrl as jest.MockedFunction<typeof getImageUrl>;
const mockToastMessages = toastMessages as jest.Mocked<typeof toastMessages>;
const mockV4 = v4 as jest.MockedFunction<() => string>;

// Test wrapper component with theme
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider theme={theme}>{children}</ThemeProvider>
);

describe('UploadCutterImage', () => {
  const mockOnChange = jest.fn();
  const mockFileId = 'test-file-id-123';
  const mockFilenameDisk = 'uploaded-file-123.jpg';

  beforeEach(() => {
    jest.clearAllMocks();
    mockV4.mockReturnValue(mockFileId);
    mockValidateFileImage.mockReturnValue({ isValid: true, wrongFileType: false, wrongFileSize: false });
    mockUploadFileService.mockResolvedValue({
      data: { id: 'file-id', filenameDisk: mockFilenameDisk },
    });
    mockGetImageUrl.mockReturnValue('https://example.com/image.jpg');
  });

  it('renders upload button when no images are uploaded', () => {
    render(
      <TestWrapper>
        <UploadCutterImage onChange={mockOnChange} />
      </TestWrapper>
    );

    const uploadLabel = document.querySelector('label.MuiButton-root');
    expect(uploadLabel).toBeInTheDocument();
    expect(screen.getByText('cutter-photo')).toBeInTheDocument();
    expect(screen.getByText('file-size-limit')).toBeInTheDocument();
  });

  it('renders camera icon in upload button', () => {
    render(
      <TestWrapper>
        <UploadCutterImage onChange={mockOnChange} />
      </TestWrapper>
    );

    const cameraIcon = screen.getByTestId('CameraAltOutlinedIcon');
    expect(cameraIcon).toBeInTheDocument();
  });

  it('handles file selection and upload successfully', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <UploadCutterImage onChange={mockOnChange} />
      </TestWrapper>
    );

    const file = new File(['test content'], 'test-image.jpg', { type: 'image/jpeg' });
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;

    await user.upload(fileInput, file);

    await waitFor(() => {
      expect(mockValidateFileImage).toHaveBeenCalledWith(file);
      expect(mockV4).toHaveBeenCalled();
      expect(mockUploadFileService).toHaveBeenCalledWith(expect.any(FormData));
      expect(mockOnChange).toHaveBeenCalledWith(mockFilenameDisk);
    });
  });

  it('shows loading skeleton during upload', async () => {
    const user = userEvent.setup();

    // Mock a delayed upload response
    mockUploadFileService.mockImplementation(() =>
      new Promise(resolve =>
        setTimeout(() => resolve({ data: { id: 'file-id', filenameDisk: mockFilenameDisk } }), 100)
      )
    );

    render(
      <TestWrapper>
        <UploadCutterImage onChange={mockOnChange} />
      </TestWrapper>
    );

    const file = new File(['test content'], 'test-image.jpg', { type: 'image/jpeg' });
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;

    await user.upload(fileInput, file);

    // Check for loading skeleton
    expect(document.querySelector('.MuiSkeleton-root')).toBeInTheDocument();

    // Wait for upload to complete
    await waitFor(() => {
      expect(document.querySelector('.MuiSkeleton-root')).not.toBeInTheDocument();
    });
  });

  it('displays uploaded image after successful upload', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <UploadCutterImage onChange={mockOnChange} />
      </TestWrapper>
    );

    const file = new File(['test content'], 'test-image.jpg', { type: 'image/jpeg' });
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;

    await user.upload(fileInput, file);

    await waitFor(() => {
      expect(mockGetImageUrl).toHaveBeenCalledWith(mockFilenameDisk);
      const avatar = screen.getByRole('img');
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveAttribute('src', 'https://example.com/image.jpg');
    });
  });

  it('shows error toast for invalid file type', async () => {
    mockValidateFileImage.mockReturnValue({ isValid: false, wrongFileType: true, wrongFileSize: false });

    render(
      <TestWrapper>
        <UploadCutterImage onChange={mockOnChange} />
      </TestWrapper>
    );

    const file = new File(['test content'], 'test-file.txt', { type: 'text/plain' });
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;

    // Use fireEvent.change to directly trigger the change handler
    fireEvent.change(fileInput, { target: { files: [file] } });

    await waitFor(() => {
      expect(mockValidateFileImage).toHaveBeenCalledWith(file);
      expect(mockToastMessages.error).toHaveBeenCalledWith('invalid-image');
    });
    expect(mockUploadFileService).not.toHaveBeenCalled();
    expect(mockOnChange).not.toHaveBeenCalled();
  });

  it('shows error toast when upload fails', async () => {
    const user = userEvent.setup();
    mockUploadFileService.mockRejectedValue(new Error('Upload failed'));

    render(
      <TestWrapper>
        <UploadCutterImage onChange={mockOnChange} />
      </TestWrapper>
    );

    const file = new File(['test content'], 'test-image.jpg', { type: 'image/jpeg' });
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;

    await user.upload(fileInput, file);

    await waitFor(() => {
      expect(mockToastMessages.error).toHaveBeenCalledWith('common-error');
      expect(mockOnChange).not.toHaveBeenCalled();
    });
  });

  it('clears file input value after processing', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <UploadCutterImage onChange={mockOnChange} />
      </TestWrapper>
    );

    const file = new File(['test content'], 'test-image.jpg', { type: 'image/jpeg' });
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;

    await user.upload(fileInput, file);

    await waitFor(() => {
      expect(fileInput.value).toBe('');
    });
  });

  it('accepts correct image file extensions', () => {
    render(
      <TestWrapper>
        <UploadCutterImage onChange={mockOnChange} />
      </TestWrapper>
    );

    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    expect(fileInput).toHaveAttribute('accept', '.jpg,.jpeg,.png');
  });

  it('only allows single file selection', () => {
    render(
      <TestWrapper>
        <UploadCutterImage onChange={mockOnChange} />
      </TestWrapper>
    );

    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    expect(fileInput).not.toHaveAttribute('multiple');
  });

  it('handles empty file selection gracefully', async () => {
    render(
      <TestWrapper>
        <UploadCutterImage onChange={mockOnChange} />
      </TestWrapper>
    );

    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;

    // Simulate selecting no file
    fireEvent.change(fileInput, { target: { files: [] } });

    expect(mockValidateFileImage).not.toHaveBeenCalled();
    expect(mockUploadFileService).not.toHaveBeenCalled();
    expect(mockOnChange).not.toHaveBeenCalled();
  });

  it('shows camera icon for re-upload after successful upload', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <UploadCutterImage onChange={mockOnChange} />
      </TestWrapper>
    );

    // First upload
    const file1 = new File(['test content 1'], 'test-image-1.jpg', { type: 'image/jpeg' });
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;

    await user.upload(fileInput, file1);

    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(mockFilenameDisk);
    });

    // After upload, there should be a camera icon for re-upload
    await waitFor(() => {
      const cameraIcons = screen.getAllByTestId('CameraAltOutlinedIcon');
      expect(cameraIcons.length).toBeGreaterThan(0);

      // Should have file input for re-upload
      const fileInputs = document.querySelectorAll('input[type="file"]');
      expect(fileInputs.length).toBeGreaterThan(0);
    });
  });

  describe('Image Prop Functionality', () => {
    it('should display existing image when image prop is provided', () => {
      const existingImage = 'existing-image.jpg';
      mockGetImageUrl.mockReturnValue('https://example.com/existing-image.jpg');

      render(
        <TestWrapper>
          <UploadCutterImage onChange={mockOnChange} image={existingImage} />
        </TestWrapper>
      );

      // Should display the existing image
      const avatar = screen.getByRole('img');
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveAttribute('src', 'https://example.com/existing-image.jpg');

      // Should not show upload button
      expect(screen.queryByText('cutter-photo')).not.toBeInTheDocument();

      // Should have camera icon for re-upload
      const cameraIcon = screen.getByTestId('CameraAltOutlinedIcon');
      expect(cameraIcon).toBeInTheDocument();
    });

    it('should not display existing image when image prop is empty string', () => {
      render(
        <TestWrapper>
          <UploadCutterImage onChange={mockOnChange} image="" />
        </TestWrapper>
      );

      // Should show upload button instead
      expect(screen.getByText('cutter-photo')).toBeInTheDocument();
      expect(screen.queryByRole('img')).not.toBeInTheDocument();
    });

    it('should not display existing image when image prop is undefined', () => {
      render(
        <TestWrapper>
          <UploadCutterImage onChange={mockOnChange} />
        </TestWrapper>
      );

      // Should show upload button instead
      expect(screen.getByText('cutter-photo')).toBeInTheDocument();
      expect(screen.queryByRole('img')).not.toBeInTheDocument();
    });

    it('should allow re-upload when existing image is displayed', async () => {
      const user = userEvent.setup();
      const existingImage = 'existing-image.jpg';
      mockGetImageUrl.mockReturnValue('https://example.com/existing-image.jpg');

      render(
        <TestWrapper>
          <UploadCutterImage onChange={mockOnChange} image={existingImage} />
        </TestWrapper>
      );

      // Create a mock file
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      // Find the file input (it should be hidden but accessible)
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      expect(fileInput).toBeInTheDocument();

      // Simulate file selection
      await user.upload(fileInput, file);

      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith(mockFilenameDisk);
      });
    });

    it('should prioritize uploaded images over existing image prop', async () => {
      const user = userEvent.setup();
      const existingImage = 'existing-image.jpg';
      mockGetImageUrl.mockReturnValue('https://example.com/existing-image.jpg');

      render(
        <TestWrapper>
          <UploadCutterImage onChange={mockOnChange} image={existingImage} />
        </TestWrapper>
      );

      // Initially shows existing image
      expect(screen.getByRole('img')).toHaveAttribute('src', 'https://example.com/existing-image.jpg');

      // Upload a new file
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;

      await user.upload(fileInput, file);

      // After upload completes, should show the new uploaded image
      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith(mockFilenameDisk);
      });
    });

    it('should handle null getImageUrl return gracefully', () => {
      const existingImage = 'existing-image.jpg';
      mockGetImageUrl.mockReturnValue(null);

      render(
        <TestWrapper>
          <UploadCutterImage onChange={mockOnChange} image={existingImage} />
        </TestWrapper>
      );

      // Should still display avatar but with fallback icon instead of img
      const avatar = screen.getByTestId('PersonIcon');
      expect(avatar).toBeInTheDocument();
    });
  });
});
