/* eslint-disable no-console */
import { AxiosError } from 'axios';
import { getCookieServer } from 'configs/cookie';
import { NextRequest, NextResponse } from 'next/server';

import { createServerApi } from 'services/api/serverApi';
import { safeJSONParse } from './common';
import { AppConfig } from 'configs/app-config';
import { logger } from './logger';

// Helper function to format body for curl command
function formatCurlBody(body: BodyInit | null | undefined): string | undefined {
  if (!body) return undefined;

  if (typeof body === 'string') {
    try {
      const jsonObj = JSON.parse(body);
      return `'${JSON.stringify(jsonObj)}'`;
    } catch {
      return `'${body.replace(/'/g, `'\\''`)}'`;
    }
  }

  if (body instanceof URLSearchParams) return `'${body.toString()}'`;
  if (body instanceof FormData) return "'<form-data>'";

  return "'<binary-data>'";
}

// Helper function to determine curl data flag
function getCurlDataFlag(contentType?: string): string {
  if (contentType?.includes('application/json')) return '--data';
  if (contentType?.includes('x-www-form-urlencoded')) return '--data-urlencode';
  return '--data';
}

export function toCurl({
  url,
  method = 'GET',
  headers = {},
  body,
}: {
  url: string;
  method?: string;
  headers?: Record<string, string>;
  body: BodyInit | null | undefined;
}) {
  const formattedBody = formatCurlBody(body);
  const contentType = headers['content-type'] || headers['Content-Type'];
  const dataFlag = getCurlDataFlag(contentType);

  const curlParts = [
    `curl -X ${method.toUpperCase()}`,
    `'${url}'`,
    ...Object.entries(headers).map(([key, value]) => `-H '${key}: ${value}'`),
    body ? `${dataFlag} ${formattedBody}` : '',
  ].filter(Boolean);

  return curlParts;
}

// Helper function to prepare request configuration
async function prepareRequestConfig(req: NextRequest, method: string, path: string[]) {
  const token = await getCookieServer('access_token_pkg_house');
  const originalUrl = new URL(req.url);
  const search = originalUrl.search;
  const proxiedUrl = `${path.join('/')}${search}`;
  const body = ['GET', 'HEAD'].includes(method) ? undefined : await req.text();

  const language = (await (getCookieServer<string>('NEXT_LOCALE') ?? 'th')) as unknown as string;

  const headers: Record<string, string> = {
    'content-type': req.headers.get('content-type') ?? 'application/json',
    'Accept-Language': language,
  };

  if (token) {
    headers['authorization'] = `Bearer ${token}`;
  }

  return { proxiedUrl, body, headers };
}

// Helper function to handle 401 unauthorized responses
function handleUnauthorizedResponse(): NextResponse {
  return new NextResponse(null, {
    status: 401,
    headers: {
      'Set-Cookie': 'access_token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly; Secure; SameSite=Strict',
    },
  });
}

// Helper function to create error response object
function createErrorResponse(
  error: unknown,
  path: string[],
  method: string
): { response: NextResponse; shouldClearCookie: boolean } {
  let errorMessage = 'Server Internal Error';
  let statusCode = 500;
  let errorDetails = null;
  let shouldClearCookie = false;

  if (error instanceof AxiosError) {
    errorMessage = error.message;

    if (error.response) {
      statusCode = error.response.status;
      errorDetails = safeJSONParse(error.response.data);

      logger.error('API Error:', {
        message: error.message,
        status: error.response.status,
        data: JSON.stringify(error.response.data),
      });

      shouldClearCookie = statusCode === 401;
    } else if (error.request) {
      errorMessage = 'No response received from server';
      errorDetails = {
        message: 'Request error (details omitted due to circular structure)',
        url: error.config?.url || 'unknown',
        method: error.config?.method ?? 'unknown',
      };
    }
  }

  const errorResponseObject = {
    message: errorMessage,
    status: statusCode,
    path: `/${path.join('/')}`,
    timestamp: new Date().toISOString(),
    method: method,
    details: errorDetails,
  };

  const response = NextResponse.json(errorResponseObject, { status: statusCode });

  return { response, shouldClearCookie };
}

export const cacheApiOptions = {
  'v1/masterdata/edge/location/license-plate-provinces': {
    maxAge: 24 * 60 * 60, // 24 hours
    staleWhileRevalidate: 0.5 * 60 * 60, // 30 minutes
  },
  'v1/durian/varieties': {
    maxAge: 24 * 60 * 60, // 24 hours
    staleWhileRevalidate: 0.5 * 60 * 60, // 30 minutes
  },
};

export async function handler(
  req: NextRequest,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
  path: string[]
): Promise<NextResponse> {
  try {
    const EXTERNAL_API = AppConfig.CONTROLLER_URL;
    const { proxiedUrl, body, headers } = await prepareRequestConfig(req, method, path);

    console.info('\x1b[36m%s\x1b[0m', `------------- Start Curl ${proxiedUrl} -------------`);
    console.info(
      'Curl',
      toCurl({
        url: `${EXTERNAL_API}/${proxiedUrl}`,
        method,
        headers,
        body,
      }).join(' ')
    );
    console.info('\x1b[36m%s\x1b[0m', `------------- End Curl ${proxiedUrl} -------------`);

    const api = await createServerApi(EXTERNAL_API, headers);
    const axiosRes = await api.request({
      url: proxiedUrl,
      method,
      headers,
      data: body,
      responseType: 'text',
    });

    logger.debug('response', {
      status: axiosRes.status,
      data: JSON.stringify(axiosRes.data),
    });

    const response = new NextResponse(axiosRes.data, {
      status: axiosRes.status,
      headers,
    });

    const cacheOption = cacheApiOptions[proxiedUrl as keyof typeof cacheApiOptions];

    if (cacheOption) {
      response.headers.set(
        'Cache-Control',
        `public, max-age=${cacheOption.maxAge}, stale-while-revalidate=${cacheOption.staleWhileRevalidate}`
      );
    } else {
      response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    }

    return response;
  } catch (error) {
    const { response, shouldClearCookie } = createErrorResponse(error, path, method);

    if (shouldClearCookie) {
      return handleUnauthorizedResponse();
    }

    return response;
  }
}
