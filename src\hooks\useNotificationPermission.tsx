import { useState, useEffect, useCallback } from 'react';

type PermissionState = NotificationPermission; // "default" | "granted" | "denied"

export function useNotificationPermission() {
  const [permission, setPermission] = useState<PermissionState>(() => {
    if (typeof window === 'undefined' || !('Notification' in window)) {
      return 'denied';
    }
    return Notification.permission;
  });

  useEffect(() => {
    if (typeof window === 'undefined' || !('Notification' in window)) return;
    const onChange = () => setPermission(Notification.permission);
    const id = setInterval(onChange, 1000 * 10);
    return () => clearInterval(id);
  }, []);

  const requestPermission = useCallback(async (): Promise<PermissionState> => {
    if (typeof window === 'undefined' || !('Notification' in window)) {
      return 'denied';
    }
    const result = await Notification.requestPermission();
    setPermission(result);
    return result;
  }, []);

  return { permission, requestPermission };
}
