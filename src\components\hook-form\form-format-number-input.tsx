import { CircularProgress, FormControl, InputAdornment, TextField, TextFieldProps, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { JSX, ReactElement } from 'react';
import { Control, Controller, FieldErrors, FieldValues, Path } from 'react-hook-form';
import { NumberFormatBase } from 'react-number-format';
import { formatOrchardNo } from 'utils/format';

export interface FormNumberFormatInputProps<TFormValues extends FieldValues> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  errors?: FieldErrors<TFormValues>;
  label: string;
  required?: boolean;
  requiredMessage?: string;
  placeholder?: string;
  variant?: TextFieldProps['variant'];
  max?: number;
  min?: number;
  patternMessage?: string;
  pattern?: RegExp;
  loading?: boolean;
  decimalScale?: number;
  validate?: Record<string, (value: unknown) => boolean | string>;
  startAdornment?: JSX.Element;
  format?: string;
  disabled?: boolean;
  defaultValue?: string;
  helperText?: string;
  maxLength?: number;
  mask?: string;
  type?: 'text' | 'tel' | 'password';
  minLength?: number;
  minLengthMessage?: string;
}

export function FormNumberFormatInput<TFormValues extends FieldValues>({
  name,
  control,
  errors,
  label,
  required = false,
  requiredMessage,
  placeholder,
  variant,
  max,
  loading = false,
  min,
  validate,
  startAdornment,
  disabled = false,
  defaultValue,
  helperText,
  maxLength,
  type = 'tel',
  minLength,
  minLengthMessage,
}: FormNumberFormatInputProps<TFormValues>): ReactElement {
  const formT = useTranslations('form');
  const rules = {
    ...(required && { required: requiredMessage }),
    ...(validate && { validate }),
    ...(min && { min: { value: min, message: `${label} ${formT('min')} ${min}` } }),
    ...(max && { max: { value: max, message: `${label} ${formT('max')} ${max}` } }),
    ...(minLength && {
      minLength: { value: minLength, message: minLengthMessage || `${label} must be at least ${minLength} characters` },
    }),
  };

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field }) => (
        <FormControl fullWidth error={!!errors?.[name]}>
          <Typography sx={{ fontSize: '18px', mb: 1 }} variant="caption">
            {label}{' '}
            <Typography sx={{ visibility: required ? 'visible' : 'hidden' }} variant="caption" color="error">
              *
            </Typography>
          </Typography>
          <NumberFormatBase
            valueIsNumericString
            type={type}
            disabled={disabled}
            placeholder={placeholder}
            helperText={!!errors?.[name] ? String(errors?.[name]?.message) : helperText}
            error={!!errors?.[name]}
            defaultValue={defaultValue}
            value={field.value || ''}
            customInput={TextField}
            fullWidth
            variant={variant}
            format={(originValue) => {
              if (!originValue) return '';

              const val = maxLength ? originValue.slice(0, maxLength) : originValue;

              return formatOrchardNo(val);
            }}
            minLength={minLength}
            maxLength={maxLength}
            onValueChange={({ value }) => {
              if (maxLength && value.length > maxLength) {
                return;
              }
              // FIXME: this line of code is temp solution to prevent validate on reset, try to find another solution
              if (!value) return;
              field.onChange(value);
            }}
            slotProps={{
              input: {
                startAdornment: startAdornment ? (
                  <InputAdornment position="start">{startAdornment}</InputAdornment>
                ) : null,
                endAdornment: loading ? (
                  <InputAdornment position="end">
                    <CircularProgress size={20} />
                  </InputAdornment>
                ) : null,
              },
            }}
          />
        </FormControl>
      )}
    />
  );
}
