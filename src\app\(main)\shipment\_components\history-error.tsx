'use client';

import { Box, Typography, Button } from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC } from 'react';
import { theme } from 'styles/theme';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import RefreshIcon from '@mui/icons-material/Refresh';

interface HistoryErrorProps {
  onRetry?: () => void;
  error?: string;
}

export const HistoryError: FC<HistoryErrorProps> = ({ onRetry, error }) => {
  const commonT = useTranslations('common');
  const shipmentT = useTranslations('shipment');

  return (
    <Box
      sx={{
        backgroundColor: theme.palette.customColors.white,
        borderRadius: '8px',
        border: `1px solid ${theme.palette.customColors.gray5}`,
        p: 4,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '300px',
        gap: 2,
      }}
    >
      <ErrorOutlineIcon 
        sx={{ 
          fontSize: 48, 
          color: theme.palette.customColors.toastError,
          mb: 1 
        }} 
      />
      
      <Typography variant="h6" color="text.primary" textAlign="center">
        {shipmentT('history-error-title')}
      </Typography>
      
      <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ maxWidth: 400 }}>
        {error || commonT('common-error')}
      </Typography>
      
      {onRetry && (
        <Button
          variant="contained"
          startIcon={<RefreshIcon />}
          onClick={onRetry}
          sx={{ mt: 2 }}
        >
          {commonT('retry')}
        </Button>
      )}
    </Box>
  );
};
