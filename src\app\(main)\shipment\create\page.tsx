'use client';

import { Box, CircularProgress } from '@mui/material';
import { LoadingPage } from 'app/_ui/loading-page';
import { PromptDialog } from 'components';
import { generateDefaultSearchParams } from 'hooks/useMenuItems';
import { headerHeight } from 'layouts/main/constant';
import { useTranslations } from 'next-intl';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { Suspense } from 'react';
import { clientRoutes } from 'routes/client-routes';
import { FormShipmentStepEnum, useCreateShipmentStore } from 'store/useCreateShipmentStore';
import { useGlobalStore } from 'store/useGlobalStore';
import { theme } from 'styles/theme';

// Dynamic imports with loading fallbacks
const CreateShipmentTable = dynamic(
  () => import('../_container/product-selected-table').then((mod) => ({ default: mod.CreateShipmentTable })),
  {
    loading: () => <LoadingPage />,
  }
);

const ShipmentFormStep = dynamic(
  () => import('../_container/shipment-form-step').then((mod) => ({ default: mod.ShipmentFormStep })),
  {
    loading: () => <LoadingPage />,
  }
);

const Index = () => {
  const { formStep } = useCreateShipmentStore();
  const { setLoadingPage } = useGlobalStore();
  const { showWarningDialog, setShowWarningDialog } = useCreateShipmentStore();

  const commonT = useTranslations('common');
  const shipmentT = useTranslations('shipment');
  const router = useRouter();

  const onConfirmDialog = () => {
    setShowWarningDialog(false);
    setLoadingPage(true);
    const shipmentPathName = `${clientRoutes.shipment}?${generateDefaultSearchParams()}`;
    router.push(shipmentPathName);
  };

  return (
    <>
      <Box
        sx={{
          width: '100%',
          visibility: formStep === FormShipmentStepEnum.SelectedProductIds ? 'visible' : 'hidden',
          flex: 1,
          flexDirection: 'column',
          position: 'absolute',
          top: 0,
          left: 0,
          zIndex: formStep === FormShipmentStepEnum.SelectedProductIds ? 5 : -1,
          height: `calc(100vh - ${headerHeight}px)`,
          background: theme.palette.customColors.lightGray,
        }}
      >
        <CreateShipmentTable />
      </Box>
      <Box
        sx={{
          width: '100%',
          visibility: formStep !== FormShipmentStepEnum.SelectedProductIds ? 'visible' : 'hidden',
          flex: 1,
          flexDirection: 'column',
          position: 'absolute',
          top: 0,
          left: 0,
          minHeight: '1px',
          background: theme.palette.customColors.lightGray,
          zIndex: formStep !== FormShipmentStepEnum.SelectedProductIds ? 5 : -1,
        }}
      >
        <Suspense fallback={<CircularProgress />}>
          <ShipmentFormStep />
        </Suspense>
      </Box>

      <PromptDialog
        open={showWarningDialog}
        onClose={() => setShowWarningDialog(false)}
        onConfirm={onConfirmDialog}
        confirmText={commonT('confirm')}
        title={shipmentT('warning-cancel-shipment-title')}
        content={shipmentT('warning-cancel-shipment')}
      />
    </>
  );
};

export default Index;
