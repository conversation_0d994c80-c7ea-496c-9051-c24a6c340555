import { useTheme } from '@mui/material';
import SvgIcon, { SvgIconProps } from '@mui/material/SvgIcon';
import { FC } from 'react';

export const CustomSortIcon: FC<SvgIconProps & { asc?: boolean; desc?: boolean }> = (props) => {
  const { asc, desc, ...restProps } = props;

  const theme = useTheme();

  return (
    <SvgIcon {...restProps} sx={{ width: 22, height: 22 }}>
      <path
        d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9z"
        fill={asc ? theme.palette.primary.main : theme.palette.customColors.gray}
        transform="scale(0.9) translate(3, 1)"
      />
      {/* Down Arrow - slightly scaled down and moved up */}
      <path
        d="M12 18.17L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15z"
        fill={desc ? theme.palette.primary.main : theme.palette.customColors.gray}
        transform="scale(0.9) translate(3, -1)"
      />
    </SvgIcon>
  );
};
