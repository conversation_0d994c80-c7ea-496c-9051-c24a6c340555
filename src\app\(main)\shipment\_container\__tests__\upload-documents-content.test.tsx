import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { UploadDocumentsContent } from '../upload-documents-content';
import { useCreateShipmentStore } from 'store/useCreateShipmentStore';
import { validateFile } from 'utils';
import toastMessages from 'utils/toastMessages';
import { logger } from 'utils/logger';

// Type for mock Image component props
interface MockImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  [key: string]: unknown;
}

// Type for mock UploadProgressUI component props
interface MockUploadProgressUIProps {
  fileName: string;
  onDelete: (id: string) => void;
  fileId: string;
}

// Type for mock useCreateShipmentStore return value
interface MockCreateShipmentStore {
  additionalFiles: Array<{
    id: string;
    name: string;
    size: number;
    type: string;
    url: string;
  }>;
  updateUploadFile: jest.MockedFunction<(file: {
    id: string;
    name: string;
    size: number;
    type: string;
    url: string;
    file: File;
  }) => Promise<void>>;
  deleteUploadFile: jest.MockedFunction<(id: string) => void>;
}

// Mock dependencies
jest.mock('store/useCreateShipmentStore');
jest.mock('utils', () => ({
  validateFile: jest.fn(),
}));
jest.mock('utils/toastMessages');
jest.mock('utils/logger');
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'mock-uuid-123'),
}));

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: jest.fn((namespace) => (key: string) => `${namespace}.${key}`),
}));

// Mock next/image
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: MockImageProps) {
    return <img src={src} alt={alt} {...props} />;
  };
});

// Mock MUI theme
jest.mock('@mui/material', () => ({
  ...jest.requireActual('@mui/material'),
  useTheme: () => ({
    palette: {
      customColors: {
        neutralBorder: '#e0e0e0',
      },
    },
  }),
}));

// Mock upload progress component
jest.mock('components/upload-file-progress/upload-file-progress', () => {
  return function MockUploadProgressUI({ fileName, onDelete, fileId }: MockUploadProgressUIProps) {
    return (
      <div data-testid={`upload-progress-${fileId}`}>
        <span>{fileName}</span>
        <button onClick={() => onDelete(fileId)}>Delete</button>
      </div>
    );
  };
});

const mockUseCreateShipmentStore = useCreateShipmentStore as jest.MockedFunction<typeof useCreateShipmentStore>;
const mockValidateFile = validateFile as jest.MockedFunction<typeof validateFile>;
const mockToastMessages = toastMessages as jest.Mocked<typeof toastMessages>;
const mockLogger = logger as jest.Mocked<typeof logger>;

describe('UploadDocumentsContent', () => {
  const mockUpdateUploadFile = jest.fn();
  const mockDeleteUploadFile = jest.fn();
  const mockAdditionalFiles = [
    {
      id: 'file-1',
      name: 'document1.pdf',
      size: 1024,
      type: 'application/pdf',
      url: 'http://example.com/doc1.pdf',
    },
    {
      id: 'file-2',
      name: 'document2.jpg',
      size: 2048,
      type: 'image/jpeg',
      url: 'http://example.com/doc2.jpg',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseCreateShipmentStore.mockReturnValue({
      additionalFiles: [],
      updateUploadFile: mockUpdateUploadFile,
      deleteUploadFile: mockDeleteUploadFile,
    } as MockCreateShipmentStore);

    mockValidateFile.mockReturnValue({ isValid: true, wrongFileType: false, wrongFileSize: false });
    mockUpdateUploadFile.mockResolvedValue(undefined);
  });

  const renderComponent = (props = {}) => {
    return render(<UploadDocumentsContent {...props} />);
  };

  describe('Component Rendering', () => {
    it('renders upload area with correct elements', () => {
      renderComponent();

      expect(screen.getByText('shipment.upload-description')).toBeInTheDocument();
      expect(screen.getByText('shipment.upload-placeholder')).toBeInTheDocument();
      expect(screen.getByText('shipment.upload-btn-title')).toBeInTheDocument();
      expect(screen.getByText('list-expect-document.title')).toBeInTheDocument();
    });

    it('renders empty state when no files uploaded', () => {
      renderComponent();

      expect(screen.getByText('shipment.no-file-uploaded')).toBeInTheDocument();
      expect(screen.getByAltText('empty-state-icon')).toBeInTheDocument();
    });

    it('renders uploaded files when files exist', () => {
      mockUseCreateShipmentStore.mockReturnValue({
        additionalFiles: mockAdditionalFiles,
        updateUploadFile: mockUpdateUploadFile,
        deleteUploadFile: mockDeleteUploadFile,
      } as MockCreateShipmentStore);

      renderComponent();

      expect(screen.getByTestId('upload-progress-file-1')).toBeInTheDocument();
      expect(screen.getByTestId('upload-progress-file-2')).toBeInTheDocument();
      expect(screen.getByText('document1.pdf')).toBeInTheDocument();
      expect(screen.getByText('document2.jpg')).toBeInTheDocument();
    });
  });

  describe('Single File Upload', () => {
    it('handles single valid file upload successfully', async () => {
      const user = userEvent.setup();
      const { container } = renderComponent();

      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
      const fileInput = container.querySelector('input[type="file"]') as HTMLInputElement;

      await user.upload(fileInput, file);

      await waitFor(() => {
        expect(mockValidateFile).toHaveBeenCalledWith(file);
        expect(mockUpdateUploadFile).toHaveBeenCalledWith({
          id: 'mock-uuid-123',
          name: 'test.pdf',
          size: file.size,
          type: 'application/pdf',
          url: '',
          file,
        });
      });
    });

    it('handles invalid file validation', async () => {
      mockValidateFile.mockReturnValue({ isValid: false, wrongFileType: true, wrongFileSize: false });
      const { container } = renderComponent();

      const file = new File(['test content'], 'test.txt', { type: 'text/plain' });
      const fileInput = container.querySelector('input[type="file"]') as HTMLInputElement;

      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        expect(mockValidateFile).toHaveBeenCalledWith(file);
        expect(mockToastMessages.error).toHaveBeenCalledWith('form.invalid-file');
        expect(mockUpdateUploadFile).not.toHaveBeenCalled();
      });
    });
  });

  describe('Multiple File Upload', () => {
    it('supports multiple file selection', () => {
      const { container } = renderComponent();

      const fileInput = container.querySelector('input[type="file"]') as HTMLInputElement;

      expect(fileInput).toHaveAttribute('multiple');
    });

    it('handles multiple valid files upload successfully', async () => {
      const user = userEvent.setup();
      const { container } = renderComponent();

      const files = [
        new File(['content1'], 'doc1.pdf', { type: 'application/pdf' }),
        new File(['content2'], 'doc2.jpg', { type: 'image/jpeg' }),
        new File(['content3'], 'doc3.png', { type: 'image/png' }),
      ];

      const fileInput = container.querySelector('input[type="file"]') as HTMLInputElement;

      await user.upload(fileInput, files);

      await waitFor(() => {
        expect(mockValidateFile).toHaveBeenCalledTimes(3);
        expect(mockUpdateUploadFile).toHaveBeenCalledTimes(3);
      });
    });

    it('handles mixed valid and invalid files', async () => {
      mockValidateFile
        .mockReturnValueOnce({ isValid: true, wrongFileType: false, wrongFileSize: false })
        .mockReturnValueOnce({ isValid: false, wrongFileType: true, wrongFileSize: false })
        .mockReturnValueOnce({ isValid: true, wrongFileType: false, wrongFileSize: false });

      const { container } = renderComponent();

      const files = [
        new File(['content1'], 'doc1.pdf', { type: 'application/pdf' }),
        new File(['content2'], 'doc2.txt', { type: 'text/plain' }),
        new File(['content3'], 'doc3.jpg', { type: 'image/jpeg' }),
      ];

      const fileInput = container.querySelector('input[type="file"]') as HTMLInputElement;

      fireEvent.change(fileInput, { target: { files } });

      await waitFor(() => {
        expect(mockValidateFile).toHaveBeenCalledTimes(3);
        expect(mockUpdateUploadFile).toHaveBeenCalledTimes(2); // Only valid files
        expect(mockToastMessages.error).toHaveBeenCalledWith('form.invalid-file');
      });
    });

    it('limits files to maximum allowed (20 files)', async () => {
      const user = userEvent.setup();
      // Mock store with 18 existing files
      const existingFiles = Array.from({ length: 18 }, (_, i) => ({
        id: `existing-${i}`,
        name: `existing${i}.pdf`,
        size: 1024,
        type: 'application/pdf',
        url: '',
      }));

      mockUseCreateShipmentStore.mockReturnValue({
        additionalFiles: existingFiles,
        updateUploadFile: mockUpdateUploadFile,
        deleteUploadFile: mockDeleteUploadFile,
      } as MockCreateShipmentStore);

      const { container } = renderComponent();

      // Try to upload 5 more files (should only accept 2 due to 20 file limit)
      const files = Array.from({ length: 5 }, (_, i) =>
        new File([`content${i}`], `doc${i}.pdf`, { type: 'application/pdf' })
      );

      const fileInput = container.querySelector('input[type="file"]') as HTMLInputElement;

      await user.upload(fileInput, files);

      await waitFor(() => {
        expect(mockValidateFile).toHaveBeenCalledTimes(2); // Only first 2 files processed
        expect(mockUpdateUploadFile).toHaveBeenCalledTimes(2);
      });
    });

    it('shows error when at maximum file capacity', async () => {
      const user = userEvent.setup();
      // Mock store with 20 existing files (at max capacity)
      const existingFiles = Array.from({ length: 20 }, (_, i) => ({
        id: `existing-${i}`,
        name: `existing${i}.pdf`,
        size: 1024,
        type: 'application/pdf',
        url: '',
      }));

      mockUseCreateShipmentStore.mockReturnValue({
        additionalFiles: existingFiles,
        updateUploadFile: mockUpdateUploadFile,
        deleteUploadFile: mockDeleteUploadFile,
      } as MockCreateShipmentStore);

      const { container } = renderComponent();

      const file = new File(['content'], 'doc.pdf', { type: 'application/pdf' });
      const fileInput = container.querySelector('input[type="file"]') as HTMLInputElement;

      await user.upload(fileInput, file);

      await waitFor(() => {
        expect(mockToastMessages.error).toHaveBeenCalledWith('form.invalid-file');
        expect(mockUpdateUploadFile).not.toHaveBeenCalled();
      });
    });
  });

  describe('Error Handling', () => {
    it('handles upload service error', async () => {
      const user = userEvent.setup();
      mockUpdateUploadFile.mockRejectedValue(new Error('Upload failed'));
      const { container } = renderComponent();

      const file = new File(['content'], 'test.pdf', { type: 'application/pdf' });
      const fileInput = container.querySelector('input[type="file"]') as HTMLInputElement;

      await user.upload(fileInput, file);

      await waitFor(() => {
        expect(mockLogger.error).toHaveBeenCalledWith(
          'Error uploading file "test.pdf":',
          { error: expect.any(Error) }
        );
        expect(mockDeleteUploadFile).toHaveBeenCalledWith('mock-uuid-123');
      });
    });

    it('clears input value after processing', async () => {
      const user = userEvent.setup();
      const { container } = renderComponent();

      const file = new File(['content'], 'test.pdf', { type: 'application/pdf' });
      const fileInput = container.querySelector('input[type="file"]') as HTMLInputElement;

      await user.upload(fileInput, file);

      await waitFor(() => {
        expect(fileInput.value).toBe('');
      });
    });
  });

  describe('Component Props', () => {
    it('applies custom minHeight when provided', () => {
      const { container } = renderComponent({ minHeight: 500 });

      const mainBox = container.querySelector('[data-testid="upload-documents-container"]') ||
                     container.firstChild as HTMLElement;
      
      // Check if minHeight style is applied (480px = 500 - 20)
      expect(mainBox).toHaveStyle('min-height: 480px');
    });
  });
});
