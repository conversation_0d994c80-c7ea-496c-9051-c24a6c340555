import { createShipmentEventService } from 'services/shipment.service';
import { CreateShipment } from 'types';
import { useMutation, UseMutationOptions } from '@tanstack/react-query';

export function useCreateShipmentMutate(options?: UseMutationOptions<void, Error, CreateShipment, unknown>) {
  return useMutation({
    mutationFn: (formValues: CreateShipment) => createShipmentEventService(formValues),
    mutationKey: ['useCreateShipmentMutate'],
    ...options
  });
}
