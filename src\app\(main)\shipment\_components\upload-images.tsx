import CameraAltOutlinedIcon from '@mui/icons-material/CameraAltOutlined';
import ClearRoundedIcon from '@mui/icons-material/ClearRounded';
import { Box, Button, Grow, IconButton, Skeleton, Typography, styled } from '@mui/material';
import { ImageReviewModal } from 'components';
import { IMAGE_EXTENSIONS } from 'constant/common';
import { useTranslations } from 'next-intl';
import React from 'react';
import { UploadFile, useCreateShipmentStore } from 'store/useCreateShipmentStore';
import { theme } from 'styles/theme';
import { getImageUrl, validateFileImage } from 'utils';
import toastMessages from 'utils/toastMessages';
import { v4 } from 'uuid';

export async function urlToFile(url: string, filename: string): Promise<File> {
  const res = await fetch(url);
  const blob = await res.blob();
  return new File([blob], filename, { type: blob.type });
}

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

export interface ImagePreview {
  file?: File;
  preview: string;
  id?: string;
}

interface UploadImageProps {
  initialImages?: string[];
  onChange?: (imgString: string[]) => void;
  multiple?: boolean;
  maxFiles?: number;
}

const UploadImage: React.FC<UploadImageProps> = () => {
  const { uploadShipmentPhoto, shipmentPhotos, deleteShipmentPhoto } = useCreateShipmentStore();

  const commonT = useTranslations('common');
  const formT = useTranslations('form');

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;

    if (!files || files.length === 0) return;

    const maxFiles = 3;
    const remainingSlots = maxFiles - shipmentPhotos.length;

    // If already at max capacity, do nothing
    if (remainingSlots <= 0) {
      e.target.value = '';
      return;
    }

    const allFiles = Array.from(files);
    const validFiles: File[] = [];
    let processedCount = 0;

    for (const file of allFiles) {
      if (validFiles.length >= remainingSlots) break;

      const { isValid } = validateFileImage(file);
      processedCount++;

      if (isValid) {
        validFiles.push(file);
      }
    }

    const validCount = validFiles.length;
    const invalidCount = processedCount - validCount;

    if (invalidCount > 0) {
      toastMessages.error(formT('invalid-image'));
    }

    if (validFiles.length === 0) {
      e.target.value = '';
      return;
    }

    const uploadPromises = validFiles.map(async (file) => {
      const imgId = v4();
      const fileUpload = {
        id: imgId,
        name: file.name,
        size: file.size,
        type: file.type,
        url: '',
        file,
      };

      try {
        await uploadShipmentPhoto(fileUpload);
      } catch {
        deleteShipmentPhoto(fileUpload.id);
        throw new Error(file.name);
      }
    });

    const results = await Promise.allSettled(uploadPromises);
    const failedUploads = results.filter((result) => result.status === 'rejected');

    if (failedUploads.length > 0) {
      toastMessages.error(commonT('common-error'));
    }

    e.target.value = '';
  };

  const handleRemove = (idx: string) => {
    deleteShipmentPhoto(idx);
  };

  const renderPhotoItem = (img: UploadFile) => {
    if (img.isUploading) {
      return <Skeleton variant="rectangular" sx={{ borderRadius: '4px' }} width={122} height={120} />;
    }
    return (
      <>
        <ImageReviewModal imageUrl={getImageUrl(img.url)} isLocalImage={true} imageSize={120} />
        <IconButton
          size="small"
          sx={{ position: 'absolute', right: 0, top: 0, p: 0 }}
          color="error"
          disabled={img.isUploading}
          onClick={() => handleRemove(img.id)}
        >
          <ClearRoundedIcon fontSize="inherit" color="inherit" />
        </IconButton>
      </>
    );
  };

  return (
    <Box sx={{ width: '100%', display: 'flex', gap: '12px' }}>
      {shipmentPhotos.length < 3 && (
        <Button
          component="label"
          role={undefined}
          sx={{
            width: '120px',
            height: '120px',
            border: `1px dashed ${theme.palette.customColors.neutralBorder}`,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            position: 'relative',
          }}
          variant="outlined"
        >
          <VisuallyHiddenInput
            style={{
              width: '100%',
              height: '100%',
              position: 'absolute',
              right: 0,
              bottom: 0,
              zIndex: 2,
            }}
            accept={IMAGE_EXTENSIONS.join(',')}
            type="file"
            multiple={true}
            onChange={handleFileChange}
          />
          <CameraAltOutlinedIcon sx={{ color: theme.palette.customColors.black }} />
          <Typography
            variant="caption"
            sx={{ color: theme.palette.customColors.black }}
          >{`${3 - shipmentPhotos.length}/3`}</Typography>
          <Typography variant="caption" fontSize="14px" color="text.secondary">
            ({commonT('optional')})
          </Typography>
        </Button>
      )}

      {shipmentPhotos.map((img, idx) => (
        <Grow in={true} timeout={100 + idx * 50} key={`${img.id}`}>
          <Box
            sx={{
              width: '120px',
              height: '120px',
              border: `1px dashed solid`,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              position: 'relative',
            }}
          >
            {renderPhotoItem(img)}
          </Box>
        </Grow>
      ))}
    </Box>
  );
};

export default UploadImage;
