import { OcrResponse } from 'types';

import { apiService } from './api/clientApi';
import { AppConfig } from 'configs/app-config';

const externalNextRoute = AppConfig.EXTERNAL_NEXT_ROUTE;

export type UploadFileResponse = {
  data: {
    id: string;
    filenameDisk: string;
  };
};

export const uploadOcaDocumentPtpService = (fileUrl: string): Promise<{ data: OcrResponse }> => {
  return apiService.post(externalNextRoute + '/v1/durian/events/packing/receipt/scan', { imageUrl: fileUrl });
};

export const exportDetailPtpService = (receiptNumber: string): Promise<{ data: OcrResponse }> =>
  apiService.post(externalNextRoute + '/v1/durian/events/packing/receipt/prompt', {
    receiptNumber,
  });

export const getQrCodeBatchLotService = (
  quantity: number,
  nameOfExportingCompany?: string,
): Promise<{ data: { qrId: string; batchlot: string; qrUrl: string, id: string }[] }> =>
  apiService.post(externalNextRoute + '/v1/durian/events/packing/qr-code', { quantity, nameOfExportingCompany: Array(quantity).fill(nameOfExportingCompany) });
