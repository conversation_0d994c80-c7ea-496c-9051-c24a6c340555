'use client';

import { Checkbox as MuiCheckbox, CheckboxProps, FormControl, FormControlLabel } from '@mui/material';
import { FC, ReactNode } from 'react';

interface Props extends CheckboxProps {
  loading?: boolean;
  label?: ReactNode;
}

export const Checkbox: FC<Props> = ({ loading = false, label, ...props }) => {
  return (
    <FormControl fullWidth sx={{ position: 'relative' }}>
      <FormControlLabel control={<MuiCheckbox {...props} disabled={props.disabled || loading} />} label={label} />
    </FormControl>
  );
};
