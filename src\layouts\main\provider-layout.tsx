'use client';

import { CssBaseline, ThemeProvider } from '@mui/material';
import { QueryClientProvider } from '@tanstack/react-query';
import { ClientOnly } from 'components/client-only';
import ClientLocalizationProvider from 'configs/client-localization-provider';
import NotificationRegistry from 'configs/notification-registry';
import queryClient from 'configs/query-client';
import { SnackbarProvider } from 'notistack';
import { useEffect, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { getCookieLocaleService } from 'services/internal.service';
import { theme } from 'styles/theme';
import { User } from 'types/user';
import { logger } from 'utils/logger';
import { AuthLayout } from '../auth/auth-layout';
import { Layout } from './client-layout';
import { ErrorFallback } from './components';
import { customToast } from './components/toast';
import { setCookieLocale } from 'utils/cookie-client';
import { useGlobalStore } from 'store/useGlobalStore';
import { FEATURE_FLAGS } from 'hooks/useFeatureFlag';
import { useFeatureFlagEnabled } from 'posthog-js/react';
import { LoadingScreen } from 'components/LoadingScreen';
import { usePathname } from 'next/navigation';
import { authRoutes, clientRoutes, protectedPaths } from 'routes/client-routes';
import { basePathProd } from 'configs/app-config';

interface ProviderLayoutProps {
  children: React.ReactNode;
  user?: User;
  isLogin?: boolean;
}

export const ORIGINAL_TEXT_EN = 'detect-language';

export const ProviderLayout = ({ children, user, isLogin }: Readonly<ProviderLayoutProps>) => {
  const setLocaleStore = useGlobalStore((state) => state.setLocale);
  const enabledFeatCheck = useFeatureFlagEnabled(FEATURE_FLAGS.FEAT_CHECK);

  const pathname = usePathname();

  const [featCheck, setFeatCheck] = useState<boolean>();
  const [checkAuth, setCheckAuth] = useState<boolean>(false);

  useEffect(() => {
    const isProtected = protectedPaths.some((protectPath) => {
      if (protectPath.exact) {
        return protectPath.path === pathname;
      }
      return pathname?.startsWith(protectPath.path);
    });

    if (isProtected && !isLogin && pathname) {
      if (pathname === clientRoutes.home) {
        document.location.href = `${basePathProd}${authRoutes.login}`;
        return;
      }
      document.location.href = `/fe/packaging-house${authRoutes.login}?callbackUrl=${encodeURIComponent(basePathProd + pathname)}`;
    }

    setCheckAuth(true);
  }, [pathname, isLogin]);

  useEffect(() => {
    if (enabledFeatCheck) {
      setFeatCheck(true);
    }

    setTimeout(() => {
      if (enabledFeatCheck === undefined) {
        setFeatCheck(true);
      }
    }, 1000);
  }, [enabledFeatCheck]);

  useEffect(() => {
    getCookieLocaleService().then((response) => {
      if (response.locale) {
        setCookieLocale(response.locale);
        setLocaleStore(response.locale);
      }
    });
  }, [setLocaleStore]);

  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={(error, info) => {
        logger.error('Error caught by react-error-boundary:', { error, info });
      }}
    >
      <QueryClientProvider client={queryClient}>
        <ClientLocalizationProvider>
          <ThemeProvider theme={theme}>
            <CssBaseline />
            <SnackbarProvider
              autoHideDuration={3000}
              preventDuplicate
              maxSnack={3}
              anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
              Components={{
                success: customToast.success,
                error: customToast.error,
                warning: customToast.warning,
                info: customToast.info,
              }}
            >
              {!featCheck && !checkAuth && <LoadingScreen />}
              {featCheck && checkAuth && (
                <ClientOnly>
                  <span data-translate-probe style={{ visibility: 'hidden', position: 'fixed', top: 0, left: 0 }}>
                    {ORIGINAL_TEXT_EN}
                  </span>

                  {isLogin ? (
                    <div style={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }} translate="no">
                      <NotificationRegistry />
                      <Layout user={user}>{children}</Layout>
                    </div>
                  ) : (
                    <AuthLayout>{children}</AuthLayout>
                  )}
                </ClientOnly>
              )}
            </SnackbarProvider>
          </ThemeProvider>
        </ClientLocalizationProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};
