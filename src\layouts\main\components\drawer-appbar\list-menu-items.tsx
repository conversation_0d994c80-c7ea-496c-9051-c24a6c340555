'use client';

import { Box, List, Typography } from '@mui/material';
import { FC } from 'react';

import { useMenuItems } from 'hooks/useMenuItems';
import { SidebarMenuItem } from './sidebar-menu-item';

interface ListMenuItemsProps {
  isOpen: boolean;
}

export const ListMenuItems: FC<ListMenuItemsProps> = ({ isOpen }) => {
  const { menuItemsTop } = useMenuItems();

  const isDev = window.__ENV__?.NEXT_PUBLIC_ENV === 'development';
  const isStaging = window.__ENV__?.NEXT_PUBLIC_ENV === 'staging';

  const renderVersion = () => {
    if (typeof window === 'undefined') {
      return '';
    }

    const version = window.__ENV__?.NEXT_PUBLIC_WEB_VERSION ?? '';

    if (isDev) {
      return isOpen ? version : `${version.slice(0, 7)}`;
    } else if (isStaging) {
      return `${version}`;
    }
    return `${version?.split('-prod')[0] || 'v1.0.0'}`;
  };

  return (
    <List
      disablePadding
      sx={{
        mt: '24px',
        height: '100%',
        display: 'flex',
        justifyContent: 'space-between',
        flexDirection: 'column',
        color: 'text.primary',
      }}
      translate="no"
    >
      <Box sx={{ width: '100%' }}>
        {menuItemsTop.map((item) =>
          item.hidden ? null : <SidebarMenuItem key={item.itemKey} item={item} isOpen={isOpen} />
        )}
      </Box>
      <Typography
        variant="caption"
        fontWeight={500}
        sx={{ width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 3, fontSize: '12px' }}
      >
        {renderVersion()}
      </Typography>
    </List>
  );
};
