import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { DetailRow } from '../detail-row';

// Mock theme
jest.mock('styles/theme', () => ({
  theme: {
    palette: {
      customColors: {
        gray5: '#e0e0e0',
      },
    },
  },
}));

describe('DetailRow', () => {
  it('renders title and content correctly', () => {
    render(<DetailRow title="Test Title" content="Test Content" />);

    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('displays default value when content is empty', () => {
    render(<DetailRow title="Test Title" />);

    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('--')).toBeInTheDocument();
  });

  it('displays default value when content is null or undefined', () => {
    render(<DetailRow title="Test Title" content={null} />);

    expect(screen.getByText('--')).toBeInTheDocument();
  });

  it('renders ReactNode as title', () => {
    const titleNode = <span data-testid="custom-title">Custom Title Component</span>;

    render(<DetailRow title={titleNode} content="Content" />);

    expect(screen.getByTestId('custom-title')).toBeInTheDocument();
    expect(screen.getByText('Custom Title Component')).toBeInTheDocument();
  });

  it('renders ReactNode as content', () => {
    const contentNode = <div data-testid="custom-content">Custom Content Component</div>;

    render(<DetailRow title="Title" content={contentNode} />);

    expect(screen.getByTestId('custom-content')).toBeInTheDocument();
    expect(screen.getByText('Custom Content Component')).toBeInTheDocument();
  });

  it('calls onClick when clicked', () => {
    const handleClick = jest.fn();

    render(<DetailRow title="Test Title" content="Content" onClick={handleClick} />);

    const container = screen.getByText('Test Title').closest('div');
    fireEvent.click(container!);

    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('applies capitalize text transformation when isCapitalize is true', () => {
    render(<DetailRow title="Title" content="test content" isCapitalize={true} />);

    const contentElement = screen.getByText('test content');
    expect(contentElement).toHaveStyle({ textTransform: 'capitalize' });
  });

  it('does not apply capitalize when isCapitalize is false', () => {
    render(<DetailRow title="Title" content="test content" isCapitalize={false} />);

    const contentElement = screen.getByText('test content');
    expect(contentElement).toHaveStyle({ textTransform: '' });
  });

  it('applies full width to content when contentFullWidth is true', () => {
    render(<DetailRow title="Title" content="Content" contentFullWidth={true} />);

    const contentElement = screen.getByText('Content');
    expect(contentElement).toHaveStyle({ width: '100%' });
  });

  it('applies auto width to content when contentFullWidth is false', () => {
    render(<DetailRow title="Title" content="Content" contentFullWidth={false} />);

    const contentElement = screen.getByText('Content');
    expect(contentElement).toHaveStyle({ width: 'auto' });
  });

  it('renders without border when noBorder is true', () => {
    render(<DetailRow title="Title" content="Content" noBorder={true} />);

    const container = screen.getByText('Title').closest('div');
    expect(container).toHaveStyle({ borderBottom: 'none' });
  });

  it('renders with border when noBorder is false', () => {
    render(<DetailRow title="Title" content="Content" noBorder={false} />);

    const container = screen.getByText('Title').closest('div');
    expect(container).toHaveStyle({ borderBottom: '1px solid #e0e0e0' });
  });

  it('applies no padding when noPadding is true', () => {
    render(<DetailRow title="Title" content="Content" noPadding={true} />);

    const container = screen.getByText('Title').closest('div');
    expect(container).toHaveStyle({ padding: '0' });
  });

  it('applies default padding when noPadding is false', () => {
    render(<DetailRow title="Title" content="Content" noPadding={false} />);

    const container = screen.getByText('Title').closest('div');
    expect(container).toHaveStyle({ padding: '12px 0' });
  });

  it('renders with default props', () => {
    render(<DetailRow title="Title" />);

    expect(screen.getByText('Title')).toBeInTheDocument();
    expect(screen.getByText('--')).toBeInTheDocument();

    const container = screen.getByText('Title').closest('div');
    expect(container).toHaveStyle({
      padding: '12px 0',
      borderBottom: '1px solid #e0e0e0'
    });
  });

  it('matches snapshot with basic props', () => {
    const { container } = render(<DetailRow title="Test Title" content="Test Content" />);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('matches snapshot with default content', () => {
    const { container } = render(<DetailRow title="Test Title" />);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('matches snapshot with ReactNode title and content', () => {
    const titleNode = <span>Custom Title</span>;
    const contentNode = <div>Custom Content</div>;

    const { container } = render(<DetailRow title={titleNode} content={contentNode} />);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('matches snapshot with all props enabled', () => {
    const { container } = render(
      <DetailRow
        title="Title"
        content="Content"
        noBorder={true}
        noPadding={true}
        contentFullWidth={true}
        isCapitalize={true}
        sx={{ backgroundColor: 'red' }}
      />
    );
    expect(container.firstChild).toMatchSnapshot();
  });
});
