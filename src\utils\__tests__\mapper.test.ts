import { transportationModeValues } from 'constant/shipment';
import { mapperOcrResponseToToOcrRecord } from '../mapper';
import { OcrSourceFrom, SuggestOcrResponse } from 'types';
import { v4 } from 'uuid';

// Mock uuid
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'mocked-uuid')
}));

describe('mapperOcrResponseToToOcrRecord', () => {
  const mockInput: SuggestOcrResponse = {
    receiptNumber: 'REC123',
    destinationCountry: 'USA',
    transportationMode: transportationModeValues[0],
    numberOfBoxes: 10,
    exportDate: 1625097600, // July 1, 2021 in Unix timestamp
    borderCheckpointName: 'Border1',
    totalWeightKg: 100
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should map all fields correctly with valid data', () => {
    // Assume 'AIR' is in transportationModeValues
    const result = mapperOcrResponseToToOcrRecord(mockInput, 'ocr');

    expect(result).toEqual({
      receiptNumber: 'REC123',
      destinationCountry: "THE PEOPLE'S REPUBLIC OF CHINA",
      transportationMode: transportationModeValues[0],
      numberOfBoxes: '10',
      exportDate: 1625097600 * 1000,
      borderCheckpointName: 'Border1',
      containerNumber: '',
      truckNumber: '',
      trailerNumber: '',
      orchardNo: '',
      totalWeightKg: '100',
      id: 'mocked-uuid',
      sourceFrom: 'ocr',
      nameOfExportingCompany: ''
    });
    expect(v4).toHaveBeenCalledTimes(1);
  });

  test('should handle missing optional fields', () => {
    const incompleteInput = {
      receiptNumber: 'REC123',
      destinationCountry: "THE PEOPLE'S REPUBLIC OF CHINA",
      transportationMode: transportationModeValues[0]
    } as SuggestOcrResponse;

    const result = mapperOcrResponseToToOcrRecord(incompleteInput, 'ocr');

    expect(result).toEqual({
      receiptNumber: 'REC123',
      destinationCountry: "THE PEOPLE'S REPUBLIC OF CHINA",
      transportationMode: transportationModeValues[0],
      numberOfBoxes: '',
      exportDate: null,
      borderCheckpointName: '',
      containerNumber: '',
      truckNumber: '',
      trailerNumber: '',
      orchardNo: '',
      totalWeightKg: '0',
      id: 'mocked-uuid',
      sourceFrom: 'ocr',
      nameOfExportingCompany: ''
    });
  });

  test('should set empty transportationMode when value is not in allowed list', () => {
    const invalidInput = {
      ...mockInput,
      transportationMode: 'INVALID_MODE'
    };

    const result = mapperOcrResponseToToOcrRecord(invalidInput, 'ocr');

    expect(result.transportationMode).toBe('');
  });

  test('should use provided ID instead of generating one', () => {
    const customId = 'custom-id-123';

    const result = mapperOcrResponseToToOcrRecord(mockInput, 'ocr', customId);

    expect(result.id).toBe(customId);
    expect(v4).not.toHaveBeenCalled();
  });

  test('should handle different source values', () => {
    const sources: OcrSourceFrom[] = ['ocr', 'ephyto'];

    sources.forEach(source => {
      const result = mapperOcrResponseToToOcrRecord(mockInput, source);
      expect(result.sourceFrom).toBe(source);
    });
  });
});
