import { ReactElement } from 'react';
import { Control, Controller, FieldErrors, FieldValues, Path } from 'react-hook-form';
import { Fade, FormControl, MenuItem, Select, Typography, useTheme } from '@mui/material';

export interface SelectOption {
  value: string;
  label: string;
  originalValue?: string; // Optional, for storing the original value
}

export interface FormSelectProps<TFormValues extends FieldValues> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  errors: FieldErrors<TFormValues>;
  label: string;
  options: SelectOption[];
  required?: boolean;
  requiredMessage?: string;
  disabled?: boolean;
  placeholder?: string;
}

export const FormSelect = <TFormValues extends FieldValues>({
  name,
  control,
  errors,
  label,
  options,
  required = false,
  requiredMessage,
  disabled,
  placeholder,
}: FormSelectProps<TFormValues>): ReactElement => {
  const theme = useTheme();

  return (
    <Controller
      name={name}
      control={control}
      rules={{ required: required ? requiredMessage : false }}
      render={({ field }) => (
        <FormControl fullWidth error={!!errors[name]}>
          <Typography
            sx={{ fontSize: '18px', mb: 1, color: disabled ? theme.palette.text.disabled : theme.palette.text.primary,  }}
            variant="caption"
          >
            {label}{' '}
            <Typography sx={{ visibility: required ? 'visible' : 'hidden' }} variant="caption" color="error">
              *
            </Typography>
          </Typography>

          {!field.value && (
            <MenuItem sx={{ position: 'absolute', top: '46px', left: 0 }} disabled value="">
              {placeholder}
            </MenuItem>
          )}
          <Select {...field} disabled={disabled} defaultValue={''} displayEmpty value={field.value || ''}>
            {options.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
          {!!errors[name] && (
            <Fade in={true} timeout={500}>
              <Typography variant="caption" color="error">
                {String(errors[name]?.message)}
              </Typography>
            </Fade>
          )}
        </FormControl>
      )}
    />
  );
};
