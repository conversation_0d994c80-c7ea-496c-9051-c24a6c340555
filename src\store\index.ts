import { StateCreator, create as zCreate } from 'zustand';
import { devtools, persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

const storeResetFns = new Set<() => void>();

export const resetAllStores = async () => {
  storeResetFns.forEach((resetFn) => {
    resetFn();
  });
};

export const create = <T extends { _hasHydrated?: boolean }>(
  store: StateCreator<T, [['zustand/devtools', never]] | [['zustand/persist', unknown], ['zustand/immer', never]], []>,
  options?: {
    persistKey?: string;
    useSession?: boolean;
    partialize?: (state: T) => Partial<T>;
    merge?: (persistedState: Partial<T>, currentState: T) => T;
    onRehydrateStorage?: (state: T) => Partial<T>;
  }
) => {
  const withImmer = immer(store as StateCreator<T, [['zustand/immer', never]], []>);

  const withPersist = options?.persistKey
    ? persist(withImmer, {
        name: options.persistKey,
        storage: createJSONStorage(() => (options.useSession ? sessionStorage : localStorage)),
        partialize: options.partialize,
        merge: options.merge as (persistedState: unknown, currentState: T) => T,
        skipHydration: false,
        onRehydrateStorage: () => (state?: T) => {
          if (!state) return;
          (state as T)._hasHydrated = true;
          if (options.onRehydrateStorage) {
            Object.assign(state, options.onRehydrateStorage(state));
          }
        },
      })
    : withImmer;

  const customStore = zCreate<T>()(devtools(withPersist as StateCreator<T, [['zustand/devtools', never]], []>));
  const initialState = customStore.getInitialState();

  storeResetFns.add(() => {
    customStore.setState(initialState, true);
  });

  return zCreate<T>()(devtools(withPersist as StateCreator<T, [['zustand/devtools', never]], []>));
};
