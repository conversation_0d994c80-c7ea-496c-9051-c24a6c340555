import { SvgIcon, SvgIconProps } from '@mui/material';
import { FC } from 'react';

export const TruckSolidIcon: FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon {...props}>
      <svg width={props.width} height={props.height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clipPath="url(#clip0_2143_2201)">
          <path
            d="M6.44287 15.3346C7.42463 15.3346 8.22105 16.1302 8.22119 17.1119C8.22119 18.0938 7.42471 18.8903 6.44287 18.8903C5.46116 18.8901 4.66553 18.0937 4.66553 17.1119C4.66566 16.1303 5.46125 15.3347 6.44287 15.3346ZM13.5542 15.3346C14.536 15.3346 15.3324 16.1302 15.3325 17.1119C15.3325 18.0938 14.536 18.8903 13.5542 18.8903C12.5725 18.8901 11.7769 18.0937 11.7769 17.1119C11.777 16.1303 12.5726 15.3348 13.5542 15.3346ZM14.3013 3.77893C14.9412 3.77893 15.5281 4.12534 15.8481 4.67639L16.4165 5.67249C16.4964 5.8147 16.39 6.00061 16.2212 6.00061C14.9946 6.00069 13.9995 6.99665 13.9995 8.22327V10.8903C13.9997 12.1167 14.9947 13.1119 16.2212 13.1119H18.4438C18.6925 13.1121 18.888 13.3076 18.8882 13.5563V14.4459C18.888 15.9213 17.6966 17.1119 16.2212 17.1119C16.2211 15.6454 15.0208 14.4459 13.5542 14.4459C12.0878 14.4462 10.8883 15.6455 10.8882 17.1119H9.10986C9.10974 15.6455 7.91031 14.4461 6.44385 14.4459C4.97725 14.4459 3.77697 15.6454 3.77686 17.1119C2.30147 17.1119 1.11009 15.9213 1.10986 14.4459V12.6676C1.10993 12.1788 1.51066 11.7789 1.99951 11.7789H10.4438C11.6704 11.7788 12.6655 10.7828 12.6655 9.55627V4.6676C12.6656 4.17893 13.0656 3.77919 13.5542 3.77893H14.3013ZM18.6567 9.59241C18.8078 9.85906 18.8882 10.1612 18.8882 10.4723V11.7789H16.2212C15.7325 11.7788 15.3327 11.379 15.3325 10.8903V8.22327C15.3326 7.73446 15.7324 7.33468 16.2212 7.33459H17.3677L18.6567 9.59241ZM10.9595 1.11194C11.4126 1.11213 11.7769 1.47708 11.7769 1.9303V9.30725C11.7769 10.2139 11.039 10.9518 10.1323 10.9518H1.99951C1.51062 10.9518 1.10986 10.552 1.10986 10.0631V4.39221C1.10986 2.57891 2.57685 1.11199 4.39014 1.11194H10.9595ZM8.30127 4.67639C8.04348 4.40986 7.62551 4.40108 7.35889 4.65881L5.96338 6.01038L5.52783 5.59241C5.26117 5.34352 4.83434 5.34332 4.58545 5.60999C4.32788 5.87667 4.33741 6.30345 4.604 6.56116L5.50146 7.41467C5.62584 7.53892 5.79465 7.60115 5.96338 7.6012C6.12338 7.6012 6.29294 7.53912 6.42627 7.41467L8.28369 5.61877C8.55028 5.36099 8.55902 4.94303 8.30127 4.67639Z"
            fill="currentColor"
          />
        </g>
        <defs>
          <clipPath id="clip0_2143_2201">
            <rect width="20" height="20" fill="white" />
          </clipPath>
        </defs>
      </svg>
    </SvgIcon>
  );
};
