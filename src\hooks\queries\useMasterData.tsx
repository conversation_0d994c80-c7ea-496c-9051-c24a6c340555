'use client';

import { useQuery } from '@tanstack/react-query';
import { getPackagingMasterDataService } from 'services/resource.service';
import { useUserStore } from 'store/useUserStore';

export const useMasterDataQuery = () => {
  const user = useUserStore((state) => state.user);
  return useQuery({
    queryKey: ['getPackagingMasterData'],
    queryFn: () => getPackagingMasterDataService(),
    select: (response) => {
      return response.data;
    },
    enabled: !!user,
  });
};
