'use client';

import { Box, Button, Grid, InputAdornment, Typography, useTheme } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import { FormAutocomplete, FormDatePickerInput, FormNumberInput, FormSelect, FormTextInput } from 'components';
import { FormNumberFormatInput } from 'components/hook-form/form-format-number-input';
import { useEphytoExportDetailMutate } from 'hooks/mutates/useEphytoExportDetailMutate';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { FC, useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { useCreateShipmentStore } from 'store/useCreateShipmentStore';
import { Receipt } from 'types';
import { v4 } from 'uuid';
import { UploadFileBox } from '../_components/upload-ocr-box';
import { capturePosthog } from 'utils/posthog';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { getLabelByLocale } from 'utils/cookie-client';
import { borderCheckpointNameValues, transportationModeValues } from 'constant/shipment';

export const defaultReceiptFormValues: Receipt = {
  receiptNumber: '',
  destinationCountry: "THE PEOPLE'S REPUBLIC OF CHINA",
  transportationMode: '',
  numberOfBoxes: '',
  exportDate: null,
  truckNumber: '',
  trailerNumber: '',
  orchardNo: '',
  totalWeightKg: '',
  truckProvinceRegistrationNumber: '',
  trailerProvinceRegistrationNumber: '',
  borderCheckpointName: '',
  nameOfExportingCompany: '',
};

interface UploadReceiptContentProps {
  receiptFormOptions: UseFormReturn<Receipt, unknown, Receipt>;
}

export const maxlengthOrchardNo = 17;

export const UploadReceiptContent: FC<UploadReceiptContentProps> = ({ receiptFormOptions }) => {
  const ocrT = useTranslations('ocr');
  const formT = useTranslations('form');
  const { mutateAsync, isPending } = useEphytoExportDetailMutate();
  const { updateOrcForm, ocrFile, setIsUploadOcrError, manualInputOrc, setSessionFormData, isUploadOcrError } =
    useCreateShipmentStore();

  const { provinces } = useMasterDataStore();

  const borderCheckpointDataOpts = (borderCheckpointNameValues ?? []).map((checkpoint) => ({
    value: checkpoint,
    label: checkpoint,
  }));

  const provinceDataOptions = provinces?.map((province) => ({
    value: province.provinceVehicleCode ?? '',
    label: province.label ? getLabelByLocale({ label: province.label }) : 'unknown',
  }));

  const theme = useTheme();

  const {
    control,
    handleSubmit,
    reset,
    watch: watchReceiptForm,
    setError,
    getValues,
    setValue,
    formState: { errors },
  } = receiptFormOptions;

  // Watch all form values for store synchronization
  const receiptNumber = watchReceiptForm('receiptNumber');
  const truckNumber = watchReceiptForm('truckNumber');
  const trailerNumber = watchReceiptForm('trailerNumber');

  const shipmentT = useTranslations('shipment');

  useEffect(() => {
    if (ocrFile) {
      reset({ ...ocrFile, transportationMode: ocrFile.transportationMode ?? '' });
    }
  }, [ocrFile, reset]);

  useEffect(() => {
    if (!truckNumber) {
      setValue('truckProvinceRegistrationNumber', '');
    }
  }, [truckNumber, setValue]);

  useEffect(() => {
    if (!trailerNumber) {
      setValue('trailerProvinceRegistrationNumber', '');
    }
  }, [trailerNumber, setValue]);

  const onFetchReceiptNumber = async () => {
    try {
      const response = await mutateAsync(receiptNumber);
      const data = response.data.suggest;

      const convertUnixTime = data.exportDate ? data.exportDate * 1000 : null;

      const fetchOrcData = {
        ...data,
        numberOfBoxes: data.numberOfBoxes.toString() ?? '',
        exportDate: convertUnixTime,
        totalWeightKg: data.totalWeightKg?.toString() ?? '',
        transportationMode: transportationModeValues.includes(data.transportationMode) ? data.transportationMode : '',
        containerNumber: '',
        truckNumber: '',
        trailerNumber: '',
        orchardNo: '',
        borderCheckpointName: data.borderCheckpointName ?? '',
        destinationCountry: "THE PEOPLE'S REPUBLIC OF CHINA",
      };

      capturePosthog('get_ephyto_success');

      reset(fetchOrcData);

      setSessionFormData({ orcFile: fetchOrcData });
    } catch {
      capturePosthog('get_ephyto_failed');

      setError('receiptNumber', {
        message: ocrT('ocr-not-found'),
      });
    }
  };

  const onSubmit = (values: Receipt) => {
    const id = ocrFile?.id ?? v4();

    updateOrcForm({
      ...values,
      id,
      sourceFrom: values.sourceFrom ?? 'ephyto',
    });
    reset({ ...defaultReceiptFormValues });
    setIsUploadOcrError(false);
  };

  const ocrInformationT = useTranslations('ocr-information');

  const transportationModeOptions = transportationModeValues.map((option) => ({
    value: option,
    label: option,
  }));

  const ocrSupportText = ocrT('ocr-drawer-description');

  const ReceiptDescriptionPlaceholder = (chunk: React.ReactNode) => (
    <Link href="/assets/images/ocr-placeholder.jpg" target="_blank">
      {chunk}
    </Link>
  );

  return (
    <>
      {!manualInputOrc && !ocrFile?.receiptNumber && (
        <Typography variant="caption" color="text.secondary" sx={{ mb: '20px', width: '100%' }} textAlign="left">
          {shipmentT.rich('receipt-description', {
            placeholder: ReceiptDescriptionPlaceholder,
          })}
        </Typography>
      )}

      {!isUploadOcrError && !manualInputOrc && ocrFile?.receiptNumber && (
        <Typography
          component="p"
          variant="caption"
          sx={{
            borderRadius: '8px',
            backgroundColor: theme.palette.customColors.primary100,
            p: '8px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            width: '100%',
            mt: '12px',
          }}
        >
          <ErrorOutlineIcon sx={{ color: theme.palette.customColors.primary }} />
          {ocrSupportText}
        </Typography>
      )}

      <Box
        component="div"
        sx={{
          width: '100%',
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          padding: !ocrFile?.receiptNumber && !manualInputOrc ? '20px' : 0,
          border:
            !ocrFile?.receiptNumber && !manualInputOrc
              ? `2px dashed ${theme.palette.customColors.neutralBorder}`
              : 'unset',
        }}
      >
        {!ocrFile?.receiptNumber && !manualInputOrc && <UploadFileBox />}

        {(ocrFile?.receiptNumber || manualInputOrc) && (
          <Box
            sx={{
              width: '100%',
              height: '100%',
              borderRadius: '8px',
              display: 'flex',
              flexDirection: 'column',
              gap: '8px',
            }}
          >
            <Grid
              container
              component="form"
              width="100%"
              onSubmit={handleSubmit(onSubmit)}
              spacing={2}
              sx={{ mt: '20px' }}
            >
              <Grid size={{ xs: 12, md: 12, lg: 6 }}>
                <FormTextInput<Receipt>
                  name="receiptNumber"
                  required
                  requiredMessage={formT.markup('required', {
                    formName: ocrT('receipt-number-input'),
                  })}
                  placeholder={ocrInformationT('placeholder-receipt-number')}
                  errors={errors}
                  control={control}
                  label={ocrT('receipt-number-input')}
                  customBtn={
                    <Button
                      variant="outlined"
                      sx={{ padding: '14px', maxHeight: '60px' }}
                      disabled={isPending}
                      onClick={onFetchReceiptNumber}
                    >
                      <SearchIcon />
                    </Button>
                  }
                />
              </Grid>

              <Grid size={{ xs: 12, md: 12, lg: 6 }}>
                <FormTextInput<Receipt>
                  name="destinationCountry"
                  placeholder={ocrInformationT('placeholder-destination-country')}
                  defaultValue="THE PEOPLE'S REPUBLIC OF CHINA"
                  errors={errors}
                  control={control}
                  label={ocrT('destination-country')}
                  loading={isPending}
                  required
                  maxLength={100}
                  requiredMessage={shipmentT('destination-country-required')}
                />
              </Grid>

              <Grid size={{ xs: 12, md: 12, lg: 6 }}>
                <FormSelect<Receipt>
                  name="transportationMode"
                  control={control}
                  errors={errors}
                  label={ocrT('transport-mode')}
                  placeholder={ocrInformationT('placeholder-transport-mode')}
                  options={transportationModeOptions}
                  required
                  requiredMessage={formT.markup('required', {
                    formName: ocrT('transport-mode'),
                  })}
                />
              </Grid>
              <Grid size={{ xs: 12, md: 12, lg: 6 }}>
                <FormAutocomplete<Receipt>
                  errors={errors}
                  name="borderCheckpointName"
                  control={control}
                  label={ocrT('border-checkpoint-name')}
                  options={borderCheckpointDataOpts}
                  placeholder={ocrInformationT('placeholder-border-checkpoint-name')}
                />
              </Grid>

              <Grid size={{ xs: 12, md: 12, lg: 6 }}>
                <FormNumberInput<Receipt>
                  errors={errors}
                  name="totalWeightKg"
                  control={control}
                  label={ocrT('total-weight')}
                  placeholder={ocrInformationT('placeholder-total-weight')}
                  required
                  decimalScale={1}
                  max={9999999.9}
                  min={1}
                  requiredMessage={shipmentT('total-weight-required')}
                />
              </Grid>

              <Grid size={{ xs: 12, md: 12, lg: 6 }}>
                <FormNumberInput<Receipt>
                  errors={errors}
                  name="numberOfBoxes"
                  decimalScale={0}
                  max={9999999}
                  min={1}
                  placeholder={ocrInformationT('placeholder-number-of-boxes')}
                  control={control}
                  label={ocrT('number-of-boxes')}
                  required
                  requiredMessage={shipmentT('number-of-boxes-required')}
                  loading={isPending}
                />
              </Grid>

              <Grid size={{ xs: 12, md: 12, lg: 6 }}>
                <FormDatePickerInput<Receipt>
                  errors={errors}
                  name="exportDate"
                  required
                  requiredMessage={shipmentT('export-date-required')}
                  control={control}
                  label={ocrT('export-date')}
                />
              </Grid>

              <Grid size={{ xs: 12, md: 12, lg: 6 }}>
                <FormTextInput<Receipt>
                  errors={errors}
                  name="nameOfExportingCompany"
                  control={control}
                  label={ocrT('exporting-company-name')}
                  placeholder={ocrT('exporting-company-placeholder')}
                  required
                  requiredMessage={ocrT('exporting-company-required')}
                  loading={isPending}
                />
              </Grid>

              <Grid size={{ xs: 12, md: 12, lg: 6 }}>
                <FormNumberFormatInput<Receipt>
                  errors={errors}
                  name="orchardNo"
                  placeholder={ocrInformationT('placeholder-orchard-no')}
                  minLength={maxlengthOrchardNo}
                  minLengthMessage={formT('invalid-format')}
                  control={control}
                  label={ocrT('orchard-no')}
                  required
                  requiredMessage={shipmentT('orchard-no-required')}
                  patternMessage={formT('invalid-format')}
                  startAdornment={
                    <InputAdornment position="start">{ocrInformationT('prefix-orchard-no')}</InputAdornment>
                  }
                  maxLength={maxlengthOrchardNo}
                />
              </Grid>

              <Grid size={{ xs: 12, md: 12, lg: 6 }}>
                <FormTextInput<Receipt>
                  errors={errors}
                  placeholder={ocrInformationT('placeholder-container-number')}
                  name="containerNumber"
                  control={control}
                  label={ocrT('container-number')}
                  maxLength={20}
                />
              </Grid>

              <Grid size={{ xs: 12, md: 12, lg: 6 }}>
                <FormTextInput<Receipt>
                  errors={errors}
                  name="truckNumber"
                  control={control}
                  label={ocrT('truck-number')}
                  maxLength={20}
                  placeholder={ocrInformationT('placeholder-truck-number')}
                />
              </Grid>

              <Grid size={{ xs: 12, md: 12, lg: 6 }}>
                <FormAutocomplete<Receipt>
                  errors={errors}
                  name="truckProvinceRegistrationNumber"
                  control={control}
                  label={ocrT('province-truck-registration-title')}
                  options={provinceDataOptions}
                  placeholder={ocrT('province-truck-registration-placeholder')}
                  disabled={!getValues('truckNumber')}
                />
              </Grid>

              <Grid size={{ xs: 12, md: 12, lg: 6 }}>
                <FormTextInput<Receipt>
                  errors={errors}
                  placeholder={ocrInformationT('placeholder-trailer-number')}
                  name="trailerNumber"
                  maxLength={20}
                  control={control}
                  label={ocrT('trailer-number')}
                />
              </Grid>

              <Grid size={{ xs: 12, md: 12, lg: 6 }}>
                <FormAutocomplete<Receipt>
                  errors={errors}
                  name="trailerProvinceRegistrationNumber"
                  control={control}
                  label={ocrT('province-trailer-registration-title')}
                  options={provinceDataOptions}
                  placeholder={ocrT('province-trailer-registration-placeholder')}
                  disabled={!getValues('trailerNumber')}
                />
              </Grid>
            </Grid>
          </Box>
        )}
        <Box />
      </Box>
    </>
  );
};
