import { AppConfig } from 'configs/app-config';
import { MenuItem } from 'hooks/useMenuItems';
import { useRouter } from 'next/navigation';
import { useGlobalStore } from 'store/useGlobalStore';
import { ORIGINAL_TEXT_EN } from '../provider-layout';

export const useMenuItemActions = (item: MenuItem, isOpen: boolean) => {
  const setOpenMenus = useGlobalStore((state) => state.setOpenMenus);
  const isTablet = useGlobalStore((state) => state.isTablet);
  const closeTabletSidebar = useGlobalStore((state) => state.close);
  const router = useRouter();

  const hasChildren = Array.isArray(item.children) && item.children.length > 0;
  const isLink = !!item.path && !hasChildren;

  const checkTranslation = () => {
    const PROBE_SELECTOR = '[data-translate-probe]';
    const probe = document.querySelector(PROBE_SELECTOR);
    if (!probe) return;

    const content = probe.textContent?.trim();

    if (content && content !== ORIGINAL_TEXT_EN) {
      location.href = `${AppConfig.basePathProd}/${item.path}`;
    } else {
      router.push(item.path ?? '/');
    }
  };

  const handleClick = (event: React.MouseEvent<HTMLElement>, setAnchorEl: (el: HTMLElement | null) => void) => {
    if (!isOpen && hasChildren) {
      setAnchorEl(event.currentTarget);
      return;
    }

    if (item.onClick) return item.onClick();
    setOpenMenus(item.itemKey);

    if (isLink) {
      checkTranslation();
    }

    if (isTablet) {
      closeTabletSidebar();
    }
  };

  const handleChildClick = (childItem: MenuItem, onClose: () => void) => {
    if (childItem.onClick) childItem.onClick();
    onClose();
  };

  return {
    handleClick,
    handleChildClick,
    hasChildren,
    isLink,
  };
};
