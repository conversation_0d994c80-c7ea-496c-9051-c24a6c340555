import { set<PERSON><PERSON>ie<PERSON><PERSON>ale } from 'configs/cookie';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

export async function POST(req: Request) {
  try {
    const body = await req.json();

    const locale = body?.locale;

    if (locale) {
      await setCookie<PERSON><PERSON>ale('NEXT_LOCALE', locale);
    }

    return NextResponse.json({
      success: true,
    });
  } catch {
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get locale cookie',
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const cookieStore = await cookies();

    const portalLocale = cookieStore.get('NEXT_LOCALE')?.value ?? 'th';

    return NextResponse.json({
      success: true,
      locale: portalLocale,
    });
  } catch {
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get locale cookie',
        locale: 'en', // fallback locale
      },
      { status: 500 }
    );
  }
}
