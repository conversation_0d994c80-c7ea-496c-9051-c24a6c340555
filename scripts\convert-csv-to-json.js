/* eslint-disable @typescript-eslint/no-require-imports */
const fs = require('fs');
const path = require('path');

// Function to parse CSV content and convert to JSON
function parseCSVToJSON(csvContent) {
  const lines = csvContent.split('\n');
  const translations = {};

  // Skip header line
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;

    // Enhanced CSV parsing to handle comma-separated values with proper quote handling
    const csvRegex = /(?:^|,)("(?:[^"]+|"")*"|[^,]*)/g;
    const matches = [];
    let match;

    while ((match = csvRegex.exec(line)) !== null) {
      let value = match[1];
      // Remove surrounding quotes and unescape internal quotes
      if (value.startsWith('"') && value.endsWith('"')) {
        value = value.slice(1, -1).replace(/""/g, '"');
      }
      matches.push(value);
    }

    if (matches.length >= 2) {
      const english = matches[0].trim();
      const thai = matches[1].trim();

      if (english && thai) {
        translations[english] = thai;
      }
    }
  }

  return translations;
}

// Function to convert CSV to JSON
function convertCSVToJSON() {
  try {
    // Read CSV file
    const csvPath = path.join(__dirname, '..', 'translations', 'thai-english-mapping.csv');

    if (!fs.existsSync(csvPath)) {
      console.error('❌ CSV file not found:', csvPath);
      console.log('💡 Please create the CSV file first');
      return;
    }

    const csvContent = fs.readFileSync(csvPath, 'utf8');
    const translations = parseCSVToJSON(csvContent);

    return translations;
  } catch (error) {
    console.error('❌ Error reading CSV file:', error);
    throw error;
  }
}

// Function to generate JSON and save to file
function generateJSON() {
  try {
    const translations = convertCSVToJSON();
    const outputPath = path.join(__dirname, '..', 'translations', 'thai-english-mapping.json');

    // Write JSON with proper formatting
    fs.writeFileSync(outputPath, JSON.stringify(translations, null, 2), 'utf8');

    console.log('✅ JSON file generated successfully!');
    console.log(`📍 Location: ${outputPath}`);
    console.log(`📊 Total translations: ${Object.keys(translations).length} entries`);

    // Show some examples
    console.log('\n📝 Sample translations:');
    const sampleKeys = Object.keys(translations).slice(0, 5);
    sampleKeys.forEach(key => {
      console.log(`  "${key}" → "${translations[key]}"`);
    });

    return outputPath;
  } catch (error) {
    console.error('❌ Error generating JSON:', error);
    throw error;
  }
}

// Function to also update the Thai language files
function generateTranslationFiles() {
  try {
    const translations = convertCSVToJSON();

    // Create English translations (keys = values for reference)
    const englishTranslations = {};
    Object.keys(translations).forEach(key => {
      englishTranslations[key] = key;
    });

    // Ensure messages directory exists
    const messagesDir = path.join(__dirname, '..', 'messages');
    if (!fs.existsSync(messagesDir)) {
      fs.mkdirSync(messagesDir, { recursive: true });
    }

    // Write English translations
    const englishPath = path.join(messagesDir, 'en.json');
    fs.writeFileSync(englishPath, JSON.stringify(englishTranslations, null, 2), 'utf8');

    // Write Thai translations
    const thaiPath = path.join(messagesDir, 'th.json');
    fs.writeFileSync(thaiPath, JSON.stringify(translations, null, 2), 'utf8');

    console.log('✅ Translation files generated successfully!');
    console.log(`📍 English: ${englishPath}`);
    console.log(`📍 Thai: ${thaiPath}`);
    console.log(`📊 Total translations: ${Object.keys(translations).length}`);

    return { englishPath, thaiPath };
  } catch (error) {
    console.error('❌ Error generating translation files:', error);
    throw error;
  }
}

// Main function
function main() {
  const args = process.argv.slice(2);
  const generateMessages = args.includes('--messages');

  if (generateMessages) {
    generateTranslationFiles();
  } else {
    generateJSON();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { parseCSVToJSON, convertCSVToJSON, generateJSON, generateTranslationFiles };
