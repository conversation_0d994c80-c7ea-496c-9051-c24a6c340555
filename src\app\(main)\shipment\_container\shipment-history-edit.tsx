'use client';

import { Box, Typography, IconButton } from '@mui/material';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { Breadcrumbs } from 'components';
import { clientRoutes } from 'routes/client-routes';
import { PackingHouseDetail } from 'types';
import { SortOrder } from 'types/shipment-history';
import { mockShipmentHistoryData } from '../../../../data/mock-shipment-history';
import { HistorySortButtons } from '../_components/history-sort-buttons';
import { HistoryDisplay } from '../_components/history-display';

interface ShipmentHistoryEditProps {
  shipmentId: string;
  shipmentDetail: PackingHouseDetail;
}

const ShipmentHistoryEdit = ({ shipmentId, shipmentDetail }: ShipmentHistoryEditProps) => {
  const shipmentT = useTranslations('shipment');
  const router = useRouter();

  const [sortOrder, setSortOrder] = useState<SortOrder>('newest');

  const historyData = mockShipmentHistoryData;

  const breadcrumbs = [
    {
      label: shipmentT('shipments'),
      href: clientRoutes.shipment,
    },
    {
      label: shipmentDetail?.name || shipmentId,
      href: `/shipment/${shipmentId}`,
    },
    {
      label: shipmentT('history-edit-title'),
      href: clientRoutes.shipmentHistoryEdit(shipmentId),
    },
  ];

  const handleSortChange = (newSortOrder: SortOrder) => {
    setSortOrder(newSortOrder);
  };

  const handleBackClick = () => {
    router.back();
  };

  return (
    <Box sx={{ flex: 1, p: '20px' }}>
      <Breadcrumbs items={breadcrumbs} />

      <Box sx={{ mt: 2, mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <IconButton onClick={handleBackClick} sx={{ p: 1 }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h6" fontWeight="bold">
            {shipmentT('history-edit-title')}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
          <HistorySortButtons sortOrder={sortOrder} onSortChange={handleSortChange} />
        </Box>
      </Box>

      <HistoryDisplay entries={historyData.entries} sortOrder={sortOrder} />
    </Box>
  );
};

export default ShipmentHistoryEdit;
