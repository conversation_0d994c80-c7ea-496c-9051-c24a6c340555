'use client';

import React from 'react';
import { Box, Typography, Paper, Button, Divider } from '@mui/material';
import { useTranslations } from 'next-intl';
import { theme } from 'styles/theme';
import lineSrc from 'assets/icons/line.svg';
import contactUsSrc from 'assets/icons/contact-us.svg';
import { Image } from 'components';
import { EmailOutlined, FileCopyOutlined, PhoneOutlined } from '@mui/icons-material';
import { basePathProd } from 'configs/app-config';
import toastMessages from 'utils/toastMessages';

export const ContactUs: React.FC = () => {
  const contactT = useTranslations('contact-us');

  const handleCopyEmail = async () => {
    try {
      const emailTemplate = [
        contactT('email-template-name'),
        contactT('email-template-description'),
        contactT('email-template-screenshots'),
      ].join('\n');

      await navigator.clipboard.writeText(emailTemplate);
      toastMessages.info(contactT('copy-success'));
    } catch {
      toastMessages.error(contactT('copy-error'));
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        backgroundColor: theme.palette.customColors.lightGray,
      }}
    >
      <Box component="article" sx={{ flexGrow: 1, p: { xs: 2, sm: 3 } }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3, flexWrap: 'wrap' }}>
          <Typography variant="h5" component="h1" fontWeight="bold" sx={{ mb: { xs: 2, sm: 0 } }}>
            {contactT('title')}
          </Typography>
        </Box>

        <Paper
          elevation={0}
          sx={{
            p: 4,
            borderRadius: 3,
            border: '1px solid #e0e0e0',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 6,
              mb: 4,
              p: 3,
              backgroundImage: `
                               url('${basePathProd}/assets/images/contact-bg.png')`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
              borderRadius: 2,
              justifyContent: 'center',
            }}
          >
            <Box
              sx={{
                width: 60,
                height: 60,
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Image src={contactUsSrc} width={80} height={80} alt="line" />
            </Box>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: '#333',
              }}
            >
              {contactT('help-title')}
            </Typography>
          </Box>

          <Typography
            variant="body1"
            sx={{
              color: '#666',
            }}
          >
            {contactT('tell-us')}
          </Typography>

          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: '#333',
              mb: 3,
            }}
          >
            {contactT('contact-directly')}
          </Typography>

          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mb: 4, flex: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Image src={lineSrc} width={24} height={24} alt="line" />
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  {contactT('line-support')}
                </Typography>
              </Box>

              <Divider sx={{ mr: 8 }} />

              {/* Phone Contact */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <PhoneOutlined sx={{ color: theme.palette.customColors.primary, fontSize: 24 }} />
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  {contactT('phone-support')}
                </Typography>
              </Box>

              <Divider sx={{ mr: 8 }} />

              {/* Email Contact */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <EmailOutlined sx={{ color: theme.palette.customColors.primary, fontSize: 24 }} />
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  {contactT('email-support')}
                </Typography>
              </Box>
            </Box>

            <Box
              sx={{
                backgroundColor: theme.palette.customColors.primary50,
                color: 'black',
                borderRadius: 2,
                mb: 3,
                pb: 1.5,
                flex: 1,
              }}
            >
              <Box
                sx={{
                  background: theme.palette.customColors.primary,
                  borderTopLeftRadius: 8,
                  borderTopRightRadius: 8,
                  px: 3,
                  py: 1,
                }}
              >
                <Typography variant="body2" sx={{ fontWeight: 500, color: 'white' }}>
                  {contactT('email-template-title')}
                </Typography>
              </Box>
              <Box component="ul" sx={{ px: 4, py: 2 }}>
                <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                  {contactT('email-template-name')}
                </Typography>
                <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                  {contactT('email-template-description')}
                </Typography>
                <Typography component="li" variant="body2">
                  {contactT('email-template-screenshots')}
                </Typography>
              </Box>
              <Box sx={{ px: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={<FileCopyOutlined />}
                  onClick={handleCopyEmail}
                  sx={{
                    borderColor: theme.palette.customColors.primary,
                    color: theme.palette.customColors.primary,
                    textTransform: 'none',
                    fontWeight: 500,
                    width: '100%',
                  }}
                >
                  {contactT('copy-button')}
                </Button>
              </Box>
            </Box>
          </Box>
        </Paper>
      </Box>
    </Box>
  );
};
