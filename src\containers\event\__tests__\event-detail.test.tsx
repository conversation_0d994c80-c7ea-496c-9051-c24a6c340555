/* eslint-disable @next/next/no-img-element */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-require-imports */
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { EventDetail } from '../event-detail';
import { ThemeProvider } from '@mui/material/styles';
import { theme } from 'styles/theme';
import { PackingHouseDetail, DurianVariety } from 'types';

// Mock dependencies
jest.mock('@tanstack/react-query', () => ({
  useQuery: jest.fn(),
  useMutation: jest.fn(),
  useQueryClient: jest.fn(),
}));

jest.mock('hooks/mutates/useReceiveHarvestMutate', () => ({
  useReceiveHarvestMutate: jest.fn(),
}));

jest.mock('hooks/mutates/useUpdateReceiveMutate', () => ({
  useUpdateReceiveMutate: jest.fn(),
}));

jest.mock('hooks/useGetEventStatus', () => ({
  useGetEventStatus: jest.fn(),
}));

jest.mock('store/useUserStore', () => ({
  useUserStore: jest.fn(),
}));

jest.mock('next-intl', () => ({
  useTranslations: jest.fn(),
}));

jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

jest.mock('utils', () => ({
  formatNumberWithCommas: jest.fn((value) => value?.toString() ?? '0'),
  getImageUrl: jest.fn((filename) => `https://example.com/${filename}`),
}));

jest.mock('utils/event', () => ({
  compareVarieties: jest.fn(),
  convertModalDataToVarieties: jest.fn(),
  convertResponseToWeights: jest.fn(),
  reorderToOriginalOrder: jest.fn(),
  transformArray: jest.fn(),
  transformDurianDataEvent: jest.fn(),
}));

jest.mock('utils/toastMessages', () => ({
  __esModule: true,
  default: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock('utils/cookie-client', () => ({
  getCookieLocale: jest.fn(() => 'en'),
}));

jest.mock('utils/gtag', () => ({
  sendEvent: jest.fn(),
}));

jest.mock('services/resource.service', () => ({
  fetchProvinceService: jest.fn(),
}));

jest.mock('components', () => ({
  Breadcrumbs: ({ items }: any) => (
    <div data-testid="breadcrumbs">
      {items.map((item: any) => (
        <span key={item.label}>{item.label}</span>
      ))}
    </div>
  ),
  DetailRow: ({ title, content }: any) => (
    <div data-testid="detail-row">
      <span>{title}</span>: <span>{content}</span>
    </div>
  ),
  Dialog: ({ isOpen, title, content, onConfirm, onCancel }: any) =>
    isOpen ? (
      <div data-testid="dialog">
        <h2>{title}</h2>
        <p>{content}</p>
        <button onClick={onConfirm} data-testid="dialog-confirm">
          Confirm
        </button>
        <button onClick={onCancel} data-testid="dialog-cancel">
          Cancel
        </button>
      </div>
    ) : null,
  ImageReviewModal: ({ imageUrl }: any) => (
    <div data-testid="image-review-modal">
      <img src={imageUrl} alt="review" />
    </div>
  ),
  PlotOptionBox: ({ plotNumber, plotId }: any) => (
    <div data-testid="plot-option-box">
      Plot: {plotNumber} - {plotId}
    </div>
  ),
}));

jest.mock('components/durian-variety', () => ({
  DurianVarietyBox: ({ variety, isUpdated, onToggleView }: any) => (
    <div data-testid="durian-variety-box">
      <span>{variety.name ?? variety.id}</span>
      {isUpdated && <span data-testid="updated-badge">Updated</span>}
      <button onClick={() => onToggleView?.(variety.id)} data-testid="toggle-view">
        Toggle View
      </button>
    </div>
  ),
}));

jest.mock('../_components', () => ({
  OriginalDrawer: ({ open, onClose }: any) =>
    open ? (
      <div data-testid="original-drawer">
        <button onClick={onClose} data-testid="close-original-drawer">
          Close
        </button>
      </div>
    ) : null,
  RejectDrawer: ({ open, toggle }: any) =>
    open ? (
      <div data-testid="reject-drawer">
        <button onClick={() => toggle(false)} data-testid="close-reject-drawer">
          Close
        </button>
      </div>
    ) : null,
  TotalWeightInfoDrawer: ({ open, toggle }: any) =>
    open ? (
      <div data-testid="total-weight-drawer">
        <button onClick={() => toggle(false)} data-testid="close-total-weight-drawer">
          Close
        </button>
      </div>
    ) : null,
  ReceiveHarvestModal: ({ open, onClose, onSave }: any) =>
    open ? (
      <div data-testid="receive-harvest-modal">
        <button onClick={onClose} data-testid="close-update-modal">
          Close
        </button>
        <button onClick={() => onSave({
          update: {
            varieties: [
              {
                id: 'variety1',
                flowerBloomingDay: **********,
                grades: [
                  { id: 'grade1', weight: 10 },
                  { id: 'grade2', weight: 15 }
                ]
              },
              {
                id: 'variety2',
                flowerBloomingDay: **********,
                grades: [
                  { id: 'grade3', weight: 5 }
                ]
              }
            ]
          }
        })} data-testid="save-update-modal">
          Save
        </button>
      </div>
    ) : null,
  formatDateWithLocale: jest.fn((date) => new Date(date).toLocaleDateString()),
  renderStatus: jest.fn((status, label) => <span data-testid="status">{label}</span>),
  renderRecordByInDetail: jest.fn((isReceiving, data) => `${data.userCreated.firstName} ${data.userCreated.lastName}`),
}));

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider theme={theme}>{children}</ThemeProvider>
);

const mockVarieties: DurianVariety[] = [
  {
    id: 'variety1',
    value: 'monthong',
    label: { th: 'มันทอง', en: 'Monthong' },
    flowerBloomingDay: **********,
    name: 'Monthong',
    grades: [
      { id: 'grade1', value: 'A', label: { th: 'เกรด A', en: 'Grade A' }, weight: 10 },
      { id: 'grade2', value: 'B', label: { th: 'เกรด B', en: 'Grade B' }, weight: 15 },
    ],
  },
  {
    id: 'variety2',
    value: 'kanyao',
    label: { th: 'ก้านยาว', en: 'Kanyao' },
    flowerBloomingDay: **********,
    name: 'Kanyao',
    grades: [
      { id: 'grade3', value: 'A', label: { th: 'เกรด A', en: 'Grade A' }, weight: 5 },
    ],
  },
];

const mockEventDetail: PackingHouseDetail = {
  id: 'event-123',
  status: 'waiting',
  type: 'receiving',
  name: 'Test Event',
  description: 'Test Description',
  address: 'Test Address',
  country: 'Thailand',
  dateCreated: '2023-12-25T10:00:00Z',
  images: [
    { id: 'img1', filenameDisk: 'image1.jpg', filenameDownload: 'image1.jpg' },
  ],
  logo: { id: 'logo1', filenameDisk: 'logo.jpg', filenameDownload: 'logo.jpg' },
  farm: {
    address: 'Farm Address',
    id: 'farm-1',
    name: 'Test Farm',
  },
  gln: '1234567890123',
  positionLongitude: 100.5,
  positionLatitude: 13.7,
  role: 'packing_house_staff',
  batchlot: 'BATCH-001',
  packingHouse: {
    id: 'ph-1',
    name: 'Test Packing House',
  } as any,
  userCreated: {
    id: 'user-1',
    firstName: 'John',
    lastName: 'Doe',
    role: 'farmer',
    profile: {
      nickname: 'Johnny',
    },
  },
  varieties: mockVarieties,
  packing: [],
  meta: {
    startTime: '2023-12-25T08:00:00Z',
    endTime: '2023-12-25T17:00:00Z',
    weight: 100,
    trunkPlateNumber: 'TRK-123',
    sourceMaterial: 'durian',
    flowerBloomingDay: '1703500800000',
    cuttingDay: '1703500800000',
    cutter: {
      id: 'cutter-1',
      name: 'Test Cutter',
      licenseNumber: 'LIC123',
      profileId: 'profile-1',
      existingCutterProfileId: null,
      existingCutterId: null,
      avatar: {
        id: 'avatar-1',
        filenameDisk: 'avatar.jpg',
        filenameDownload: 'avatar.jpg'
      },
      isCertified: true,
    },
    cutterVehicle: {
      vehicleRegistrationNumber: 'ABC-123',
      provinceRegistrationNumber: '10',
      image: {
        id: 'vehicle-1',
        filenameDisk: 'vehicle.jpg',
        filenameDownload: 'vehicle.jpg'
      },
    },
    farm: {
      farmId: 'farm-1',
      plots: [
        {
          id: 'plot-1',
          name: 'Plot 1',
          plotId: 'P001',
          gap: '5x5',
          area: 100,
          areaUnit: { th: 'ไร่', en: 'Rai' },
          image: {
            id: 'plot-1',
            filenameDisk: 'plot.jpg',
            filenameDownload: 'plot.jpg'
          },
        },
      ],
    },
  },
  immutable: false,
};

describe('EventDetail', () => {
  const mockMutateAsync = jest.fn();
  const mockUpdateReceiveAsync = jest.fn();
  const mockRouter = { back: jest.fn(), push: jest.fn() };
  const mockSearchParams = { get: jest.fn() };
  const mockUser = { firstName: 'Test', lastName: 'User' };
  const mockTranslation = jest.fn((key: string) => key);

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup mocks
    const { useQuery } = require('@tanstack/react-query');
    const { useReceiveHarvestMutate } = require('hooks/mutates/useReceiveHarvestMutate');
    const { useUpdateReceiveMutate } = require('hooks/mutates/useUpdateReceiveMutate');
    const { useGetEventStatus } = require('hooks/useGetEventStatus');
    const { useUserStore } = require('store/useUserStore');
    const { useTranslations } = require('next-intl');
    const { useRouter, useSearchParams } = require('next/navigation');

    useQuery.mockReturnValue({
      data: {
        data: [
          {
            provinceVehicleCode: '10',
            label: { th: 'กรุงเทพมหานคร', en: 'Bangkok' },
          },
        ],
      },
      isLoading: false,
      error: null,
    });

    useReceiveHarvestMutate.mockReturnValue({
      mutateAsync: mockMutateAsync,
    });

    useUpdateReceiveMutate.mockReturnValue({
      mutateAsync: mockUpdateReceiveAsync,
    });

    useGetEventStatus.mockReturnValue({
      getStatusLabel: jest.fn(() => ({
        eventStatus: 'waiting',
        eventStatusLabel: 'Waiting',
      })),
    });

    useUserStore.mockReturnValue(mockUser);
    useTranslations.mockReturnValue(mockTranslation);
    useRouter.mockReturnValue(mockRouter);
    useSearchParams.mockReturnValue(mockSearchParams);

    // Setup utility mocks
    const { transformDurianDataEvent } = require('utils/event');
    transformDurianDataEvent.mockReturnValue(mockVarieties);
  });

  describe('Component Rendering', () => {
    it('renders event detail component with basic information', () => {
      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} />
        </TestWrapper>
      );

      expect(screen.getByRole('heading', { name: 'Test Event' })).toBeInTheDocument();
      expect(screen.getByTestId('breadcrumbs')).toBeInTheDocument();
      expect(screen.getByText('durian-harvesting-summary')).toBeInTheDocument();
      expect(screen.getByText('cutter-information')).toBeInTheDocument();
      expect(screen.getByText('durian-variety-grade-weight')).toBeInTheDocument();
      expect(screen.getByText('farm-information')).toBeInTheDocument();
    });

    it('renders receiving mode correctly', () => {
      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} isReceiving={true} />
        </TestWrapper>
      );

      expect(screen.getByRole('heading', { name: 'BATCH-001' })).toBeInTheDocument();
    });

    it('renders waiting status with action buttons', () => {
      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} />
        </TestWrapper>
      );

      expect(screen.getByText('reject-this-batch')).toBeInTheDocument();
      expect(screen.getByText('receive-this-batch')).toBeInTheDocument();
    });

    it('renders rejected status with reject reason', () => {
      const rejectedEventDetail = {
        ...mockEventDetail,
        status: 'rejected',
        rejectReason: 'Quality issues',
      };

      const { useGetEventStatus } = require('hooks/useGetEventStatus');
      useGetEventStatus.mockReturnValue({
        getStatusLabel: jest.fn(() => ({
          eventStatus: 'rejected',
          eventStatusLabel: 'Rejected',
        })),
      });

      render(
        <TestWrapper>
          <EventDetail eventDetail={rejectedEventDetail} />
        </TestWrapper>
      );

      expect(screen.getByText('reject-drawer-reason')).toBeInTheDocument();
      expect(screen.getByText('Quality issues')).toBeInTheDocument();
    });

    it('renders edit button in receiving mode when not immutable', () => {
      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} isReceiving={true} />
        </TestWrapper>
      );

      expect(screen.getByText('edit')).toBeInTheDocument();
    });

    it('does not render edit button when immutable', () => {
      const immutableEventDetail = {
        ...mockEventDetail,
        immutable: true,
      };

      render(
        <TestWrapper>
          <EventDetail eventDetail={immutableEventDetail} isReceiving={true} />
        </TestWrapper>
      );

      expect(screen.queryByText('edit')).not.toBeInTheDocument();
    });

    it('renders view mode correctly', () => {
      mockSearchParams.get.mockReturnValue('view');

      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} isReceiving={true} />
        </TestWrapper>
      );

      expect(screen.queryByText('edit')).not.toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('handles back button click', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} />
        </TestWrapper>
      );

      const backButton = screen.getByTestId('ArrowBackOutlinedIcon').closest('button');
      await user.click(backButton!);

      expect(mockRouter.back).toHaveBeenCalled();
    });

    it('opens receive confirmation dialog', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} />
        </TestWrapper>
      );

      const receiveButton = screen.getByText('receive-this-batch');
      await user.click(receiveButton);

      expect(screen.getByTestId('receive-harvest-modal')).toBeInTheDocument();
    });

    it('opens reject drawer', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} />
        </TestWrapper>
      );

      const rejectButton = screen.getByText('reject-this-batch');
      await user.click(rejectButton);

      expect(screen.getByTestId('reject-drawer')).toBeInTheDocument();
    });

    it('opens total weight info drawer', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} />
        </TestWrapper>
      );

      const infoButton = screen.getByTestId('InfoOutlinedIcon').closest('button');
      await user.click(infoButton!);

      expect(screen.getByTestId('total-weight-drawer')).toBeInTheDocument();
    });

    it('opens update varieties modal', async () => {
      const user = userEvent.setup();
      mockSearchParams.get.mockReturnValue(null); // Not in view mode

      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} isReceiving={true} />
        </TestWrapper>
      );

      const editButton = screen.getByRole('button', { name: 'edit' });
      await user.click(editButton);

      expect(screen.getByTestId('receive-harvest-modal')).toBeInTheDocument();
    });

    it('opens original drawer when toggle view is clicked', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} />
        </TestWrapper>
      );

      const toggleViewButtons = screen.getAllByTestId('toggle-view');
      await user.click(toggleViewButtons[0]); // Click the first one

      expect(screen.getByTestId('original-drawer')).toBeInTheDocument();
    });
  });

  describe('Dialog and Modal Interactions', () => {
    it('handles receive confirmation', async () => {
      const user = userEvent.setup();
      const { transformArray, reorderToOriginalOrder, compareVarieties, convertModalDataToVarieties } = require('utils/event');
      const { sendEvent } = require('utils/gtag');

      transformArray.mockReturnValue(mockVarieties);
      reorderToOriginalOrder.mockReturnValue(mockVarieties);
      compareVarieties.mockReturnValue({ hasChanges: false, changedVarietyIds: [] });
      convertModalDataToVarieties.mockReturnValue(mockVarieties);

      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} />
        </TestWrapper>
      );

      // Open receive harvest modal
      const receiveButton = screen.getByText('receive-this-batch');
      await user.click(receiveButton);

      // Confirm using the mocked save button (since modal is mocked)
      const confirmButton = screen.getByTestId('save-update-modal');
      await user.click(confirmButton);

      await waitFor(() => {
        expect(mockMutateAsync).toHaveBeenCalledWith({
          harvestProductId: 'event-123',
          latitude: 13.7,
          longitude: 100.5,
          update: {
            varieties: mockVarieties,
          },
        });
        expect(sendEvent).toHaveBeenCalledWith('receive_incoming_harvest');
      });
    });

    it('handles receive confirmation with changes', async () => {
      const user = userEvent.setup();
      const { transformArray, reorderToOriginalOrder, compareVarieties, convertModalDataToVarieties } = require('utils/event');
      const { sendEvent } = require('utils/gtag');

      transformArray.mockReturnValue(mockVarieties);
      reorderToOriginalOrder.mockReturnValue(mockVarieties);
      compareVarieties.mockReturnValue({ changedVarietyIds: ['variety1'] });
      convertModalDataToVarieties.mockReturnValue(mockVarieties);

      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} />
        </TestWrapper>
      );

      // Open receive harvest modal directly (the edit icon was removed)
      const receiveButton = screen.getByText('receive-this-batch');
      await user.click(receiveButton);

      // Confirm using the mocked save button (since modal is mocked)
      const confirmButton = screen.getByTestId('save-update-modal');
      await user.click(confirmButton);

      await waitFor(() => {
        expect(sendEvent).toHaveBeenCalledWith('receive_incoming_harvest');
      });
    });

    it('handles dialog cancellation', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} />
        </TestWrapper>
      );

      // Open receive harvest modal
      const receiveButton = screen.getByText('receive-this-batch');
      await user.click(receiveButton);

      // Cancel using the mocked close button (since modal is mocked)
      const cancelButton = screen.getByTestId('close-update-modal');
      await user.click(cancelButton);

      expect(screen.queryByTestId('receive-harvest-modal')).not.toBeInTheDocument();
    });

    it('handles update varieties modal save in receiving mode', async () => {
      const user = userEvent.setup();
      const { transformArray, reorderToOriginalOrder } = require('utils/event');
      const { sendEvent } = require('utils/gtag');

      mockSearchParams.get.mockReturnValue(null); // Not in view mode

      transformArray.mockReturnValue(mockVarieties);
      reorderToOriginalOrder.mockReturnValue(mockVarieties);

      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} isReceiving={true} />
        </TestWrapper>
      );

      const editButton = screen.getByRole('button', { name: 'edit' });
      await user.click(editButton);

      const saveButton = screen.getByTestId('save-update-modal');
      await user.click(saveButton);

      await waitFor(() => {
        expect(mockUpdateReceiveAsync).toHaveBeenCalledWith({
          productId: 'event-123',
          update: {
            varieties: mockVarieties,
          },
        });
        expect(sendEvent).toHaveBeenCalledWith('enter_variety_grade_weight');
      });
    });

    it('handles update varieties modal close', async () => {
      const user = userEvent.setup();
      mockSearchParams.get.mockReturnValue(null); // Not in view mode

      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} isReceiving={true} />
        </TestWrapper>
      );

      const editButton = screen.getByRole('button', { name: 'edit' });
      await user.click(editButton);

      const closeButton = screen.getByTestId('close-update-modal');
      await user.click(closeButton);

      expect(screen.queryByTestId('receive-harvest-modal')).not.toBeInTheDocument();
    });
  });

  describe('Data Processing and Calculations', () => {
    it('calculates total weight correctly', () => {
      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} />
        </TestWrapper>
      );

      // Total weight should be calculated from varieties grades
      // mockVarieties has grades with weights: 10, 15, 5 = 30 total
      expect(screen.getByText(/30.*kg/)).toBeInTheDocument();
    });

    it('handles empty varieties gracefully', () => {
      const eventDetailWithoutVarieties = {
        ...mockEventDetail,
        varieties: [],
      };

      render(
        <TestWrapper>
          <EventDetail eventDetail={eventDetailWithoutVarieties} />
        </TestWrapper>
      );

      expect(screen.getByText(/0.*kg/)).toBeInTheDocument();
    });

    it('handles missing meta data gracefully', () => {
      const eventDetailWithoutMeta = {
        ...mockEventDetail,
        meta: {
          startTime: '2023-12-25T08:00:00Z',
          endTime: '2023-12-25T17:00:00Z',
          weight: 0,
          trunkPlateNumber: '',
          sourceMaterial: '',
          flowerBloomingDay: '1703500800000',
        },
      };

      render(
        <TestWrapper>
          <EventDetail eventDetail={eventDetailWithoutMeta} />
        </TestWrapper>
      );

      // Should not crash and render basic information
      expect(screen.getByRole('heading', { name: 'Test Event' })).toBeInTheDocument();
    });

    it('displays province information correctly', () => {
      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} />
        </TestWrapper>
      );

      // Province should be resolved from the mock data
      expect(screen.getByText('Bangkok')).toBeInTheDocument();
    });

    it('handles missing province data', () => {
      const { useQuery } = require('@tanstack/react-query');
      useQuery.mockReturnValue({
        data: null,
        isLoading: false,
        error: null,
      });

      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} />
        </TestWrapper>
      );

      expect(screen.getByText('--')).toBeInTheDocument();
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('handles confirm with no data', async () => {
      // Test with minimal eventDetail that has required fields
      const minimalEventDetail = {
        ...mockEventDetail,
        status: null,
        type: null,
      };

      // Mock getStatusLabel to handle null values
      const { useGetEventStatus } = require('hooks/useGetEventStatus');
      useGetEventStatus.mockReturnValue({
        getStatusLabel: jest.fn(() => ({
          eventStatus: 'unknown',
          eventStatusLabel: 'Unknown',
        })),
      });

      render(
        <TestWrapper>
          <EventDetail eventDetail={minimalEventDetail as any} />
        </TestWrapper>
      );

      // Component should handle null status gracefully
      expect(screen.getByRole('heading', { name: 'Test Event' })).toBeInTheDocument();
    });

    it('handles confirm with empty varieties', async () => {
      const user = userEvent.setup();
      const { transformArray, reorderToOriginalOrder, compareVarieties, convertModalDataToVarieties } = require('utils/event');

      // Mock the utility functions
      transformArray.mockReturnValue(mockVarieties);
      reorderToOriginalOrder.mockReturnValue(mockVarieties);
      compareVarieties.mockReturnValue({ hasChanges: false, changedVarietyIds: [] });
      convertModalDataToVarieties.mockReturnValue(mockVarieties);

      const eventDetailWithEmptyVarieties = {
        ...mockEventDetail,
        varieties: [],
      };

      render(
        <TestWrapper>
          <EventDetail eventDetail={eventDetailWithEmptyVarieties} />
        </TestWrapper>
      );

      const receiveButton = screen.getByText('receive-this-batch');
      await user.click(receiveButton);

      // Confirm using the mocked save button (since modal is mocked)
      const confirmButton = screen.getByTestId('save-update-modal');
      await user.click(confirmButton);

      // Should call mutateAsync - the modal provides varieties data even if original event had empty varieties
      expect(mockMutateAsync).toHaveBeenCalledWith({
        harvestProductId: 'event-123',
        latitude: 13.7,
        longitude: 100.5,
        update: {
          varieties: mockVarieties,
        },
      });
    });

    it('handles missing user data', () => {
      const { useUserStore } = require('store/useUserStore');
      useUserStore.mockReturnValue(null);

      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} />
        </TestWrapper>
      );

      // Should render without crashing
      expect(screen.getByRole('heading', { name: 'Test Event' })).toBeInTheDocument();
    });

    it('handles missing images gracefully', () => {
      const eventDetailWithoutImages = {
        ...mockEventDetail,
        images: [],
        meta: {
          ...mockEventDetail.meta,
          cutterVehicle: {
            vehicleRegistrationNumber: mockEventDetail.meta.cutterVehicle!.vehicleRegistrationNumber,
            provinceRegistrationNumber: mockEventDetail.meta.cutterVehicle!.provinceRegistrationNumber,
            image: undefined,
          },
          farm: {
            farmId: 'farm-1',
            plots: [
              {
                ...mockEventDetail.meta.farm!.plots[0],
                image: undefined,
              },
            ],
          },
        },
      };

      render(
        <TestWrapper>
          <EventDetail eventDetail={eventDetailWithoutImages} />
        </TestWrapper>
      );

      expect(screen.getByRole('heading', { name: 'Test Event' })).toBeInTheDocument();
    });

    it('handles other variety with custom name', () => {
      const eventDetailWithOtherVariety = {
        ...mockEventDetail,
        varieties: [
          ...mockVarieties,
          {
            id: 'other',
            value: 'other',
            label: { th: 'อื่นๆ', en: 'Other' },
            flowerBloomingDay: **********,
            name: 'Custom Variety Name',
            grades: [
              { id: 'grade4', value: 'C', label: { th: 'เกรด C', en: 'Grade C' }, weight: 8 },
            ],
          },
        ],
      };

      render(
        <TestWrapper>
          <EventDetail eventDetail={eventDetailWithOtherVariety} />
        </TestWrapper>
      );

      expect(screen.getByRole('heading', { name: 'Test Event' })).toBeInTheDocument();
    });
  });

  describe('Accessibility and UI States', () => {
    it('has proper ARIA labels and roles', () => {
      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} />
        </TestWrapper>
      );

      const buttons = screen.getAllByRole('button');
      expect(buttons.length).toBeGreaterThan(0);
    });

    it('displays loading states appropriately', () => {
      const { useQuery } = require('@tanstack/react-query');
      useQuery.mockReturnValue({
        data: null,
        isLoading: true,
        error: null,
      });

      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} />
        </TestWrapper>
      );

      expect(screen.getByRole('heading', { name: 'Test Event' })).toBeInTheDocument();
    });

    it('handles query errors gracefully', () => {
      const { useQuery } = require('@tanstack/react-query');
      useQuery.mockReturnValue({
        data: null,
        isLoading: false,
        error: new Error('Failed to fetch provinces'),
      });

      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} />
        </TestWrapper>
      );

      expect(screen.getByRole('heading', { name: 'Test Event' })).toBeInTheDocument();
    });

    it('renders AdjustWeightInformation component when diffVarieties data exists and isReceiving is true', () => {
      const eventDetailWithDiffVarieties = {
        ...mockEventDetail,
        meta: {
          ...mockEventDetail.meta,
          diffVarieties: [
            {
              value: 'variety1',
              label: {
                th: 'ทุเรียนพันธุ์ 1',
                en: 'Durian Variety 1',
              },
              grades: [
                {
                  value: 'grade1',
                  label: {
                    th: 'เกรด A',
                    en: 'Grade A',
                  },
                  diff: {
                    origin: 100,
                    current: 95,
                  },
                },
              ],
            },
          ],
        },
      };

      render(
        <TestWrapper>
          <EventDetail eventDetail={eventDetailWithDiffVarieties} isReceiving={true} />
        </TestWrapper>
      );

      // Check if the weight adjusted section is rendered
      expect(screen.getByText('weight-adjusted-by-packing-house')).toBeInTheDocument();
      expect(document.getElementById('diff-varieties')).toBeInTheDocument();
    });

    it('does not render AdjustWeightInformation component when diffVarieties data does not exist', () => {
      render(
        <TestWrapper>
          <EventDetail eventDetail={mockEventDetail} isReceiving={true} />
        </TestWrapper>
      );

      // Check if the weight adjusted section is not rendered
      expect(screen.queryByText('weight-adjusted-by-packing-house')).not.toBeInTheDocument();
      expect(document.getElementById('diff-varieties')).not.toBeInTheDocument();
    });

    it('does not render AdjustWeightInformation component when isReceiving is false', () => {
      const eventDetailWithDiffVarieties = {
        ...mockEventDetail,
        meta: {
          ...mockEventDetail.meta,
          diffVarieties: [
            {
              value: 'variety1',
              label: {
                th: 'ทุเรียนพันธุ์ 1',
                en: 'Durian Variety 1',
              },
              grades: [
                {
                  value: 'grade1',
                  label: {
                    th: 'เกรด A',
                    en: 'Grade A',
                  },
                  diff: {
                    origin: 100,
                    current: 95,
                  },
                },
              ],
            },
          ],
        },
      };

      render(
        <TestWrapper>
          <EventDetail eventDetail={eventDetailWithDiffVarieties} isReceiving={false} />
        </TestWrapper>
      );

      // Check if the weight adjusted section is not rendered when not in receiving mode
      expect(screen.queryByText('weight-adjusted-by-packing-house')).not.toBeInTheDocument();
      expect(document.getElementById('diff-varieties')).not.toBeInTheDocument();
    });
  });
});
