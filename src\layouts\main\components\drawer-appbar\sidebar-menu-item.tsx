'use client';

import { ExpandMore } from '@mui/icons-material';
import { Box, Collapse, List, ListItemButton, Tooltip } from '@mui/material';
import { MenuItem } from 'hooks/useMenuItems';
import React, { FC, useState } from 'react';
import { useGlobalStore } from 'store/useGlobalStore';
import { motion } from 'framer-motion';
import { useSidebarStyles, useIsSelected } from '../sidebar-hooks';
import { MenuItemContent } from '../menu-item-content';
import { PopoverMenu } from '../popover-menu';
import { useMenuItemActions } from '../menu-item-actions';

const AnimatedIcon = ({ isExpanded }: { isExpanded: boolean }) => (
  <motion.div
    initial={false}
    animate={{
      rotate: isExpanded ? 180 : 0,
    }}
    transition={{
      duration: 0.4,
      delay: 0.15,
      type: 'spring',
      stiffness: 150,
      damping: 25,
    }}
    whileTap={{ rotate: isExpanded ? 170 : 10, opacity: 0.6 }}
    style={{ display: 'inline-block' }}
  >
    <ExpandMore fontSize="small" />
  </motion.div>
);

const AnimatedBox = motion(Box);

interface SidebarMenuItemProps {
  item: MenuItem;
  isOpen: boolean;
}

export const SidebarMenuItem: FC<SidebarMenuItemProps> = ({ item, isOpen }) => {
  const openMenus = useGlobalStore((state) => state.openMenus);
  const isExpanded = !!openMenus[item.itemKey];
  const hasHydrated = useGlobalStore((s) => s._hasHydrated);

  // Popover state for collapsed menu
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [isHovered, setIsHovered] = useState(false);
  const openPopover = Boolean(anchorEl);

  // Custom hooks
  const styles = useSidebarStyles(isOpen);
  const isSelected = useIsSelected();
  const { handleClick, handleChildClick, hasChildren } = useMenuItemActions(item, isOpen);

  const handlePopoverClose = () => {
    setAnchorEl(null);
  };

  const showBorderLeft = Boolean(
    !isOpen || (isOpen && item.isChildren && isSelected(item.path ?? '', item.exact ?? false))
  );
  const isItemSelected = isSelected(item?.path ?? '', item.exact ?? false);

  const getButtonStyles = () => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    py: 2,
    width: '100%',
    boxSizing: 'border-box',
    height: '62px',
    backgroundColor: item.isChildren ? styles.backgroundChildrenColor : 'transparent',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    cursor: 'pointer',
    '&.Mui-selected': {
      color: styles.textPrimarySelectedColor,
      backgroundColor: !isOpen
        ? styles.backgroundActiveColor
        : item.isChildren
          ? styles.backgroundChildrenColor
          : 'transparent',
      borderRadius: !isOpen ? '12px' : 'unset',
      '&:hover': {
        color: styles.textPrimarySelectedColor,
        backgroundColor: styles.backgroundActiveColor,
        borderRadius: !isOpen ? '12px' : 'unset',
      },
    },
    '&:hover:not(.Mui-selected)': {
      color: styles.textPrimarySelectedColor,
      backgroundColor: styles.backgroundActiveColor,
      borderRadius: !isOpen ? '12px' : 'unset',
    },
  });

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: hasHydrated ? 1 : 0 }}
      transition={{
        duration: 0.4,
        delay: 0.1,
        ease: 'easeOut',
      }}
    >
      <AnimatedBox
        translate="no"
        sx={{ width: '100%', boxSizing: 'border-box', p: !isOpen ? '8px' : 0 }}
        transition={{ type: 'spring', stiffness: 400, damping: 25 }}
      >
        <Tooltip title={item.text} placement="right" disableHoverListener={isOpen} arrow translate="no">
          <Box key={item.itemKey} sx={{ width: '100%', height: '100%' }}>
            <motion.div
              whileHover={{ scale: 1.05 }}
              onHoverStart={() => setIsHovered(true)}
              onHoverEnd={() => setIsHovered(false)}
              transition={{ type: 'spring', stiffness: 400, damping: 25 }}
            >
              <ListItemButton
                component="button"
                onClick={(event) => handleClick(event, setAnchorEl)}
                selected={isItemSelected}
                sx={getButtonStyles()}
              >
                <MenuItemContent
                  item={item}
                  isOpen={isOpen}
                  isSelected={isItemSelected ?? false}
                  borderColor={styles.borderLeftColor}
                  plText={styles.plText}
                  showBorderLeft={showBorderLeft}
                  backgroundActiveColor={styles.backgroundActiveColor}
                  isHovered={isHovered}
                />
                {isOpen && item.hasChildren && <AnimatedIcon isExpanded={isExpanded} />}
              </ListItemButton>
            </motion.div>
          </Box>
        </Tooltip>

        {/* Expanded menu items when sidebar is open */}
        {isOpen && hasChildren && (
          <Collapse in={isExpanded} timeout={300} mountOnEnter unmountOnExit collapsedSize={0}>
            <List component="div" disablePadding>
              {item.children?.map((childrenItem, index) => (
                <motion.div
                  key={childrenItem.itemKey}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  transition={{
                    duration: 0.3,
                    delay: index * 0.05,
                    ease: 'easeOut',
                  }}
                >
                  <SidebarMenuItem item={childrenItem} isOpen={isOpen} />
                </motion.div>
              ))}
            </List>
          </Collapse>
        )}

        {/* Popover menu for collapsed state */}
        {!isOpen && hasChildren && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: openPopover ? 1 : 0, y: openPopover ? 0 : -10 }}
            transition={{ duration: 0.2, ease: 'easeOut' }}
          >
            <PopoverMenu
              item={item}
              open={openPopover}
              anchorEl={anchorEl}
              onClose={handlePopoverClose}
              onChildClick={(childItem) => handleChildClick(childItem, handlePopoverClose)}
              isSelected={isSelected}
              styles={styles}
            />
          </motion.div>
        )}
      </AnimatedBox>
    </motion.div>
  );
};
