import camelcaseKeys from 'camelcase-keys';
import { getCookieServer } from 'configs/cookie';
import { get } from 'lodash-es';
import dynamic from 'next/dynamic';
import { redirect } from 'next/navigation';
import { createServerApi } from 'services/api/serverApi';
import { NextPage } from 'next';
import { errorRoute } from 'routes/client-routes';
import { AppConfig } from 'configs/app-config';

const PackingHouseInfo = dynamic(() => import('containers/settings').then((mod) => mod.PackingHouseInformation));

const Index: NextPage = async () => {
  const accessToken = await getCookieServer('access_token_pkg_house');

  const userInfo = accessToken && (await getCookieServer('user_info'));
  const headers: Record<string, string> = {};
  let packingHouseInfoId = '';

  headers['content-type'] = 'application/json';

  if (accessToken) {
    headers['authorization'] = `Bearer ${accessToken}`;
  }

  const api = await createServerApi(AppConfig.CONTROLLER_URL, headers, undefined, 'json');

  if (!userInfo) {
    const userInfoResponse = await api.get('/v1/user/me');

    const userResponseData = userInfoResponse.data;

    const userData = userResponseData.data;

    packingHouseInfoId = get(userData, 'profile.supplier_id.id', '');
  } else {
    packingHouseInfoId = get(userInfo, 'profile.supplier_id.id', '');
  }

  const { data: response } = await api.get(`/v1/durian/packing-house/setting/${packingHouseInfoId}`);

  if (!response.data) {
    return redirect(errorRoute.notFound);
  }

  return <PackingHouseInfo packingHouseInfo={camelcaseKeys(response.data, { deep: true })} />;
};

export default Index;
