import { Box, Button, Stack, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC } from 'react';
import { DetailRow, Drawer } from 'components';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { formatNumberWithCommas } from 'utils';

interface DurianContent {
  id: string;
  name?: string;
  grades: {
    id: string;
    weight: number;
  }[];
}

interface TotalWeightInfoDrawerProps {
  content?: DurianContent[];
  total?: number;
  open: boolean;
  toggle: (open: boolean) => void;
}

export const TotalWeightInfoDrawer: FC<TotalWeightInfoDrawerProps> = ({ content, total, open, toggle }) => {
  const { getGradeLabel, getVarietyLabel } = useMasterDataStore();

  const commonT = useTranslations('common');
  const receivingTranslation = useTranslations('receive');

  const onClose = () => {
    toggle(false);
  };

  const drawerTitle = `${receivingTranslation('total-weight-calculation')}`;

  const footerElement = (
    <Box
      sx={{
        px: 2,
        py: 1.5,
        borderTop: 1,
        borderColor: 'divider',
        display: 'flex',
        justifyContent: 'space-between',
        gap: 1,
      }}
    >
      <Button fullWidth variant="outlined" onClick={onClose}>
        {commonT('close-modal-btn')}
      </Button>
    </Box>
  );

  return (
    <Drawer
      anchor="right"
      drawerTitle={drawerTitle}
      open={open}
      onClose={onClose}
      hasActionBtn={false}
      footerElement={footerElement}
    >
      <Box
        component="div"
        sx={{ width: '100%', p: '24px 16px', display: 'flex', flexDirection: 'column', gap: '16px' }}
      >
        {content &&
          content.map((item) => (
            <Stack
              spacing={1}
              sx={{
                mb: 2,
                width: '100%',
                borderBottom: '1px solid #E5E5EA',
              }}
              key={item.id}
            >
              <Typography color="textPrimary" textAlign="start" fontWeight="medium" component="div">
                {item?.name ? item.name : getVarietyLabel(item.id)}
              </Typography>
              {item.grades?.map((durianGrade) => (
                <DetailRow
                  key={durianGrade.id}
                  title={getGradeLabel(durianGrade.id)}
                  content={`${formatNumberWithCommas(durianGrade.weight)} ${commonT('kg')}`}
                  noBorder
                  sx={{ flexDirection: 'row' }}
                />
              ))}
            </Stack>
          ))}

        <DetailRow
          title={
            <Typography color="textPrimary" textAlign="start" fontWeight="medium" component="div">
              {receivingTranslation('total')}
            </Typography>
          }
          content={`${formatNumberWithCommas(total)} ${commonT('kg')}`}
          sx={{ flexDirection: 'row' }}
          noBorder
        />
      </Box>
    </Drawer>
  );
};
