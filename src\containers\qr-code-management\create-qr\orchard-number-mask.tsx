'use client';

import { useTranslations } from 'next-intl';
import { forwardRef } from 'react';
import { IMaskInput } from 'react-imask';

interface CustomProps {
  onChange: (event: { target: { name: string; value: string } }) => void;
  name: string;
}

export const OrchardNumberMask = forwardRef<HTMLInputElement, CustomProps>(function Mask(props, ref) {
  const { onChange, ...other } = props;

  const commonT = useTranslations('common');

  return (
    <IMaskInput
      {...other}
      mask={`${commonT('prefix-plot')} ##-####-##-###-######`}
      definitions={{
        '#': /^\d/,
      }}
      unmask="typed"
      inputRef={ref}
      onAccept={(value: string) => {
        // prevent form trigger to render error when reset form
        if (!value) return;
        onChange({ target: { name: props.name, value } });
      }}
      overwrite
    />
  );
});
