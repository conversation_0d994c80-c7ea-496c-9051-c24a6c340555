import { useMutation, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from 'hooks/queries/_key';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { clientRoutes } from 'routes/client-routes';
import { updateReceive } from 'services/event.service';
import { useToastStore } from 'store/useToastStore';
import { ReceiveUpdateRequest } from 'types';
import toastMessages from 'utils/toastMessages';

export function useUpdateReceiveMutate() {
  const router = useRouter();
  const setToast = useToastStore((state) => state.setToast);
  const receiveTranslation = useTranslations('receive');
  const commonTranslation = useTranslations('common');

  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (args: ReceiveUpdateRequest) => updateReceive(args),
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: [queryKeys.EVENT, queryKeys.HARVEST, queryKeys.RECEIVING],
      });

      setToast({
        message: receiveTranslation('variety-update-success'),
        type: 'success',
      });

      router.push(clientRoutes.eventReceiving);
    },
    onError: () => {
      toastMessages.error(commonTranslation('data-outdated-error'));
    },
  });
}
