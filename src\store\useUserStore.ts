import { create } from 'store';
import { User } from 'types/user';

export type UserState = {
  user?: User;
  _hasHydrated?: boolean;
};

const defaultState: UserState = {
  user: undefined,
};

export type UserStateAction = {
  setUserInfo: (userResponse?: User) => void;
};

export const useUserStore = create<UserState & UserStateAction>((set) => ({
  ...defaultState,
  setUserInfo: (response) => {
    set({
      user: response
        ? {
            ...response,
          }
        : undefined,
    });
  },
}));
