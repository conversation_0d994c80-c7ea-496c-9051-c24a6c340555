import { Receipt } from './master-data';

export interface CreateShipment {
  status: 'draft' | 'published' | 'completed';
  shipmentName: string;
  receivingProductIds?: string[];
  receipt?: Receipt;
  latitude: number;
  longitude: number;
  packages?: ShipmentPackage[];
  documentIds?: string[];
  shipmentPhotoIds?: string[];
  formStep?: number;
}

export interface ShipmentPackage {
  brandName: string;
  productType: string;
  varietyId: string;
  gradeId: string;
  weightKg: number;
  numberOfBoxes: number;
  qrCode: string;
  batchlot: string;
  varietyName?: string;
}
