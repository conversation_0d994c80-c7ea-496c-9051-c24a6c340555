import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteHarvesting } from 'services/event.service';
import { queryKeys } from 'hooks/queries/_key';
import { useToastStore } from 'store/useToastStore';
import { useTranslations } from 'next-intl';
import toastMessages from 'utils/toastMessages';

export function useDeleteDraftMutate(type: 'harvest' | 'shipment' = 'harvest') {
  const queryClient = useQueryClient();
  const setToast = useToastStore((state) => state.setToast);
  const receivingTranslation = useTranslations('receive');
  const shipmentTranslation = useTranslations('shipment');
  const commonTranslation = useTranslations('common');

  return useMutation({
    mutationFn: (id: string) => deleteHarvesting(id),
    onSuccess: async () => {
      await (type === 'harvest'
        ? queryClient.invalidateQueries({
            queryKey: [queryKeys.HARVEST],
          })
        : queryClient.invalidateQueries({
            queryKey: ['shipment-event-data'],
          }));

      const message =
        type === 'harvest'
          ? receivingTranslation('delete-harvest-success')
          : shipmentTranslation('delete-shipment-success');

      setToast({
        message,
        type: 'success',
      });
    },
    onError: () => {
      toastMessages.error(commonTranslation('data-outdated-error'));
    },
  });
}
