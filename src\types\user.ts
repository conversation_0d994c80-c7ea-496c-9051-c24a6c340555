export type Supplier = {
  id: string;
  status: string;
  name: string;
  description: string;
  images: string[];
  address: string;
  locationLatitude: string;
  locationLongitude: string;
  type: string[];
  globalLocationNumber: string;
  city: string;
  country: string;
  bindToWorkflowNode: number[];
};

export type UserProfile = {
  id: number;
  status: string;
  role: string;
  supplierId: Supplier;
  nationalId: string;
  roleLabel: {
    en: string;
    th: string;
  };
};

export type UserState = {
  userRole: string;
  firstName: string;
  lastName: string;
  manufactureImage: string;
  manufactureName: string;
  manufactureAddress: string;
  gln: string;
};

export type User = {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  status: string;
  profile: UserProfile;
  avatar: {
    filenameDisk: string;
  };
  phoneNumber: string;
  v?: number;
};
