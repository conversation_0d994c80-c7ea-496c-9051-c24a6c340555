import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToastStore } from 'store/useToastStore';
import { useTranslations } from 'next-intl';
import toastMessages from 'utils/toastMessages';
import { deleteAvailableQr } from 'services/qr.service';
import { getListingQueryKey } from 'hooks/queries/_key';

export function useDeleteAvailableQrMutate() {
  const queryClient = useQueryClient();
  const setToast = useToastStore((state) => state.setToast);
  const qrT = useTranslations('qr');
  const commonT = useTranslations('common');

  return useMutation({
    mutationFn: deleteAvailableQr,
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: getListingQueryKey('QR'),
      });

      const message = qrT('message-delete-success');

      setToast({
        message,
        type: 'success',
      });
    },
    onError: () => {
      toastMessages.error(commonT('data-outdated-error'));
    },
  });
}
