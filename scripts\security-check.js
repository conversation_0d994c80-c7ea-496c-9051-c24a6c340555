/* eslint-disable @typescript-eslint/no-require-imports */
// scripts/security-check.js
const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

const BUILD_DIR = '.next/static/chunks'; // Location of client bundles

// Define suspicious keywords to scan for
const SUSPICIOUS_KEYS = ['API_KEY', 'SECRET', 'TOKEN', 'PRIVATE', 'FIREBASE'];

// Security headers to check
const REQUIRED_HEADERS = {
  'x-frame-options': 'DENY',
  'x-content-type-options': 'nosniff',
  'x-xss-protection': '1; mode=block',
  'strict-transport-security': 'max-age=31536000; includeSubDomains',
  'content-security-policy': 'frame-ancestors \'none\'',
  'referrer-policy': 'strict-origin-when-cross-origin'
};

// 1. Check process.env for exposed public keys
const checkEnvVars = () => {
  const exposed = Object.keys(process.env).filter(k =>
    k.startsWith('NEXT_PUBLIC_') || SUSPICIOUS_KEYS.some(s => k.includes(s))
  );

  console.log('\n🔍 Checking process.env for client-exposed keys...');
  exposed.forEach(key => {
    console.warn(`🚨 Potentially exposed key: ${key} = ${process.env[key]}`);
  });

  if (exposed.length === 0) {
    console.log('✅ No obviously exposed keys in process.env');
  }
};

// 2. Scan production JS bundles for hardcoded secrets
const scanBuildForSecrets = () => {
  console.log('\n🔍 Scanning compiled frontend JS for hardcoded secrets...');
  let found = [];

  const entries = fs.readdirSync(BUILD_DIR, { withFileTypes: true });

  entries.forEach(entry => {
    if (!entry.isFile()) return; // ✅ Skip directories

    const file = entry.name;
    const fullPath = path.join(BUILD_DIR, file);
    const content = fs.readFileSync(fullPath, 'utf-8');

    SUSPICIOUS_KEYS.forEach(key => {
      const regex = new RegExp(`${key}\\s*[:=]\\s*['"]?([^'"\\s]+)['"]?`, 'gi');
      const match = regex.exec(content);
      if (match) {
        found.push({ file, key, value: match[1] });
      }
    });
  });

  if (found.length === 0) {
    console.log('✅ No hardcoded keys found in JS bundles.');
  } else {
    console.warn('🚨 Found suspicious hardcoded keys:');
    console.table(found);
  }
};

// 3. Check security headers on running application
const checkSecurityHeaders = async (url = 'http://localhost:3000') => {
  console.log(`\n🔍 Checking security headers for ${url}...`);

  return new Promise((resolve) => {
    const client = url.startsWith('https') ? https : http;

    const req = client.request(url, { method: 'HEAD' }, (res) => {
      const headers = res.headers;
      const issues = [];

      // Check for required headers
      Object.entries(REQUIRED_HEADERS).forEach(([header]) => {
        const actualValue = headers[header.toLowerCase()];

        if (!actualValue) {
          issues.push(`❌ Missing header: ${header}`);
        } else if (header === 'content-security-policy') {
          // Special check for frame-ancestors
          if (!actualValue.includes("frame-ancestors 'none'")) {
            issues.push(`⚠️  CSP missing frame-ancestors 'none': ${actualValue}`);
          } else {
            console.log(`✅ frame-ancestors 'none' found in CSP`);
          }
        } else {
          console.log(`✅ ${header}: ${actualValue}`);
        }
      });

      // Check for dangerous headers
      const dangerousHeaders = ['server', 'x-powered-by'];
      dangerousHeaders.forEach(header => {
        if (headers[header.toLowerCase()]) {
          issues.push(`⚠️  Information disclosure: ${header} = ${headers[header.toLowerCase()]}`);
        }
      });

      if (issues.length === 0) {
        console.log('✅ All security headers look good!');
      } else {
        console.log('\n🚨 Security header issues found:');
        issues.forEach(issue => console.log(issue));
      }

      resolve(issues);
    });

    req.on('error', (err) => {
      console.log(`❌ Failed to check headers: ${err.message}`);
      console.log('💡 Make sure your application is running on the specified URL');
      resolve(['Connection failed']);
    });

    req.setTimeout(5000, () => {
      req.destroy();
      console.log('❌ Request timeout - application may not be running');
      resolve(['Timeout']);
    });

    req.end();
  });
};

// 4. Check Next.js configuration for security settings
const checkNextJsConfig = () => {
  console.log('\n🔍 Checking Next.js configuration for security settings...');

  const configPath = path.join(process.cwd(), 'next.config.ts');

  if (!fs.existsSync(configPath)) {
    console.log('❌ next.config.js not found');
    return;
  }

  const configContent = fs.readFileSync(configPath, 'utf-8');

  // Check for security headers configuration
  if (configContent.includes('X-Frame-Options') || configContent.includes('frame-ancestors')) {
    console.log('✅ Frame protection headers found in config');
  } else {
    console.log('⚠️  No frame protection headers found in next.config.js');
  }

  // Check for CSP configuration
  if (configContent.includes('Content-Security-Policy')) {
    console.log('✅ CSP configuration found');
  } else {
    console.log('⚠️  No CSP configuration found in next.config.js');
  }
};

// Main execution
const runSecurityCheck = async () => {
  console.log('🛡️  Security Check Started');
  console.log('========================');

  checkEnvVars();

  if (fs.existsSync(BUILD_DIR)) {
    scanBuildForSecrets();
  } else {
    console.log('\n⚠️  Build directory not found. Run `npm run build` first to scan bundles.');
  }

  checkNextJsConfig();

  // Check headers on localhost (adjust URL as needed)
  await checkSecurityHeaders('http://localhost:3000');

  console.log('\n🛡️  Security Check Complete');
};

runSecurityCheck().catch(console.error);
