/* eslint-disable @typescript-eslint/no-require-imports */
// updateTranslations.js
const fs = require('fs');
const path = require('path');

// Function to load mapping from JSON file
function loadMappingFromJSON() {
  const jsonPath = path.resolve(__dirname, '../translations/thai-english-mapping.json');

  if (!fs.existsSync(jsonPath)) {
    console.error(`❌ Could not find thai-english-mapping.json at ${jsonPath}`);
    console.log('💡 Please create the JSON file first');
    process.exit(1);
  }

  const jsonContent = fs.readFileSync(jsonPath, 'utf8');
  return JSON.parse(jsonContent);
}

const mapping = loadMappingFromJSON();

function replaceValues(obj) {
  if (Array.isArray(obj)) return obj.map(replaceValues);
  if (obj && typeof obj === 'object') {
    return Object.fromEntries(
      Object.entries(obj).map(([k,v]) => [k, replaceValues(v)])
    );
  }
  return (typeof obj === 'string' ? (mapping[obj] || obj) : obj);
}

async function main() {
  try {
    const enPath = path.resolve(__dirname, '../locales/en.json');
    if (!fs.existsSync(enPath)) {
      console.error(`❌ Could not find en.json at ${enPath}`);
      console.log('💡 Creating messages directory and en.json file...');

      // Create messages directory if it doesn't exist
      const messagesDir = path.dirname(enPath);
      if (!fs.existsSync(messagesDir)) {
        fs.mkdirSync(messagesDir, { recursive: true });
      }

      // Create en.json with keys from mapping
      const englishTranslations = {};
      Object.keys(mapping).forEach(key => {
        englishTranslations[key] = key;
      });

      await fs.promises.writeFile(enPath, JSON.stringify(englishTranslations, null, 2), 'utf8');
      console.log(`✅ Created ${enPath}`);
    }

    const enData = JSON.parse(await fs.promises.readFile(enPath, 'utf8'));
    const thData = replaceValues(enData);

    const thPath = path.resolve(__dirname, '../locales/th.json');
    await fs.promises.writeFile(thPath, JSON.stringify(thData, null, 2), 'utf8');
    console.log(`✅ Generated ${thPath}`);
    console.log(`📊 Total translations: ${Object.keys(mapping).length}`);
  } catch (err) {
    console.error('❌ Error during translation:', err);
    process.exit(1);
  }
}

main();
