import { FileUploadOutlined } from '@mui/icons-material';
import { Box, Button, styled } from '@mui/material';
import { IMAGE_EXTENSIONS } from 'constant/common';
import { useTranslations } from 'next-intl';
import React, { useState } from 'react';
import { uploadFileService } from 'services/internal.service';
import { UploadFile } from 'store/useCreateShipmentStore';
import { theme } from 'styles/theme';
import { validateFileImage } from 'utils';
import { sendEvent } from 'utils/gtag';
import toastMessages from 'utils/toastMessages';
import { v4 } from 'uuid';

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

export interface ImagePreview {
  file?: File;
  preview: string;
  id?: string;
}

interface UploadPlotImageProps {
  onChange: (imgString: string) => void;
}

const UploadPlotImage: React.FC<UploadPlotImageProps> = ({ onChange }) => {
  const [uploadFileImage, setUploadFileImage] = useState<UploadFile[]>([]);

  const commonT = useTranslations('common');
  const formT = useTranslations('form');

  const uploadFile = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    const response = await uploadFileService(formData);
    sendEvent('uploaded_photos');
    const filenameData = response.data.filenameDisk;

    return filenameData;
  };

  const uploadPhoto = async (file: UploadFile) => {
    if (!file.file) return;
    const tempArr = uploadFileImage.concat({
      ...file,
      isUploading: true,
    });
    setUploadFileImage(tempArr);

    const fileResponse = await uploadFile(file.file);

    if (fileResponse) {
      const arrMap = tempArr.map((photo) => {
        if (photo.id === file.id) {
          return {
            ...photo,
            url: fileResponse ?? '',
            isUploading: false,
          };
        }
        return photo;
      });

      setUploadFileImage(arrMap);
      onChange(fileResponse);
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (!file) return;

    const { isValid } = validateFileImage(file);

    if (!isValid) {
      toastMessages.error(formT('invalid-image'));
      e.target.value = '';
      return;
    }

    const imgId = v4();
    const fileUpload = {
      id: imgId,
      name: file.name,
      size: file.size,
      type: file.type,
      url: '',
      file,
    };

    try {
      await uploadPhoto(fileUpload);
    } catch {
      toastMessages.error(commonT('common-error'));
      setUploadFileImage([...uploadFileImage.filter((img) => img.id !== fileUpload.id)]);
    }
    e.target.value = '';
  };

  return (
    <Box sx={{ width: '100%', display: 'flex', gap: '12px' }}>
      {uploadFileImage.length < 1 && (
        <Button
          component="label"
          role={undefined}
          sx={{
            width: '120px',
            height: '120px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            border: 'none',
            cursor: 'pointer',
            position: 'relative',
          }}
          variant="outlined"
        >
          <VisuallyHiddenInput
            style={{
              width: '100%',
              height: '100%',
              position: 'absolute',
              right: 0,
              bottom: 0,
              zIndex: 2,
            }}
            accept={IMAGE_EXTENSIONS.join(',')}
            type="file"
            multiple={false}
            onChange={handleFileChange}
          />
          <FileUploadOutlined sx={{ color: theme.palette.customColors.black }} />
        </Button>
      )}
    </Box>
  );
};

export default UploadPlotImage;
