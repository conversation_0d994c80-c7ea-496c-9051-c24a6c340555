#!/bin/bash
set -e

# === Default values ===
BUMP_TYPE="patch"
UPDATE_PACKAGE=false
AUTO_CONFIRM=false
DEFAULT_BRANCH="main"
VERSION_ONLY=false

# === Parse CLI args ===
for arg in "$@"; do
  case $arg in
    --bump=*)
      BUMP_TYPE="${arg#*=}"
      ;;
    --yes|--ci)
      AUTO_CONFIRM=true
      ;;
    --version-only)
      VERSION_ONLY=true
      ;;
    *)
      echo "❌ Unknown argument: $arg"
      exit 1
      ;;
  esac
done

# === GH CLI authentication (non-interactive with GITHUB_TOKEN) ===
if ! command -v gh &>/dev/null; then
  echo "❌ GitHub CLI (gh) is not installed."
  exit 1
fi

if [ -n "$GITHUB_TOKEN" ] && [ -z "$CI" ]; then
  echo "🔐 Authenticating gh CLI using GITHUB_TOKEN..."
  gh auth setup-git
  echo "$GITHUB_TOKEN" | gh auth login --with-token > /dev/null 2>&1 || true
fi

if ! gh auth status &>/dev/null; then
  echo "❌ GitHub CLI is not authenticated."
  exit 1
fi

# === Check jq if needed ===
if ! command -v jq &>/dev/null && [ "$UPDATE_PACKAGE" = true ]; then
  echo "❌ 'jq' is required to update package.json"
  exit 1
fi

# === Fetch refs ===
git fetch --prune origin "+refs/tags/*:refs/tags/*"
git fetch --prune origin

# === Latest version tag ===
latest_tag=$(git tag -l | grep -v -- "-prod$" | sort -rV | head -n 1)
if [ -z "$latest_tag" ]; then
  latest_tag="v0.0.0"
fi
version="${latest_tag#v}"
IFS='.' read -r major minor patch <<< "$version"

# === Bump version ===
case $BUMP_TYPE in
  major) major=$((major + 1)); minor=0; patch=0 ;;
  minor) minor=$((minor + 1)); patch=0 ;;
  patch) patch=$((patch + 1)) ;;
  *)
    echo "❌ Invalid bump type: $BUMP_TYPE"
    exit 1
    ;;
esac

new_version="v$major.$minor.$patch"

# If only version output is requested, print and exit
if [ "$VERSION_ONLY" = true ]; then
  echo "$new_version"
  exit 0
fi

# === Determine release branch name ===
case $BUMP_TYPE in
  major) release_branch="release/stg-v$major.0.0" ;;
  minor) release_branch="release/stg-v$major.$minor.0" ;;
  patch) release_branch="release/stg-v$major.$minor.$patch" ;;
esac

# === Confirm ===
echo "📦 Next version: $new_version"
echo "🔁 Branch: $release_branch"
echo "📂 Update package.json: $UPDATE_PACKAGE"

if [ "$AUTO_CONFIRM" = false ]; then
  read -p "Continue? (y/n) [y]: " confirm
  confirm="${confirm:-y}"
  if [[ "$confirm" != "y" ]]; then
    echo "❌ Cancelled."
    exit 1
  fi
else
  echo "✅ Auto-confirm enabled. Continuing..."
fi

# === Create or switch to release branch ===
echo "🔄 Pulling latest $DEFAULT_BRANCH..."
git fetch origin "$DEFAULT_BRANCH"
git checkout "$DEFAULT_BRANCH"
git pull origin "$DEFAULT_BRANCH"

if git ls-remote --exit-code --heads origin "$release_branch" &>/dev/null; then
  echo "🔀 Checking out existing release branch $release_branch..."
  git checkout "$release_branch"
  git pull origin "$release_branch"
else
  echo "🌱 Creating new release branch $release_branch from $DEFAULT_BRANCH..."
  git checkout -b "$release_branch"
fi

# === Push the branch (force if needed) ===
git push -u origin "$release_branch" --force-with-lease

# === Set Git user for commits and tags ===
# You can configure Git user in several ways:
# 1. Pass environment variables: GIT_AUTHOR_NAME and GIT_AUTHOR_EMAIL
# 2. In GitHub Actions workflow, set them with:
#    env:
#      GIT_AUTHOR_NAME: "Your Name"
#      GIT_AUTHOR_EMAIL: "<EMAIL>"
git config user.name "${GIT_AUTHOR_NAME:-github-actions[bot]}"
git config user.email "${GIT_AUTHOR_EMAIL:-github-actions[bot]@users.noreply.github.com}"

git tag -a "$new_version" -m "Release $new_version"
git push origin "$new_version"

# === GitHub Release ===
gh release create "$new_version" \
  --title "stg-$new_version" \
  --target "$release_branch" \
  --generate-notes

echo "✅ GitHub release '$new_version' created from branch '$release_branch'"
# Output for GitHub Actions
echo "version=$new_version" >> $GITHUB_OUTPUT
echo "release_branch=$release_branch" >> $GITHUB_OUTPUT
