# DT Packaging House Web

## UT coverage
![Coverage](./coverage-brand/coverage.svg)

## Table of Contents

- [Project Structure](#project-structure)
- [Environment Variables](#environment-variables)
- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Development Setup](#development-setup)
  - [Production Setup](#production-setup)
- [Testing](#testing)
  - [Unit Tests](#unit-tests)
  - [Running Tests](#running-tests)
  - [Test Coverage](#test-coverage)
- [Docker Configuration](#docker-configuration)
  - [Development Docker](#development-docker)
  - [Production Docker](#production-docker)
  - [Testing Docker Configurations](#testing-docker-configurations)
- [API Routes](#api-routes)

## Project Structure

```
dt-packaging-house/
├── .github/                # GitHub Actions workflows
├── .next/                  # Next.js build output (generated)
├── .yarn/                  # Yarn 4 configuration
├── public/                 # Static assets
├── scripts/                # Utility scripts
│   └── test-dockerfile.sh  # Docker testing script
├── src/                    # Application source code
│   ├── app/                # Next.js App Router
│   │   ├── api/            # API Routes
│   │   └── ...             # Pages and components
│   ├── components/         # Reusable UI components
│   │   └── durian-variety/ # Durian variety component with tests
│   ├── containers/         # Page-level containers
│   ├── store/              # State management
│   ├── styles/             # Theme and styling
│   └── utils/              # Utility functions
├── coverage-brand/         # Test coverage reports
├── Dockerfile              # Production Docker configuration
├── Dockerfile.local        # Development Docker configuration
├── jest.config.js          # Jest testing configuration
├── package.json            # Project dependencies and scripts
└── yarn.lock               # Yarn lockfile
```

## Getting Started

### Prerequisites

- Node.js 22 or later
- Yarn 4.9.1 (defined as the project's package manager)
- Docker and Docker Compose (for containerized environments)

### Development Setup

1. Clone the repository:

   ```bash
   git clone <repository-url>
   cd dt-packaging-house
   ```

2. Install dependencies:

   ```bash
   yarn install
   ```

3. Run the development server:

   ```bash
   yarn dev
   ```

4. Using Docker for development:
   ```bash
   docker build -t dt-packaging-house-dev -f Dockerfile.local .
   docker run -p 3000:3000 -v $(pwd):/app dt-packaging-house-dev
   ```

### Production Setup

1. Build the application:

   ```bash
   yarn build
   ```

2. Start the production server:

   ```bash
   yarn start
   ```

3. Using Docker for production:
   ```bash
   docker build -t dt-packaging-house-prod .
   docker run -p 3000:3000 \
     -e NEXT_PUBLIC_APP_VERSION=1.0.0 \
     dt-packaging-house-prod
   ```

## Testing

The project uses Jest and React Testing Library for unit testing components and utilities.

### Unit Tests

Component tests are located alongside their respective components:
- `src/components/durian-variety/durian-variety.test.tsx` - Tests for DurianVarietyBox component
- Tests cover rendering, user interactions, prop variations, and edge cases

### Running Tests

```bash
# Run all tests
yarn test

# Run tests in watch mode during development
yarn test:watch

# Run tests with coverage report
yarn test:coverage

# Run tests for specific component
yarn test durian-variety
```

### Test Coverage

Test coverage reports are generated in the `coverage-brand/` directory and include:
- Line coverage
- Branch coverage
- Function coverage
- Statement coverage

The coverage badge is automatically updated and displayed at the top of this README.

## Docker Configuration

### Development Docker

The project includes a development Docker configuration (`Dockerfile.local`) designed for local development:

- Mounts the local file system for hot reloading
- Sets up Node.js 20 with Alpine Linux
- Runs the application in development mode with hot reloading

### Production Docker

The production Docker configuration (`Dockerfile`) is optimized for deployment:

- Multi-stage build to minimize image size
- Runtime environment variables for configuration
- Non-root user for security
- Standalone Next.js deployment

### Testing Docker Configurations

The project includes a script to test Docker configurations:

```bash
# Production Docker build
yarn docker:compose:up

# Production Docker down
yarn docker:compose:down

## API Routes

The application includes API routes for various functions:

- `/api/directus/upload-file`: Handles file uploads to Directus
- Other API routes handling business logic and external integrations

## GitHub Actions

The repository includes GitHub Actions workflows for:

- Pull Request validation
- Building and testing Docker images
- Deployment pipelines

See `.github/workflows` for the specific workflow configurations.

# 🚀 `release.sh` – GitHub Release Automation

Automate your version tagging, release branching, and GitHub release creation using this powerful shell script. It supports semantic versioning and version-based release branch strategies out of the box.

---

## 🧰 Features
- ✅ Bump version (`patch`, `minor`, or `major`)
- ✅ Clean versioned release branches (e.g., `stg/release/1.3.x`)
- ✅ Auto-update `package.json` version (optional)
- ✅ Push annotated git tag
- ✅ Create GitHub Releases with auto-generated changelog notes
---

## 🖥️ Requirements

- [GitHub CLI (`gh`)](https://cli.github.com/)
- [`jq`](https://stedolan.github.io/jq/) for updating `package.json`
- Git access to the target repository
---
## 📦 Usage

```bash
# Bump patch version and create release
./scripts/release.sh
# Bump minor version and update package.json
./scripts./release.sh --bump=minor
# Start a new major version
./scripts./release.sh --bump=major
