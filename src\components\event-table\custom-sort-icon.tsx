import { ColumnHeaderSortIconPropsOverrides, GridColumnHeaderSortIconProps } from "@mui/x-data-grid";
import { CustomSortIcon } from "assets/react-icons";
import { FC } from "react";

export const CustomSortIconComponent: FC<GridColumnHeaderSortIconProps & ColumnHeaderSortIconPropsOverrides> = (props) => {
  return (
    <CustomSortIcon
      asc={props.direction === 'asc'}
      desc={props.direction === 'desc'}
      fontSize="small"
    />
  );
};
