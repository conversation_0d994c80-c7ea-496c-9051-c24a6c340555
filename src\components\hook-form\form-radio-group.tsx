import { FormControl, FormControlLabel, Radio, RadioGroup, Typography } from '@mui/material';
import { ReactElement } from 'react';
import { Control, Controller, FieldErrors, FieldValues, Path } from 'react-hook-form';

export interface FormRadioGroupProps<TFormValues extends FieldValues> {
  readonly name: Path<TFormValues>;
  readonly control: Control<TFormValues>;
  readonly errors?: FieldErrors<TFormValues>;
  readonly label?: string;
  readonly required?: boolean;
  readonly requiredMessage?: string;
  readonly options: Array<{ value: string; label: string }>;
  readonly validate?: Record<string, (value: unknown) => boolean | string>;
  readonly fullWidth?: boolean;
  readonly onChange?: (value: string) => void;
}

export function FormRadioGroup<TFormValues extends FieldValues>({
  name,
  control,
  errors,
  label,
  required = false,
  requiredMessage,
  options,
  validate,
  fullWidth = true,
  onChange,
}: Readonly<FormRadioGroupProps<TFormValues>>): ReactElement {
  const rules = {
    ...(required && { required: requiredMessage ?? `${label} is required` }),
    ...(validate && { validate }),
  };

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field }) => (
        <FormControl fullWidth={fullWidth} error={!!errors?.[name]}>
          {label && (
            <Typography sx={{ fontSize: '18px', mb: 1 }} variant="caption">
              {label}{' '}
              <Typography sx={{ visibility: required ? 'visible' : 'hidden' }} variant="caption" color="error">
                *
              </Typography>
            </Typography>
          )}

          <RadioGroup
            {...field}
            onChange={(event) => {
              field.onChange(event.target.value);
              onChange?.(event.target.value);
            }}
          >
            {options.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio size="small" />}
                label={option.label}
                sx={{
                  color: field.value === option.value ? 'text.primary' : 'text.secondary',
                  backgroundColor: field.value === option.value ? 'action.hover' : 'transparent',
                  borderRadius: 1,
                  padding: '4px',
                  transition: 'all 0.2s ease-in-out',
                  '& .MuiFormControlLabel-label': {
                    fontWeight: field.value === option.value ? 500 : 400,
                  },
                  '& .MuiRadio-root': {
                    color: field.value === option.value ? 'primary.main' : 'text.secondary',
                  },
                  width: '100%',
                  margin: 0,
                }}
                slotProps={{
                  typography: {
                    variant: 'body1',
                    fontSize: '15px',
                    fontWeight: field.value === option.value ? 500 : 400,
                    whiteSpace: 'break-spaces',
                    width: '100%',
                  },
                }}
              />
            ))}
          </RadioGroup>
          {errors?.[name] && (
            <Typography variant="caption" color="error" sx={{ mt: 1 }}>
              {errors[name]?.message as string}
            </Typography>
          )}
        </FormControl>
      )}
    />
  );
}
