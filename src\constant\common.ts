import { EventStatusEnum, RecordedByEnum } from 'types';

export const IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png'];
export const PDF_EXTENSIONS = ['.pdf'];

export const ACCEPT_UPLOAD_FILES = [...IMAGE_EXTENSIONS, ...PDF_EXTENSIONS];

export const ACCEPTED_FILE_TYPES = ['application/pdf', 'image/png', 'image/jpeg'];
export const ACCEPTED_IMAGE_TYPES = ['image/png', 'image/jpeg'];
export const FORMAT_DATE_DD_MMMM_YYYY = 'DD MMMM YYYY';
export const FORMAT_DATE_DD_MM_BBBB = 'DD/MM/BBBB';

export const DD_MMMM_YYYY_WITH_DASH = 'DD/MM/YYYY';
export const PAGE_SIZE_OPTIONS = [10, 25, 50, 100];
export const RECORDED_BY_OPTIONS = [RecordedByEnum.FARMER, RecordedByEnum.CUTTER];
export const STATUS_OPTIONS = [
  EventStatusEnum.DRAFT,
  EventStatusEnum.SEALED,
  EventStatusEnum.WAITING,
  EventStatusEnum.RECEIVED,
  EventStatusEnum.REJECTED,
];
export const MAX_FILE_SIZE = 15 * 1024 * 1024;
export const RECEIVING_TYPE = 'receiving';

export const FORMAT_DATE_HOUR_MINUTE = 'DD/MM/YYYY HH:mm';

export const FORMAT_HOUR_MINUTE_DATE = 'HH:mm DD/MM/YYYY';

export const FORMAT_DATE_DD_MMM_YYYY = 'DD MMM YYYY';
export const FORMAT_DATE_DD_MMM_BBBB = 'DD MMM BBBB';

export const FIXED_LOCATION = {
  FARMER: {
    longitude: 100.5217,
    latitude: 13.8591,
  },
  TRANSPORTATION: {
    longitude: 100.5283,
    latitude: 13.9126,
  },
  RECEIVING: {
    longitude: 101.6126,
    latitude: 12.8514,
  },
  SHIPMENT: {
    longitude: 100.5283,
    latitude: 13.9126,
  },
  BORDER_CHECKPOINT: {
    longitude: 107.5372164,
    latitude: 13.1619474,
  },
};

export const PLOT_ID_LENGTH = 20;
export const PLOT_GAP_NUMBER_LENGTH = 17;
