name: Run Tests

on:
  pull_request:
    branches:
      - main

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - name: ⬇️ Checkout code
        uses: actions/checkout@v4

      - name: 🧵 Enable and install Yarn 4.9.1 via Corepack
        run: |
          corepack enable
          corepack prepare yarn@4.9.1 --activate
          yarn --version

      - name: ⚙️ Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'

      - name: 📦 Install dependencies
        run: yarn install --immutable

      - name: 🧪 Run lint
        run: yarn lint

      - name: 🧪 Run unit tests
        run: yarn test:coverage
