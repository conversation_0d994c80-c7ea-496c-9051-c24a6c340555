import { redirect } from 'next/navigation';

import { AxiosError } from 'axios';
import camelcaseKeys from 'camelcase-keys';
import { NextPage } from 'next';
import { errorRoute } from 'routes/client-routes';
import { PackingHouseDetail } from 'types';

import { createServerEventService } from 'app/(main)/_util/api-server';
import ClientWrapper from '../_container/client-wrapper';

type IndexProps = {
  params: Promise<{
    id: string;
  }>;
};

const Index: NextPage<IndexProps> = async ({ params }) => {
  const { id } = await params;
  const api = await createServerEventService(3600); // Cache for 1 hour

  let shipmentDetail: PackingHouseDetail;

  try {
    const { data: response } = await api.get(`/v1/search/events/shipment/${id}`);

    if (!response?.data) {
      return redirect(errorRoute.notFound);
    }

    shipmentDetail = camelcaseKeys(response.data, { deep: true });
  } catch (error) {

    if (error instanceof AxiosError) {
      if (error.status === 404) {
        return redirect(errorRoute.notFound);
      }
    }

    throw error;
  }

  return <ClientWrapper id={id} eventDetail={shipmentDetail} />;
};

export default Index;
