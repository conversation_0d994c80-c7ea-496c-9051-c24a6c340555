import { useMutation, useQueryClient } from '@tanstack/react-query';
import { sealShipment } from 'services/event.service';
import { useRouter } from 'next/navigation';
import { clientRoutes } from 'routes/client-routes';
import { queryKeys } from 'hooks/queries/_key';
import { SealShipmentRequest } from 'types';
import { useToastStore } from 'store/useToastStore';
import { useTranslations } from 'next-intl';
import toastMessages from 'utils/toastMessages';

export function useSealShipmentMutate() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const setToast = useToastStore((state) => state.setToast);
  const shipmentTranslation = useTranslations('shipment');
  const commonTranslation = useTranslations('common');

  return useMutation({
    mutationFn: (args: SealShipmentRequest) => sealShipment(args),
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: [queryKeys.SHIPMENT, queryKeys.EVENT],
      });

      setToast({
        message: shipmentTranslation('seal-success'),
        type: 'success',
      });

      router.push(clientRoutes.shipment);
    },
    onError: () => {
      toastMessages.error(commonTranslation('data-outdated-error'));
    },
  });
}
