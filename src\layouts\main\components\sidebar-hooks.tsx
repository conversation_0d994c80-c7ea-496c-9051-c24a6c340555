import { useTheme } from '@mui/material';
import { usePathname } from 'next/navigation';

export const useSidebarStyles = (isOpen: boolean) => {
  const theme = useTheme();

  return {
    textPrimarySelectedColor: theme.palette.customColors.primary,
    borderLeftColor: theme.palette.customColors.primary,
    backgroundChildrenColor: theme.palette.customColors.neutral50,
    backgroundActiveColor: theme.palette.customColors.primary50,
    plText: isOpen ? '16px' : '0',
  };
};

export const useIsSelected = () => {
  const pathname = usePathname();

  return (path: string, exact: boolean) => {
    const pathRemoveSearch = path.split('?')[0];

    if (!pathRemoveSearch) return false;

    if (exact) {
      return pathRemoveSearch === pathname;
    }

    return pathname?.includes(pathRemoveSearch) ?? false;
  };
};
