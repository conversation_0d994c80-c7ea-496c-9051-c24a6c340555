import { useMutation } from '@tanstack/react-query';
import { updateShipmentEventService } from 'services/shipment.service';
import { CreateShipment } from 'types';

import { UseMutationOptions } from '@tanstack/react-query';

export type UpdateShipment = {
  formValues: CreateShipment;
  shipmentId: string;
};

export function useUpdateShipmentMutate(options?: UseMutationOptions<void, Error, UpdateShipment, unknown>) {
  return useMutation({
    mutationFn: (variables: UpdateShipment) => updateShipmentEventService(variables.formValues, variables.shipmentId),
    mutationKey: ['useUpdateShipmentMutate'],
    ...options,
  });
}
