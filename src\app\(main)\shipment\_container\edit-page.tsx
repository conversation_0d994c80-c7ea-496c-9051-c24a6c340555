'use client';

import { Box, Button, IconButton, styled, Typography, useTheme } from '@mui/material';
import { useTranslations } from 'next-intl';

import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import DeleteIcon from '@mui/icons-material/Delete';
import { Breadcrumbs, Stepper } from 'components';
import { PromptDialog } from 'components/prompt-dialog';
import { FIXED_LOCATION } from 'constant/common';
import dayjs from 'dayjs';
import { UpdateShipment, useUpdateShipmentMutate } from 'hooks/mutates/useUpdateShipmentMutate';
import { useGetReceivingByIdsQuery } from 'hooks/queries/useGetReceivingByIdsQuery';
import { useDeviceHeight } from 'hooks/useDeviceHeight';
import { useGetEventStatus } from 'hooks/useGetEventStatus';
import { headerHeight } from 'layouts/main/constant';
import { LoadingLayout } from 'layouts/main/loading-layout';
import cloneDeep from 'lodash-es/cloneDeep';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { clientRoutes } from 'routes/client-routes';
import {
  FormShipmentStepEnum,
  ShipmentIdentityType,
  useCreateShipmentStore,
  VarietyOptionValue,
} from 'store/useCreateShipmentStore';
import { EventStatusEnum, PackingHouseDetail, Receipt } from 'types';
import { removeImgSuffix, safeJSONParse } from 'utils';
import { capturePosthog } from 'utils/posthog';
import toastMessages from 'utils/toastMessages';
import { v4 } from 'uuid';
import { DetailsDrawer, FormContent, SelectPackagingInformationDrawer } from '../_components';
import { QrCodeReviewContent } from '../_container/qr-code-review-content';
import { ShipmentIdentityContent } from '../_container/shipment-identity-content';
import { UploadDocumentsContent } from '../_container/upload-documents-content';
import { defaultReceiptFormValues, UploadReceiptContent } from '../_container/upload-receipt-content';
import { SelectPackagingInformationContent } from './select-packaging-information-content';
import { defaultFormReceiptValues } from '../_constant/form';

const Root = styled(Box)`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
  padding: 0;
`;

const ActionButtonGroup = styled(Box)`
  width: 100%;
  min-height: fit-content;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  align-items: flex-end;
  box-shadow: 1;
  position: relative;
`;

const StepContent = styled(Box)`
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: ${({ theme }) => theme.palette.customColors.white};
`;

type IndexProps = {
  shipmentDetail: PackingHouseDetail;
};

const Index = (props: IndexProps) => {
  const { shipmentDetail } = props;
  const param = useParams<{ id: string }>();

  const id = param?.id ?? '';

  const shipmentNameTitle = shipmentDetail?.name ?? '';

  const shipmentT = useTranslations('shipment');
  const commonT = useTranslations('common');

  const router = useRouter();
  const { showWarningDialog, setShowWarningDialog } = useCreateShipmentStore();

  const {
    formStep,
    informationFormValues,
    ocrFile,
    additionalFiles,
    updateStep,
    updateOrcForm,
    generateQrCodeBatchLot,
    updateShipmentIdentity,
    resetStore,
    setIsUploadOcrError,
    initEditForm,
    receivingIds,
    setManualInput,
    manualInputOrc,
    setReceivingDetails,
    shipmentPhotos,
  } = useCreateShipmentStore();

  const [loadingForm, setLoadingForm] = useState<boolean>(false);

  const shipmentForm = useForm<ShipmentIdentityType>({
    defaultValues: {
      name: shipmentDetail.name ?? '',
    },
    mode: 'onBlur',
  });

  const { data: receivingData } = useGetReceivingByIdsQuery([...receivingIds]);

  useEffect(() => {
    if (formStep === FormShipmentStepEnum.SelectedProductIds) {
      updateStep(FormShipmentStepEnum.ShipmentIdentity);
    }
  }, [formStep, updateStep]);

  useEffect(() => {
    if (receivingData?.length) {
      setReceivingDetails(receivingData);
    }
  }, [receivingData, setReceivingDetails]);

  const { getStatusLabel } = useGetEventStatus();

  const unauthorizedT = useTranslations('unauthorize');

  const { eventStatus } = getStatusLabel(shipmentDetail.status, shipmentDetail.type);

  const isDisabledEdit = useMemo(() => {
    const isWaiting = eventStatus === 'waiting';

    const isDraft = eventStatus === 'draft';

    if (isWaiting || isDraft) {
      return false;
    }

    return true;
  }, [eventStatus]);

  if (isDisabledEdit) {
    toastMessages.error(unauthorizedT('unauthorized-description'));
    router.push(clientRoutes.shipment);
  }

  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      e.preventDefault();
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const receiptFormOptions = useForm<Receipt>({
    defaultValues: {
      ...defaultReceiptFormValues,
    },
    mode: 'onSubmit',
  });

  const deviceHeight = useDeviceHeight();

  const minHeight = deviceHeight - headerHeight - 420;

  const { watch: watchReceiptForm } = receiptFormOptions;

  const receiptNumber = watchReceiptForm('receiptNumber');

  useEffect(() => {
    if (shipmentDetail) {
      resetStore();
      initEditForm(cloneDeep(shipmentDetail));
    }
  }, [initEditForm, resetStore, shipmentDetail]);

  const { mutateAsync, isPending } = useUpdateShipmentMutate({
    onSuccess: async () => {
      capturePosthog('shipment_created_success');
      toastMessages.success(shipmentT('update-success'));
      router.push(clientRoutes.shipment);
    },
    onError: () => {
      setLoadingForm(false);
      toastMessages.error(shipmentT('update-fail'));
    },
  });

  const [openDetailModal, setOpenDetailModal] = useState<boolean>(false);
  const theme = useTheme();

  const steps = [
    shipmentT('shipment-identity'),
    shipmentT('information-step'),
    shipmentT('receipt-step'),
    shipmentT('add-document-step'),
    shipmentT('qr-review'),
  ];

  const renderTitle = () => {
    switch (formStep) {
      case FormShipmentStepEnum.ShipmentIdentity:
        return shipmentT('shipment-identity');
      case FormShipmentStepEnum.DurianInformationStep:
        return shipmentT('information-step');
      case FormShipmentStepEnum.ReceiptStep:
        return shipmentT('receipt-step');
      case FormShipmentStepEnum.UploadDocumentStep:
        return shipmentT('add-document-step');
      case FormShipmentStepEnum.QrReviewStep:
        return shipmentT('qr-review');
      default:
        break;
    }
  };

  const checkDisableNextButton = () => {
    let isValid = true;

    switch (formStep) {
      case FormShipmentStepEnum.DurianInformationStep:
        isValid = informationFormValues.length > 0;
        break;
      case FormShipmentStepEnum.ReceiptStep:
        isValid = !!receiptNumber;
        break;
      case FormShipmentStepEnum.UploadDocumentStep:
      default:
        isValid = true;
        break;
    }

    return isValid;
  };

  const onSubmitShipment = async (asDraft?: boolean) => {
    const isValid = await shipmentForm.trigger();

    if (!isValid) {
      updateStep(FormShipmentStepEnum.ShipmentIdentity);
      return;
    }

    const fileIds = additionalFiles.map((it) => it.url)?.map((it) => removeImgSuffix(it));

    setLoadingForm(true);

    const photoIds = shipmentPhotos?.map((it) => removeImgSuffix(it.url));

    const ocrFormValue = receiptFormOptions.getValues();

    const updateForm: UpdateShipment = {
      formValues: {
        status: asDraft ? 'draft' : 'published',
        shipmentName: shipmentForm.getValues().name ?? '',
        receivingProductIds: receivingIds,
        receipt: ocrFormValue?.receiptNumber
          ? {
              numberOfBoxes: Number(ocrFormValue.numberOfBoxes),
              destinationCountry: ocrFormValue.destinationCountry,
              exportDate: dayjs(ocrFormValue.exportDate).unix(),
              totalWeightKg: Number(ocrFormValue.totalWeightKg),
              receiptNumber: ocrFormValue.receiptNumber,
              transportationMode: ocrFormValue.transportationMode ?? '',
              containerNumber: ocrFormValue.containerNumber ?? '',
              truckRegistrationNumber: ocrFormValue.truckNumber ?? '',
              trailerRegistrationNumber: ocrFormValue.trailerNumber ?? '',
              orchardRegisterNumber: ocrFormValue.orchardNo ?? '',
              borderCheckpointName: ocrFormValue.borderCheckpointName ?? '',
              nameOfExportingCompany: ocrFormValue.nameOfExportingCompany ?? '',
              truckProvinceRegistrationNumber: ocrFormValue.truckProvinceRegistrationNumber ?? '',
              trailerProvinceRegistrationNumber: ocrFormValue.trailerProvinceRegistrationNumber ?? '',
            }
          : undefined,
        latitude: FIXED_LOCATION.SHIPMENT.latitude,
        longitude: FIXED_LOCATION.SHIPMENT.longitude,
        packages: informationFormValues?.map((packageInfo) => {
          const varietyParse = safeJSONParse<VarietyOptionValue>(packageInfo.variety);
          const packingDate = packageInfo.packingDate ? dayjs(packageInfo.packingDate).unix() : undefined;

          return {
            brandName: packageInfo.brand,
            productType: packageInfo.boxType,
            varietyGradeJoinId: packageInfo.grade,
            weightKg: Number(packageInfo.netWeight),
            numberOfBoxes: Number(packageInfo.totalBoxes),
            qrCode: packageInfo.qrId ?? '',
            batchlot: packageInfo.batchlot ?? '',
            varietyId: varietyParse?.originalId ?? '',
            varietyName: varietyParse?.name,
            gradeId: packageInfo.grade,
            packingDate,
          };
        }),
        documentIds: fileIds.length ? [...fileIds] : undefined,
        shipmentPhotoIds: photoIds?.length ? [...photoIds] : undefined,
      },
      shipmentId: id,
    };
    await mutateAsync(updateForm);

    if (!asDraft) {
      resetStore();
      router.push(clientRoutes.shipment);
    }
  };

  const handleSubmitReceiptForm = (values: Receipt) => {
    const ocrId = ocrFile?.id ?? v4();

    const exportDateForm = dayjs(values.exportDate);

    updateOrcForm({
      ...values,
      id: ocrId,
      sourceFrom: values.sourceFrom ?? 'ephyto',
      exportDate: exportDateForm.valueOf(),
    });

    setIsUploadOcrError(false);
  };

  const onNext = async () => {
    if (formStep === FormShipmentStepEnum.QrReviewStep) {
      await onSubmitShipment();
      return;
    } else if (formStep === FormShipmentStepEnum.UploadDocumentStep) {
      setLoadingForm(true);
      await generateQrCodeBatchLot();
      setLoadingForm(false);
    } else if (formStep === FormShipmentStepEnum.ReceiptStep) {
      const { trigger, getValues } = receiptFormOptions;
      const isValid = await trigger();

      if (!isValid) return;

      if (isValid) {
        handleSubmitReceiptForm(getValues());
      }
    } else if (formStep === FormShipmentStepEnum.ShipmentIdentity) {
      const isValid = await shipmentForm.trigger();
      if (!isValid) return;
      updateShipmentIdentity(shipmentForm.getValues().name ?? '');
    }

    updateStep(formStep + 1);
  };

  const onGoBack = () => {
    updateStep(formStep - 1);
  };

  const onConfirmDialog = () => {
    router.push(clientRoutes.shipment);
  };

  const breadcrumbs = [
    {
      label: commonT('shipment'),
      href: clientRoutes.shipment,
    },
    {
      label: shipmentNameTitle,
      href: clientRoutes.createShipment,
    },
  ];

  const isReceipt = formStep === FormShipmentStepEnum.ReceiptStep;

  const continueBtnLabel = useMemo(() => {
    switch (formStep) {
      case FormShipmentStepEnum.UploadDocumentStep:
        return shipmentT('generate-qr-code');
      case FormShipmentStepEnum.QrReviewStep:
        return commonT('submit');
      case FormShipmentStepEnum.ReceiptStep:
      case FormShipmentStepEnum.DurianInformationStep:
      default:
        return commonT('continue');
    }
  }, [commonT, formStep, shipmentT]);

  return (
    <>
      <LoadingLayout loading={loadingForm}>
        <Box component="div" id="edit-shipment-wrapper" sx={{ flex: 1, p: '20px' }}>
          <Breadcrumbs items={breadcrumbs} />
          <Root>
            <Box sx={{ display: 'flex', gap: '8px', height: '60px', alignItems: 'center' }}>
              <IconButton onClick={() => setShowWarningDialog(true)}>
                <ArrowBackIcon />
              </IconButton>
              <Typography variant="h6" my="12px" fontWeight="bold">
                {`${shipmentT('edit-record')} ${shipmentNameTitle}`}
              </Typography>
              <Typography variant="h6" my="12px" fontWeight="bold" color="text.secondary">
                -
              </Typography>
              <Typography variant="h6" my="12px" fontWeight="bold" color="text.secondary">
                {shipmentDetail?.batchlot}
              </Typography>
            </Box>

            <Box
              sx={{
                display: 'flex',
                gap: '12px',
                alignItems: 'center',
                backgroundColor: theme.palette.customColors.primary100,
                p: '12px 16px',
                boxShadow: 1,
                borderRadius: 2,
              }}
            >
              <Typography>
                {shipmentT.markup('batch-lot-selected', {
                  x: receivingIds?.length ?? 0,
                })}
              </Typography>
              <Typography
                component="a"
                onClick={() => setOpenDetailModal(true)}
                variant="body1"
                color="primary"
                sx={{
                  cursor: 'pointer',
                  '&:hover': {
                    textDecoration: 'underline',
                  },
                }}
              >
                {commonT('view-detail')}
              </Typography>
            </Box>

            <Stepper activeStep={formStep - 1} steps={steps} />
            <StepContent sx={{ boxShadow: 1, p: '20px 16px', position: 'relative', borderRadius: 2 }}>
              <Box component="div">
                <Typography variant="body1" fontWeight="bold">
                  {renderTitle()}
                </Typography>

                {isReceipt && (ocrFile?.receiptNumber || manualInputOrc) && (
                  <IconButton
                    color="error"
                    size="medium"
                    onClick={() => {
                      updateOrcForm(undefined);
                      receiptFormOptions.reset({ ...defaultFormReceiptValues });
                      setManualInput(false);
                    }}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      width: '50px',
                      position: 'absolute',
                      right: '12px',
                      top: '16px',
                      cursor: 'pointer',
                      justifyContent: 'flex-end',
                    }}
                  >
                    <DeleteIcon fontSize="inherit" />
                  </IconButton>
                )}

                {/* Content form */}
                <Box
                  component="div"
                  sx={{
                    width: '100%',
                    flex: 1,
                    minHeight: `${minHeight}px`,
                    position: 'relative',
                    display: 'flex',
                    flexDirection: 'column',
                  }}
                >
                  <FormContent active={formStep === FormShipmentStepEnum.ShipmentIdentity} minHeight={minHeight}>
                    <ShipmentIdentityContent formOptions={shipmentForm} />
                  </FormContent>
                  <FormContent active={formStep === FormShipmentStepEnum.DurianInformationStep} minHeight={minHeight}>
                    <SelectPackagingInformationContent />
                  </FormContent>
                  <FormContent active={formStep === FormShipmentStepEnum.ReceiptStep} minHeight={minHeight}>
                    <UploadReceiptContent receiptFormOptions={receiptFormOptions} />
                  </FormContent>
                  <FormContent active={formStep === FormShipmentStepEnum.UploadDocumentStep} minHeight={minHeight}>
                    <UploadDocumentsContent minHeight={minHeight} />
                  </FormContent>
                  <FormContent active={formStep === FormShipmentStepEnum.QrReviewStep} minHeight={minHeight}>
                    <QrCodeReviewContent />
                  </FormContent>
                </Box>
              </Box>
            </StepContent>

            <ActionButtonGroup>
              {shipmentDetail?.status === EventStatusEnum.DRAFT && (
                <Button
                  variant="text"
                  onClick={() => {
                    setShowWarningDialog(true);
                  }}
                  sx={{
                    color: theme.palette.customColors.neutral700,
                    position: 'absolute',
                    top: 0,
                    left: '-10px',
                  }}
                >
                  {shipmentT('cancel')}
                </Button>
              )}

              {shipmentDetail?.status === EventStatusEnum.DRAFT && (
                <Button
                  sx={{ width: '200px', color: theme.palette.customColors.neutral700 }}
                  onClick={() => {
                    onSubmitShipment(true);
                  }}
                >
                  {commonT('save-as-draft')}
                </Button>
              )}

              <Button
                variant="outlined"
                color="primary"
                sx={{ width: '200px' }}
                onClick={onGoBack}
                disabled={formStep === FormShipmentStepEnum.ShipmentIdentity || isPending}
              >
                {shipmentT('discard')}
              </Button>
              <Button
                variant="contained"
                sx={{ width: '200px' }}
                disabled={!checkDisableNextButton()}
                loading={isPending || loadingForm}
                onClick={onNext}
              >
                {continueBtnLabel}
              </Button>
            </ActionButtonGroup>
          </Root>
          <SelectPackagingInformationDrawer />
          {receivingIds.length !== 0 && <DetailsDrawer open={openDetailModal} toggle={setOpenDetailModal} />}
        </Box>

        <PromptDialog
          open={showWarningDialog}
          onClose={() => setShowWarningDialog(false)}
          onConfirm={onConfirmDialog}
          confirmText={commonT('confirm')}
          title={shipmentT('warning-cancel-draft-shipment-title')}
          content={shipmentT('warning-cancel-draft-shipment')}
        />
      </LoadingLayout>
    </>
  );
};

export default Index;
