import { <PERSON>rian<PERSON>rade, DurianVariety } from 'types';

const INTEGER_MAX_LENGTH = 7;

// Helper functions to reduce cognitive complexity
export const cleanDecimalInput = (rawValue: string): string => {
  const firstDotIndex = rawValue.indexOf('.');
  if (firstDotIndex === -1) return rawValue;

  const beforeFirstDot = rawValue.substring(0, firstDotIndex + 1);
  const afterFirstDot = rawValue.substring(firstDotIndex + 1).replace(/\./g, '');
  return beforeFirstDot + afterFirstDot;
};

export const normalizeValue = (cleanValue: string): string => {
  if (cleanValue === '.') return '1.';
  if (cleanValue.startsWith('.')) return '0' + cleanValue;
  return cleanValue;
};

export const validateIntegerLength = (value: string, maxLength: number): boolean => {
  return value.length <= maxLength;
};

const validateMinimumValue = (value: string): boolean => {
  const num = parseFloat(value);
  return num >= 1;
};

export const hasLeadingZero = (value: string): boolean => {
  // Check if value starts with 0 followed by a digit (but not 0.x)
  return value.length > 1 && value.startsWith('0') && value[1] !== '.';
};

export const processDecimalValue = (processedValue: string): string | null => {
  const parts = processedValue.split('.');
  const integerPart = parts[0];
  const decimalPart = parts[1];

  // Validate integer part length
  if (!validateIntegerLength(integerPart, INTEGER_MAX_LENGTH)) {
    return null;
  }

  // Handle empty decimal part (ends with just a dot)
  if (decimalPart === undefined || decimalPart === '') {
    if (!validateMinimumValue(integerPart)) {
      return null;
    }
    return processedValue; // Allow "5." format
  }

  // Truncate decimal to 1 digit
  const truncatedDecimal = decimalPart.charAt(0);
  const finalValue = `${integerPart}.${truncatedDecimal}`;

  if (!validateMinimumValue(finalValue)) {
    return null;
  }

  return finalValue;
};

export const processIntegerValue = (processedValue: string): string | null => {
  if (!validateIntegerLength(processedValue, INTEGER_MAX_LENGTH)) {
    return null;
  }

  if (!validateMinimumValue(processedValue)) {
    return null;
  }

  return processedValue;
};

export function findVarietyAndGrade(
  key: string,
  varieties: DurianVariety[]
): { varietyId?: string; grade?: DurianGrade } {
  for (const variety of varieties) {
    if (!key.startsWith(variety.id)) continue;

    const grade = variety.grades.find((g: DurianGrade) => key.includes(g.id));
    if (grade) {
      return { varietyId: variety.id, grade };
    }
  }
  return {};
}

// Helper function to ensure variety exists in map
export function ensureVarietyInMap(
  varietyId: string,
  varietiesMap: Record<
    string,
    {
      id: string;
      grades: Array<{
        id: string;
        weight: number;
        name?: string;
      }>;
      flowerBloomingDay: number;
    }
  >,
  varietyBloomDays: Record<string, number>
) {
  varietiesMap[varietyId] ??= {
    id: varietyId,
    grades: [],
    flowerBloomingDay: varietyBloomDays[varietyId],
  };
}

// Helper function to check if value is valid
export function isValidValue(value?: string): boolean {
  return Boolean(value && value.trim() !== '');
}
