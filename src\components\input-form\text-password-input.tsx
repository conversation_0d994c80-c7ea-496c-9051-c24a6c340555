'use client';

import { FC, useState } from 'react';
import { FormControl, IconButton, InputAdornment, TextField, TextFieldProps, CircularProgress } from '@mui/material';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';

interface TextPasswordInputProps extends Omit<TextFieldProps, 'type'> {
  maxLength?: number;
  loading?: boolean;
}

export const TextPasswordInput: FC<TextPasswordInputProps> = ({
  maxLength,
  loading = false,
  ...props
}) => {
  const [showPassword, setShowPassword] = useState(false);

  const handleTogglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  return (
    <FormControl fullWidth sx={{ position: 'relative' }}>
      <TextField
        {...props}
        type={showPassword ? 'text' : 'password'}
        disabled={props.disabled || loading}
        slotProps={{
          htmlInput: {
            maxLength,
          },
          input: {
            endAdornment: (
              <>
                {loading ? (
                  <InputAdornment position="end">
                    <CircularProgress size={20} />
                  </InputAdornment>
                ) : (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleTogglePasswordVisibility}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  </InputAdornment>
                )}
              </>
            ),
          },
        }}
      />
    </FormControl>
  );
};
