// app/error.tsx
'use client';

import { useEffect } from 'react';
import { Box, Typography, Button, Paper } from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import HomeIcon from '@mui/icons-material/Home';
import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import { basePathProd } from 'configs/app-config';
import { captureExceptionError } from 'utils/posthog';
import { logger } from 'utils/logger';

const COLORS = {
  primaryMain: '#4285F4',
  lightGray: '#f5f5f5',
  blueHighlight: '#2266D9',
  blue500: '#2196f3',
  blueTint: '#F5F9FF',
  blue50: '#E3F2FD',
  white: '#ffffff',
  textSecondary: '#64748b',
  errorBg: 'rgba(255, 255, 255, 0.85)',
};

interface ErrorProps {
  error: Error;
}

export function ErrorFallback({ error }: ErrorProps) {
  const commonT = useTranslations('common');

  useEffect(() => {
    if (error instanceof Error) {
      logger.error('error', error);

      captureExceptionError(error, {
        message: error.message,
        stack: error.stack,
        name: error.name,
      });
    }
  }, [error]);

  return (
    <Box
      sx={{
        height: 'calc(100vh - 64px)',
        p: 2,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: `
          radial-gradient(circle at 15% 85%, rgba(66, 133, 244, 0.08) 0%, transparent 45%),
          radial-gradient(circle at 85% 15%, rgba(33, 150, 243, 0.08) 0%, transparent 45%),
          linear-gradient(135deg, ${COLORS.blueTint} 0%, ${COLORS.lightGray} 100%)
        `,
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `
            radial-gradient(circle at 30% 30%, rgba(66, 133, 244, 0.02) 0%, transparent 30%),
            radial-gradient(circle at 70% 70%, rgba(33, 150, 243, 0.02) 0%, transparent 30%)
          `,
          animation: 'floatSlow 12s ease-in-out infinite',
        },
        '@keyframes floatSlow': {
          '0%, 100%': { transform: 'translateY(0px) scale(1)' },
          '50%': { transform: 'translateY(-15px) scale(1.02)' },
        },
      }}
    >
      <motion.div
        initial={{ opacity: 0, y: 50, scale: 0.9 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.8, ease: 'easeOut' }}
      >
        <Paper
          elevation={0}
          sx={{
            maxWidth: 1000,
            minWidth: 400,
            textAlign: 'center',
            p: { xs: 4, sm: 6 },
            borderRadius: 4,
            background: COLORS.errorBg,
            backdropFilter: 'blur(25px) saturate(180%)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: `
              0 25px 50px rgba(66, 133, 244, 0.1),
              0 8px 25px rgba(0, 0, 0, 0.04),
              inset 0 1px 0 rgba(255, 255, 255, 0.6)
            `,
          }}
        >
          {/* Error Icon */}
          <motion.div
            initial={{ scale: 0.5, opacity: 0, rotate: -20 }}
            animate={{ scale: 1, opacity: 1, rotate: 0 }}
            transition={{ delay: 0.2, duration: 0.8, type: 'spring', stiffness: 120 }}
          >
            <Box
              sx={{
                width: 110,
                height: 110,
                mx: 'auto',
                mb: 4,
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: `linear-gradient(135deg, ${COLORS.primaryMain}, ${COLORS.blueHighlight})`,
                boxShadow: `
                  0 15px 35px rgba(66, 133, 244, 0.25),
                  0 5px 15px rgba(0, 0, 0, 0.08)
                `,
                position: 'relative',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  inset: -3,
                  borderRadius: '50%',
                  background: `conic-gradient(from 0deg, ${COLORS.primaryMain}20, ${COLORS.blueHighlight}20, ${COLORS.primaryMain}20)`,
                  zIndex: -1,
                  animation: 'rotate 8s linear infinite',
                },
                '@keyframes rotate': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' },
                },
              }}
            >
              <ErrorOutlineIcon sx={{ fontSize: 60, color: COLORS.white }} />
            </Box>
          </motion.div>

          {/* Main Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            <Typography
              variant="h3"
              component="h1"
              sx={{
                fontWeight: 800,
                fontSize: { xs: '1.8rem', sm: '2.2rem' },
                background: `linear-gradient(135deg, ${COLORS.primaryMain}, ${COLORS.blueHighlight})`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 2,
                letterSpacing: '-0.03em',
                lineHeight: 1.2,
              }}
            >
              {commonT('common-error')}
            </Typography>

            <Typography
              variant="body1"
              sx={{
                color: COLORS.textSecondary,
                fontSize: '1.1rem',
                lineHeight: 1.6,
                mb: 4,
                maxWidth: 1000,
                mx: 'auto',
                fontWeight: 400,
              }}
            >
              {commonT('unexpected-error-description')}
            </Typography>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.5 }}
          >
            <Box
              sx={{
                display: 'flex',
                gap: 2,
                justifyContent: 'center',
                flexDirection: { xs: 'column', sm: 'row' },
                alignItems: 'center',
              }}
            >
              <Button
                variant="contained"
                size="large"
                href={basePathProd}
                startIcon={<HomeIcon />}
                sx={{
                  background: `linear-gradient(135deg, ${COLORS.primaryMain} 0%, ${COLORS.blueHighlight} 100%)`,
                  color: COLORS.white,
                  fontWeight: 700,
                  fontSize: '1rem',
                  px: 4,
                  py: 1.5,
                  borderRadius: 3,
                  textTransform: 'none',
                  minWidth: 180,
                  boxShadow: '0 6px 20px rgba(66, 133, 244, 0.25)',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    transform: 'translateY(-3px)',
                    boxShadow: '0 10px 30px rgba(66, 133, 244, 0.35)',
                    background: `linear-gradient(135deg, ${COLORS.blueHighlight} 0%, ${COLORS.blue500} 100%)`,
                  },
                }}
              >
                {commonT('go-homepage')}
              </Button>
            </Box>
          </motion.div>
        </Paper>
      </motion.div>
    </Box>
  );
}
