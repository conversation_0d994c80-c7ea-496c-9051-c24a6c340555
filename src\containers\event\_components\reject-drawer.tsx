import { Box, Fade, Typography } from '@mui/material';
import { Drawer } from 'components';
import { FormRadioGroup, FormTextInputArea } from 'components/hook-form';
import { useRejectHarvestMutate } from 'hooks/mutates/useRejectHarvestMutate';
import { useTranslations } from 'next-intl';
import { FC, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { theme } from 'styles/theme';
import { PackingHouseDetail } from 'types';
import { isOtherRejectReason, rejectReasonOptions } from './constants';
import { capturePosthog } from 'utils/posthog';
import { sendEvent } from 'utils/gtag';

interface RejectDrawerProps {
  data: PackingHouseDetail;
  open: boolean;
  toggle: (open: boolean) => void;
}

interface RejectFormData {
  rejectReason?: string;
  customReason?: string;
}

const MAX_LENGTH = 500;

export const RejectDrawer: FC<RejectDrawerProps> = ({ data, open, toggle }) => {
  const receivingTranslation = useTranslations('receive');
  const commonTranslation = useTranslations('common');
  const { mutateAsync, isPending } = useRejectHarvestMutate();
  const boxRef = useRef<HTMLDivElement>(null);

  const form = useForm<RejectFormData>({
    defaultValues: {
      rejectReason: '',
      customReason: '',
    },
  });

  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    setError,
    formState: { errors },
  } = form;
  const watchedRejectReason = watch('rejectReason');
  const watchedCustomReason = watch('customReason');

  const isOtherSelected = isOtherRejectReason(watchedRejectReason);

  const onClose = () => {
    reset();
    toggle(false);
  };

  const finalReason = isOtherSelected ? watchedCustomReason : watchedRejectReason;

  const onSubmit = async () => {
    if (!data) {
      onClose();
      return;
    }

    if (!finalReason) {
      setError('rejectReason', {
        type: 'manual',
        message: receivingTranslation('reject-reason-error'),
      });
      return;
    }

    sendEvent('reject_incomning_harvest');

    await mutateAsync({
      productId: data.id,
      reason: finalReason,
    });

    capturePosthog('reject_incoming_batch_lot');
  };

  // Create options for radio group
  const radioOptions = rejectReasonOptions.map((option) => ({
    value: option,
    label: option,
  }));

  const drawerTitle = `${receivingTranslation('reject-drawer-title')} ${data?.batchlot}`;

  useEffect(() => {
    if (!isOtherSelected) {
      setValue('customReason', '');
    } else {
      boxRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [isOtherSelected, setValue]);

  return (
    <Drawer
      anchor="right"
      drawerTitle={drawerTitle}
      open={open}
      onClose={onClose}
      hasActionBtn={true}
      onConfirm={handleSubmit(onSubmit)}
      confirmButtonText={commonTranslation('submit')}
      isDisableConfirm={!finalReason}
      isLoading={isPending}
    >
      <Box
        component="form"
        onSubmit={handleSubmit(onSubmit)}
        sx={{
          width: '100%',
          p: '12px 12px 0',
          display: 'flex',
          flexDirection: 'column',
          gap: '8px',
          boxSizing: 'border-box',
          height: `calc(100vh - 70px - 72px)`, // 70px for header, 72px for footer
          overflowY: 'auto',
          overflowX: 'hidden',
          position: 'relative',
        }}
      >
        <Typography variant="body1" color="text.secondary" fontWeight={400}>
          {receivingTranslation('reject-drawer-content')}
        </Typography>

        <FormRadioGroup
          name="rejectReason"
          control={control}
          errors={errors}
          required
          requiredMessage={receivingTranslation('reject-reason-error')}
          options={radioOptions}
        />

        <Fade in={isOtherSelected} timeout={300} unmountOnExit>
          <Box
            ref={boxRef}
            sx={{
              width: '100%',
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <Box sx={{ width: '50px' }} />
            <FormTextInputArea
              name="customReason"
              minRows={2}
              control={control}
              errors={errors}
              required
              placeholder={receivingTranslation('reject-drawer-reason-placeholder')}
              maxLength={MAX_LENGTH}
            />
          </Box>
        </Fade>

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            visibility: isOtherSelected ? 'visible' : 'hidden',
          }}
        >
          <Fade in={isOtherSelected} timeout={300}>
            <Typography
              variant="body1"
              sx={{ color: theme.palette.customColors.gray, textAlign: 'right', fontSize: '12px' }}
            >
              {watchedCustomReason?.length ?? 0}/{MAX_LENGTH}
            </Typography>
          </Fade>
        </Box>
      </Box>
    </Drawer>
  );
};
