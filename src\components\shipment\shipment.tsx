'use client';
import AddIcon from '@mui/icons-material/Add';
import { Box, Button, IconButton, Typography } from '@mui/material';
import { GridRowParams, type GridColDef, type GridRenderCellParams } from '@mui/x-data-grid';
import {
  renderBatchname,
  renderRecordOn,
  renderStatus,
  renderUpdatedBy,
  renderUpdatedOn,
} from 'containers/event/_components';
import { useDeviceHeight } from 'hooks/useDeviceHeight';
import { useGetEventStatus } from 'hooks/useGetEventStatus';
import { headerHeight } from 'layouts/main/constant';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { clientRoutes } from 'routes/client-routes';
import { useGlobalStore } from 'store/useGlobalStore';
import { useToastStore } from 'store/useToastStore';
import { theme } from 'styles/theme';
import { EventStatusEnum, PackingHouse } from 'types';
import { formatNumberWithCommas, getStatus } from 'utils';
import toastMessages from 'utils/toastMessages';
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined';
import { useCreateShipmentStore } from 'store/useCreateShipmentStore';
import { EventDataTable } from 'components/event-table';
import { generateDefaultSearchParams } from 'hooks/useMenuItems';
import { capturePosthog } from 'utils/posthog';
import { sendEvent } from 'utils/gtag';
import { BorderColor, DeleteOutlineOutlined } from '@mui/icons-material';
import { useDeleteDraftMutate } from 'hooks/mutates/useDeleteDraftMutate';
import { Dialog } from 'components/dialog';
import { useFeatureFlag } from 'hooks/useFeatureFlag';

interface Shipment {
  id: string;
  shipmentId: string;
  destinationCountry: string;
  totalBoxes: number;
  status: 'waiting' | 'sorted';
  createdDate: string;
}

const DEFAULT_VALUE = '--';

export const Shipment = () => {
  const { toast: toastNotification, resetToast } = useToastStore();
  const shipmentT = useTranslations('shipment');
  const receiveT = useTranslations('receive');
  const commonT = useTranslations('common');
  const filterT = useTranslations('filter');
  const router = useRouter();

  const { ENABLE_DELETE_SHIPMENT } = useFeatureFlag();

  const deviceHeight = useDeviceHeight();
  const [isOpen, setIsOpen] = useState<string | null>(null);
  const { mutateAsync } = useDeleteDraftMutate('shipment');

  const { setLoadingPage } = useGlobalStore();
  const { getStatusLabel } = useGetEventStatus();

  const { resetStore } = useCreateShipmentStore();

  useEffect(() => {
    if (toastNotification) {
      toastMessages[toastNotification.type](toastNotification.message);
      resetToast();
    }
  }, [resetToast, toastNotification]);

  const handleCreateShipment = () => {
    resetStore();

    setLoadingPage(true);
    capturePosthog('create_shipment_click');
    sendEvent('create_shipment');

    router.push(`${clientRoutes.createShipment}?${generateDefaultSearchParams()}`);
  };

  const handleOnRowClick = (params: GridRowParams) => {
    const productType = params.row.type;
    const productStatus = params.row.status;
    const status = getStatus(productType, productStatus);

    if (!params.row.productId) return;

    if (status === EventStatusEnum.DRAFT) {
      return router.push(`${clientRoutes.shipment}/${params.row.productId}/edit`);
    }
    return router.push(`${clientRoutes.shipment}/${params.row.productId}`);
  };

  const handleDelete = async (id: string) => {
    setIsOpen(null);
    await mutateAsync(id);
  };

  const renderAction = (params: GridRenderCellParams<PackingHouse>) => {
    const handleViewClick = (event: React.MouseEvent) => {
      event.stopPropagation();
      event.preventDefault();
      router.push(`${clientRoutes.shipment}/${params.row.productId}`);
    };

    const handleViewEditClick = (event: React.MouseEvent) => {
      event.stopPropagation();
      event.preventDefault();
      router.push(`${clientRoutes.shipment}/${params.row.productId}/edit`);
    };

    const handleDeleteDraftShipment = (event: React.MouseEvent) => {
      event.stopPropagation();
      event.preventDefault();
      setIsOpen(params.row.productId);
    };

    const productType = params.row.type;
    const productStatus = params.row.status;
    const status = getStatus(productType, productStatus);

    return (
      <Box>
        {status !== EventStatusEnum.DRAFT && (
          <IconButton size="small" onClick={handleViewClick}>
            <RemoveRedEyeOutlinedIcon fontSize="small" />
          </IconButton>
        )}

        {status === EventStatusEnum.DRAFT && (
          <>
            <IconButton size="small" onClick={handleViewEditClick}>
              <BorderColor fontSize="small" />
            </IconButton>
            {ENABLE_DELETE_SHIPMENT && (
              <IconButton size="small" color="error" onClick={handleDeleteDraftShipment}>
                <DeleteOutlineOutlined fontSize="small" />
              </IconButton>
            )}
          </>
        )}
      </Box>
    );
  };

  const customTableHeight = useMemo<number>(() => {
    if (deviceHeight) {
      const paddingHeight = 210;

      return deviceHeight - headerHeight - paddingHeight;
    }

    return 0;
  }, [deviceHeight]);

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: shipmentT('shipment-name'),
      width: 320,
      renderHeader: () => <Typography sx={{ fontWeight: 'bold' }}>{shipmentT('shipment-name')}</Typography>,
      sortable: true,
      renderCell: renderBatchname,
    },
    {
      field: 'batchlot',
      headerName: shipmentT('shipment_id'),
      width: 250,
      renderHeader: () => <Typography sx={{ fontWeight: 'bold' }}>{shipmentT('shipment_id')}</Typography>,
      sortable: true,
    },
    {
      field: 'destinationCountry',
      headerName: shipmentT('destination_country'),
      width: 180,
      renderHeader: () => <Typography sx={{ fontWeight: 'bold' }}>{shipmentT('destination_country')}</Typography>,
      sortable: false,
      valueFormatter: (value) => {
        return value || DEFAULT_VALUE;
      },
    },
    {
      field: 'totalBoxes',
      headerName: shipmentT('total_boxes'),
      width: 150,
      renderHeader: () => <Typography sx={{ fontWeight: 'bold' }}>{shipmentT('total_boxes')}</Typography>,
      valueFormatter: (value) => {
        return formatNumberWithCommas(value) || DEFAULT_VALUE;
      },
      sortable: true,
    },
    {
      field: 'status',
      headerName: shipmentT('status'),
      width: 150,
      renderHeader: () => <Typography sx={{ fontWeight: 'bold' }}>{shipmentT('status')}</Typography>,
      renderCell: (params: GridRenderCellParams<PackingHouse>) => {
        const { eventStatus, eventStatusLabel } = getStatusLabel(params.row.status, params.row.type);
        return renderStatus(eventStatus, eventStatusLabel);
      },
      sortable: true,
    },
    {
      field: 'userUpdated',
      headerName: shipmentT('modified-by'),
      width: 160,
      renderCell: renderUpdatedBy,
      sortable: true,
    },
    {
      field: 'dateUpdated',
      headerName: shipmentT('modified-on'),
      width: 168,
      renderCell: renderUpdatedOn,
      sortable: true,
    },
    {
      field: 'dateCreated',
      headerName: shipmentT('created_date'),
      width: 168,
      renderHeader: () => <Typography sx={{ fontWeight: 'bold' }}>{shipmentT('created_date')}</Typography>,
      renderCell: renderRecordOn,
      sortable: true,
    },
    {
      field: 'actions',
      headerName: shipmentT('actions'),
      flex: 1,
      renderHeader: () => <Typography sx={{ fontWeight: 'bold' }}>{shipmentT('actions')}</Typography>,
      renderCell: renderAction,
      sortable: false,
      minWidth: 100,
    },
  ];

  const filterStatusOptions = [
    {
      label: filterT('status-sealed'),
      value: 'sealed',
    },
    {
      label: filterT('status-shipment-waiting'),
      value: 'waiting',
    },
    {
      label: filterT('status-draft'),
      value: 'draft',
    },
  ];

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
      <Box
        component="main"
        sx={{ flexGrow: 1, p: { xs: 2, sm: 3 }, backgroundColor: theme.palette.customColors.lightGray }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3, flexWrap: 'wrap' }}>
          <Typography variant="h5" component="h1" fontWeight="bold" sx={{ mb: { xs: 2, sm: 0 } }}>
            {shipmentT('shipments')}
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateShipment}
            sx={{
              backgroundColor: theme.palette.customColors.primaryMain,
              '&:hover': {
                backgroundColor: theme.palette.customColors.primaryHover,
              },
              width: { xs: '100%', sm: 'auto' },
            }}
          >
            {shipmentT('create_shipment')}
          </Button>
        </Box>
        <EventDataTable
          filterStatusOptions={filterStatusOptions}
          tableHeight={customTableHeight}
          columns={columns}
          eventType={'shipment'}
          queryKey={'shipment-event-data'}
          onRowClick={handleOnRowClick}
          defaultSortModel={[
            {
              field: 'dateCreated',
              sort: 'desc',
            },
          ]}
        />
      </Box>
      <Dialog
        isOpen={isOpen !== null}
        title={shipmentT('delete-shipment')}
        content={receiveT('delete-harvest-content')}
        okButtonText={commonT('delete-modal-btn')}
        onConfirm={() => {
          if (isOpen !== null) {
            handleDelete(isOpen);
          }
        }}
        onCancel={() => setIsOpen(null)}
        type="danger"
      />
    </Box>
  );
};
