import { cookies } from 'next/headers';
import { getRequestConfig } from 'next-intl/server';
import { LOCALE_COOKIE_NAME_NEXT } from 'utils/cookie-client';

const Index = getRequestConfig(async () => {
  const cookieStore = await cookies();
  const locale = cookieStore.get(LOCALE_COOKIE_NAME_NEXT)?.value ?? 'th';

  return {
    locale,
    messages: (await import(`../../locales/${locale}.json`)).default,
  };
});

export default Index;
