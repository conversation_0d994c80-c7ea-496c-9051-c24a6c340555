import { getCookieServer } from 'configs/cookie';
import { NextPage } from 'next';
import dynamic from 'next/dynamic';
import { redirect } from 'next/navigation';
import { User } from 'types/user';

const PDPA = dynamic(() => import('containers/pdpa').then((mod) => mod.PDPA));

const Index: NextPage = async () => {
  const accessToken = await getCookieServer<User>('access_token_pkg_house');

  const userInfo = accessToken && ((await getCookieServer<User>('user_info')) as User);

  if (!userInfo) {
    return redirect('not-found');
  }

  return <PDPA />;
};

export default Index;
