import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
  useTheme,
} from '@mui/material';
import { useTranslations } from 'next-intl';

interface NotificationSettingsPromptProps {
  open: boolean;
  setOpen: (value: boolean) => void;
}

export function NotificationSettingsPrompt(props: NotificationSettingsPromptProps) {
  const t = useTranslations('notifications');
  const theme = useTheme();

  const handleClose = () => props.setOpen(false);

  return (
    <Dialog
      open={props.open}
      onClose={handleClose}
      PaperProps={{
        sx: {
          borderRadius: 2,
          p: 0,
          maxWidth: 1000,
          width: '90%',
          boxShadow: theme.shadows[24],
        },
      }}
      BackdropProps={{
        sx: {
          backdropFilter: 'blur(3px)',
        },
      }}
    >
      <DialogTitle
        sx={{
          bgcolor: theme.palette.primary.main,
          color: theme.palette.primary.contrastText,
          px: 3,
          py: 2,
          fontSize: '1.25rem',
          fontWeight: 600,
        }}
      >
        {t('title')}
      </DialogTitle>

      <DialogContent sx={{ px: 3, py: 2 }}>
        <Typography
          sx={{
            color: theme.palette.text.secondary,
            lineHeight: 1.5,
            mb: 1,
            mt: '12px',
          }}
        >
          {t('description')}
        </Typography>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button
          variant="outlined"
          onClick={handleClose}
          sx={{
            textTransform: 'none',
            borderRadius: 1,
            boxShadow: theme.shadows[2],
          }}
        >
          {t('close')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
