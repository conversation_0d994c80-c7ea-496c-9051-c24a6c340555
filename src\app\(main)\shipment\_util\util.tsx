import { FormShipmentStepEnum } from "store/useCreateShipmentStore";
import { getMessageByLocale } from "utils/getMessage";

 export const renderTitle = (formStep: FormShipmentStepEnum) => {

  const messageT = getMessageByLocale();

    switch (formStep) {
      case FormShipmentStepEnum.ShipmentIdentity:
        return messageT('shipment.shipment-identity');
      case FormShipmentStepEnum.DurianInformationStep:
        return messageT('shipment.information-step');
      case FormShipmentStepEnum.ReceiptStep:
        return messageT('shipment.receipt-step');
      case FormShipmentStepEnum.UploadDocumentStep:
        return messageT('shipment.add-document-step');
      case FormShipmentStepEnum.QrReviewStep:
        return messageT('shipment.qr-review');
      default:
        break;
    }
  };
