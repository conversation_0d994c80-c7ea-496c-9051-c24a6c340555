export interface TranslateLabel {
  th: string | null;
  en: string | null;
}

// Grade model
export interface DurianGrade {
  id: string;
  value: string;
  label: TranslateLabel;
  weight: number;
  name?: string;
}

// Main variety model
export interface DurianVariety {
  id: string;
  value: string;
  label: TranslateLabel;
  flowerBloomingDay: number;
  grades: DurianGrade[];
  name?: string;
  originalId?: string;
  isOther?: boolean;
  flowerBloomingDuration?: number;
}

export interface UpdateVarietyRequest {
  id: string;
  flowerBloomingDay?: number;
  grades: Array<{ id: string; weight: number; name?: string }>;
}
