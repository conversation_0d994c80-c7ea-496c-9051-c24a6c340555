'use client';

import { Box, Button, ButtonProps, Skeleton, Stack, Typography, useTheme } from '@mui/material';
import { AutoFitTextSize } from 'components/auto-text-size';
import { toPng, toJpeg } from 'html-to-image';
import { useTranslations } from 'next-intl';
import React, { FC, useMemo } from 'react';
import { QRCode } from 'react-qrcode-logo';
import { QrCodeReviewProvider, QrCodeReviewProviderProps, useQrCodeReviewContext } from './context';

const QR_RATIO = 8 / 5;
const FONT_SIZE_LABEL = 17;
const DEFAULT_TEXT = '--';
const PRINT_FONT = 'Myriad Web Pro, sans-serif';
const QR_BOX_HEIGHT = 320;
const QR_CODE_SIZE = 100;
const QR_CODE_LABEL_WIDTH = 'auto';
const LABEL_GAP = '6px';

// Memoization cache for generated circle images
const circleCache = new Map<string, string>();

function generateCircleWithNumber(number: string | number, size = 100, dpi = 2): string {
  // Create cache key
  const cacheKey = `${number}-${size}-${dpi}`;

  // Return cached result if available
  if (circleCache.has(cacheKey)) {
    return circleCache.get(cacheKey)!;
  }

  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d', {
    alpha: true,
    desynchronized: true,
  });

  if (!ctx) return '';

  // Scale canvas size by DPI
  const scaledSize = size * dpi;
  canvas.width = scaledSize;
  canvas.height = scaledSize;

  // Scale context for high DPI
  ctx.scale(dpi, dpi);

  // Enable optimizations
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';

  // Draw white circle with optimized path
  ctx.fillStyle = '#ffffff';
  ctx.beginPath();
  ctx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2);
  ctx.fill();

  // Draw text with optimized settings
  ctx.fillStyle = 'black';
  ctx.font = `bold ${size * 0.6}px Arial, sans-serif`;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText(number.toString(), size / 2, size / 2);

  const dataUrl = canvas.toDataURL('image/png', 1.0);

  // Cache the result
  circleCache.set(cacheKey, dataUrl);

  // Clean up canvas
  canvas.width = 0;
  canvas.height = 0;

  return dataUrl;
}

// NOTE: <QrCodeReview /> is default qr code layout for shipment flow
// NOTE: for pages which use different layouts, try to use Compound Pattern
const QrCodeReview: FC<Omit<QrCodeReviewProviderProps, 'children'>> & {
  Container: typeof Container;
  Label: typeof Label;
  Download: typeof Download;
  DownloadQrOnly: typeof DownloadQrOnly;
} = (props) => {
  return (
    <QrCodeReview.Container {...props}>
      <QrCodeReview.Label />
      <Stack
        direction="row"
        spacing={2}
        sx={{ mt: 2, width: '100%', justifyContent: 'space-between', alignItems: 'center' }}
      >
        <Stack direction="row" spacing={2} sx={{ mt: 2, width: '100%', justifyContent: 'flex-end' }}>
          <QrCodeReview.Download />
          <QrCodeReview.DownloadQrOnly />
        </Stack>
      </Stack>
    </QrCodeReview.Container>
  );
};

const Container: FC<QrCodeReviewProviderProps> = ({ children, ...rest }) => {
  return (
    <QrCodeReviewProvider {...rest}>
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          gap: '8px',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {children}
      </Box>
    </QrCodeReviewProvider>
  );
};

const Label = () => {
  const theme = useTheme();

  const { qrExportData, contentRef, fullContentRef, textContentRef } = useQrCodeReviewContext();
  const numbers = qrExportData.boxType?.match(/\d+/g) ?? [];
  const boxTypeNumber = numbers[0] ? numbers[0] : 0;

  const url = useMemo(() => {
    if (!boxTypeNumber) return '';
    return generateCircleWithNumber(boxTypeNumber, 24, 3);
  }, [boxTypeNumber]);

  const exportToText = useMemo(() => {
    return qrExportData.exportTo ? `EXPORT TO ${qrExportData.exportTo}` : 'EXPORT TO --';
  }, [qrExportData.exportTo]);

  return (
    <Box
      sx={{
        backgroundColor: 'background.paper',
        borderRadius: '8px',
        border: `2px dashed ${theme.palette.customColors.divider}`,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        overflowY: 'hidden',
        position: 'relative',
        width: `${QR_BOX_HEIGHT * QR_RATIO}px`,
        height: `${QR_BOX_HEIGHT}px`,
        boxSizing: 'border-box',
      }}
    >
      <Box
        ref={fullContentRef}
        sx={{
          width: `100%`,
          height: `100%`,
          display: 'flex',
          justifyContent: 'center',
          backgroundColor: theme.palette.customColors.white,
          alignItems: 'center',
        }}
      >
        <Box
          ref={textContentRef}
          sx={{
            display: 'flex',
            flexDirection: 'column',
            width: '100%',
            height: '100%',
            justifyContent: 'center',
            fontFamily: PRINT_FONT,
            fontWeight: 700,
            fontStyle: 'normal',
            lineHeight: '30px',
            alignItems: 'center',
            padding: '12px',
          }}
          alignItems="baseline"
        >
          <Stack direction="column" gap={LABEL_GAP} sx={{ width: '100%' }}>
            <Typography
              fontWeight={700}
              lineHeight="30px"
              fontFamily={PRINT_FONT}
              textAlign="left"
              variant="caption"
              textTransform="uppercase"
              width={QR_CODE_LABEL_WIDTH}
              fontSize={`${FONT_SIZE_LABEL}px`}
            >
              NAME OF THE EXPORTING COMPANY:
            </Typography>
            <AutoFitTextSize
              fontWeight={700}
              lineHeight="30px"
              fontFamily={PRINT_FONT}
              maxFontSize={FONT_SIZE_LABEL}
              text={`${qrExportData.exportCompany || DEFAULT_TEXT}`}
            />

            <Stack direction="row" useFlexGap flexWrap="wrap" sx={{ width: '100%', gap: '4px' }} alignItems="baseline">
              <Typography
                fontWeight={700}
                lineHeight="30px"
                fontFamily={PRINT_FONT}
                textAlign="left"
                variant="caption"
                textTransform="uppercase"
                width={QR_CODE_LABEL_WIDTH}
                fontSize={`${FONT_SIZE_LABEL}px`}
              >
                FRUIT NAME:
              </Typography>
              <Typography
                fontWeight={700}
                lineHeight="30px"
                fontFamily={PRINT_FONT}
                textAlign="left"
                variant="caption"
                textTransform="uppercase"
                fontSize={`${FONT_SIZE_LABEL}px`}
                width={QR_CODE_LABEL_WIDTH}
              >
                Fresh Durian
              </Typography>
            </Stack>
            <Stack direction="row" useFlexGap flexWrap="wrap" sx={{ width: '100%', gap: '4px' }} alignItems="baseline">
              <AutoFitTextSize
                fontWeight={700}
                lineHeight="30px"
                fontFamily={PRINT_FONT}
                text={`ORCHARD REGISTER NUMBER: ${qrExportData.orchardRegisterNumber || DEFAULT_TEXT}`}
                maxFontSize={FONT_SIZE_LABEL}
              />
            </Stack>

            <Stack direction="row" useFlexGap flexWrap="wrap" sx={{ width: '100%', gap: '4px' }} alignItems="baseline">
              <AutoFitTextSize
                fontWeight={700}
                lineHeight="30px"
                fontFamily={PRINT_FONT}
                text={`PACKING HOUSE REGISTER NUMBER: ${qrExportData.packingHouseRegisterNumber || DEFAULT_TEXT}`}
                maxFontSize={FONT_SIZE_LABEL}
              />
            </Stack>
          </Stack>

          <Stack direction="row" sx={{ width: '100%', gap: '4px' }}>
            <Stack
              direction="column"
              sx={{
                height: '100%',
                gap: '12px',
                width: `calc(100% - ${QR_CODE_SIZE}px)`,
                justifyContent: 'flex-end',
              }}
            >
              <Stack mt="6px" direction="row" useFlexGap flexWrap={'wrap'} sx={{ gap: '4px' }} alignItems="baseline">
                <Typography
                  fontWeight={700}
                  lineHeight="30px"
                  fontFamily={PRINT_FONT}
                  textAlign="left"
                  variant="caption"
                  textTransform="uppercase"
                  fontSize={`${FONT_SIZE_LABEL}px`}
                  width={QR_CODE_LABEL_WIDTH}
                >
                  PACKING DATE:
                </Typography>
                <Typography
                  fontWeight={700}
                  lineHeight="30px"
                  fontFamily={PRINT_FONT}
                  textAlign="left"
                  variant="caption"
                  textTransform="uppercase"
                  fontSize={`${FONT_SIZE_LABEL}px`}
                  width={QR_CODE_LABEL_WIDTH}
                >
                  {qrExportData.packingDate || DEFAULT_TEXT}
                </Typography>
              </Stack>

              <Typography
                fontWeight={700}
                lineHeight="30px"
                fontFamily={PRINT_FONT}
                textAlign="left"
                variant="caption"
                textTransform="uppercase"
                fontSize={`${FONT_SIZE_LABEL}px`}
                width={QR_CODE_LABEL_WIDTH}
              >
                PRODUCT OF THAILAND
              </Typography>

              <AutoFitTextSize
                fontWeight={700}
                lineHeight="30px"
                fontFamily={PRINT_FONT}
                text={exportToText}
                maxFontSize={FONT_SIZE_LABEL}
              />
            </Stack>

            <Box
              component="div"
              id={qrExportData.qrUrl}
              ref={contentRef}
              sx={{
                display: 'flex',
                justifyContent: 'flex-start',
                alignItems: 'center',
                width: `${QR_CODE_SIZE}px`,
                height: `${QR_CODE_SIZE}px`,
                flexDirection: 'column',
                backgroundColor: theme.palette.customColors.white,
                position: 'relative',
                p: '5px',
              }}
            >
              {(!url || !qrExportData.qrUrl) && <Skeleton variant="rectangular" width={110} height={110} />}
              {url && qrExportData.qrUrl && (
                <QRCode
                  logoImage={url}
                  logoWidth={250}
                  logoHeight={250}
                  ecLevel="M"
                  value={qrExportData.qrUrl}
                  // fgColor={theme.palette.customColors.qrCodeGreen}
                  logoPaddingStyle="circle"
                  size={1000}
                  removeQrCodeBehindLogo={true}
                  logoPadding={1}
                  qrStyle="squares"
                  style={{
                    width: '100%',
                    height: '100%',
                  }}
                />
              )}
            </Box>
          </Stack>
        </Box>
      </Box>
    </Box>
  );
};

const Download: FC<Omit<ButtonProps, 'children' | 'onClick'>> = ({ sx, ...props }) => {
  const qrCodeT = useTranslations('qr-code');

  const { qrExportData, fullContentRef } = useQrCodeReviewContext();
  const widthOfBox = QR_BOX_HEIGHT * QR_RATIO;

  const onDownloadFullLabel = async () => {
    if (!fullContentRef.current) return;

    const dpi = 900;
    const widthCm = 8;
    const inchPerCm = 2.54;

    const targetWidth = Math.round((widthCm / inchPerCm) * dpi);

    const currentWidth = widthOfBox;
    const currentHeight = QR_BOX_HEIGHT;

    const pixelRatio = targetWidth / currentWidth;

    const rounded = Math.ceil(pixelRatio * 10) / 10;

    const dataUrl = await toJpeg(fullContentRef.current, {
      cacheBust: true,
      skipFonts: false,
      skipAutoScale: true,
      quality: 1.0,
      width: currentWidth,
      height: currentHeight,
      pixelRatio: rounded,
      style: {
        transformOrigin: 'center center',
        overflow: 'hidden',
        boxSizing: 'border-box',
        padding: '12px',
        textRendering: 'optimizeLegibility',
        fontFamily: PRINT_FONT,
        fontWeight: '700',
        lineHeight: '30px',
      },
      backgroundColor: '#ffffff',
    });

    const link = document.createElement('a');
    link.download = `${qrExportData.batchNumber}.jpeg`;
    link.href = dataUrl;
    link.click();
  };

  return (
    <Button onClick={onDownloadFullLabel} variant="outlined" sx={{ width: 'auto', ...sx }} {...props}>
      {qrCodeT('download-full-label')}
    </Button>
  );
};

const DownloadQrOnly: FC<Omit<ButtonProps, 'children' | 'onClick'>> = ({ sx, ...props }) => {
  const qrCodeT = useTranslations('qr-code');
  const { qrExportData, contentRef } = useQrCodeReviewContext();

  const onDownload = async () => {
    if (!contentRef.current) return;

    const width = contentRef.current.clientWidth;

    const dataUrl = await toPng(contentRef.current, {
      cacheBust: true,
      skipFonts: false,
      skipAutoScale: true,
      width: width,
      height: width,
      pixelRatio: 5,
      quality: 1.0,
      style: {
        transformOrigin: 'center center',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        boxSizing: 'border-box',
      },
      backgroundColor: '#ffffff',
    });

    const link = document.createElement('a');
    link.download = `${qrExportData.batchNumber}.png`;
    link.href = dataUrl;
    link.click();
  };

  return (
    <Button onClick={onDownload} variant="outlined" sx={{ width: 'auto', ...sx }} {...props}>
      {qrCodeT('download')}
    </Button>
  );
};

QrCodeReview.Container = Container;
QrCodeReview.Label = Label;
QrCodeReview.Download = Download;
QrCodeReview.DownloadQrOnly = DownloadQrOnly;

export default QrCodeReview;
