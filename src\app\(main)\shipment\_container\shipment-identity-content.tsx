/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { Box, Fade, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC } from 'react';

import { FormTextInput } from 'components';
import { UseFormReturn } from 'react-hook-form';
import { ShipmentIdentityType, useCreateShipmentStore } from 'store/useCreateShipmentStore';
import UploadImage from '../_components/upload-images';
import { capturePosthog } from 'utils/posthog';
import { REGEX_INPUT_NAME_FIELD } from 'utils';

export interface ShipmentIdentityFormProps {
  formOptions: UseFormReturn<ShipmentIdentityType, any, ShipmentIdentityType>;
}

export const ShipmentIdentityContent: FC<ShipmentIdentityFormProps> = ({ formOptions }) => {
  const shipmentT = useTranslations('shipment');
  const formT = useTranslations('form');
  const { updateShipmentIdentity } = useCreateShipmentStore();
  // const regex = /^[\p{L}\p{N}\s\u0E00-\u0E7F\-()]+$/u;
  const {
    control,
    formState: { errors },
    handleSubmit,
  } = formOptions;

  const onSubmit = (data: ShipmentIdentityType) => {
    capturePosthog('shipment_identity');
    updateShipmentIdentity(data.name);
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit(onSubmit)}
      sx={{ width: '100%', display: 'flex', gap: '16px', mt: '16px', height: '100%', flexDirection: 'column' }}
    >
      <FormTextInput<ShipmentIdentityType>
        name="name"
        required
        requiredMessage={shipmentT('shipment-name-required')}
        pattern={REGEX_INPUT_NAME_FIELD}
        patternMessage={formT('not-allow-characters')}
        // pattern={regex}
        // patternMessage={formT('text-invalid')}
        errors={errors}
        control={control}
        label={shipmentT('shipment-name')}
        placeholder={shipmentT('shipment-name-placeholder')}
        maxLength={100}
      />

      <Fade in={true}>
        <Box component="div">
          <Typography variant="body1" mb={1}>
            {shipmentT('shipment-photo')}
          </Typography>
          <UploadImage />
        </Box>
      </Fade>
    </Box>
  );
};
