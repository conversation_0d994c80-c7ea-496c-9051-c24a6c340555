'use client';

import { Box, Button, IconButton, Typography, useTheme } from '@mui/material';
import { useTranslations } from 'next-intl';

import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import DeleteIcon from '@mui/icons-material/Delete';
import { <PERSON>readcrumbs, Stepper } from 'components';
import { FIXED_LOCATION } from 'constant/common';
import dayjs from 'dayjs';
import { useCreateShipmentMutate } from 'hooks/mutates/useCreateShipmentMutate';
import { useGetReceivingByIdsQuery } from 'hooks/queries/useGetReceivingByIdsQuery';
import { useDeviceHeight } from 'hooks/useDeviceHeight';
import { headerHeight } from 'layouts/main/constant';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { clientRoutes } from 'routes/client-routes';
import {
  FormShipmentStepEnum,
  ShipmentIdentityType,
  useCreateShipmentStore,
  VarietyOptionValue,
} from 'store/useCreateShipmentStore';
import { Receipt } from 'types';
import { removeImgSuffix, safeJSONParse } from 'utils';
import { sendEvent } from 'utils/gtag';
import { capturePosthog } from 'utils/posthog';
import toastMessages from 'utils/toastMessages';
import { v4 } from 'uuid';
import { FormContent, SelectPackagingInformationDrawer } from '../_components';
import { DetailsDrawer } from '../_components/details-drawer';
import { QrCodeReviewContent } from '../_container/qr-code-review-content';
import { ShipmentIdentityContent } from '../_container/shipment-identity-content';
import { UploadDocumentsContent } from '../_container/upload-documents-content';
import { defaultReceiptFormValues, ReceiptPackaging, UploadReceiptContent } from '../_container/upload-receipt-content';
import { SelectPackagingInformationContent } from './select-packaging-information-content';
import { ActionButtonGroup, Root, StepContent } from '../_components/components.style';
import { LoadingLayout } from 'layouts/main/loading-layout';

const defaultShipmentForm = {
  name: '',
};

export const ShipmentFormStep = () => {
  const shipmentT = useTranslations('shipment');
  const commonT = useTranslations('common');

  const [loadingPage, setLoadingPage] = useState(false);
  const { setShowWarningDialog } = useCreateShipmentStore();

  const {
    formStep,
    informationFormValues,
    ocrFile,
    additionalFiles,
    updateStep,
    updateOrcForm,
    generateQrCodeBatchLot,
    qrCodeBatchlots,
    updateShipmentIdentity,
    setIsUploadOcrError,
    receivingIds,
    setManualInput,
    manualInputOrc,
    setReceivingDetails,
    shipmentPhotos,
    shipmentIdentity,
  } = useCreateShipmentStore();

  const shipmentForm = useForm<ShipmentIdentityType>({
    defaultValues: {
      ...defaultShipmentForm,
      ...shipmentIdentity,
    },
    mode: 'onBlur',
  });

  const { data: receivingData } = useGetReceivingByIdsQuery([...receivingIds]);

  useEffect(() => {
    if (receivingData?.length) {
      setReceivingDetails(receivingData);
    }
  }, [receivingData, setReceivingDetails]);

  const stepContentRef = useRef<HTMLDivElement>(null);

  const receiptFormOptions = useForm<ReceiptPackaging>({
    defaultValues: {
      ...defaultReceiptFormValues,
    },
    mode: 'onSubmit',
  });

  const router = useRouter();
  const deviceHeight = useDeviceHeight();

  const minHeight = deviceHeight - headerHeight - 420;

  const { mutateAsync, isPending } = useCreateShipmentMutate({
    onSuccess: async () => {
      capturePosthog('shipment_created_success');
      toastMessages.success(shipmentT('create-success'));
      router.push(clientRoutes.shipment);
    },
    onError: () => {
      toastMessages.error(shipmentT('create-failed'));
      setLoadingPage(false);
    },
  });

  const [openDetailModal, setOpenDetailModal] = useState<boolean>(false);
  const theme = useTheme();

  const steps = [
    shipmentT('shipment-identity'),
    shipmentT('information-step'),
    shipmentT('receipt-step'),
    shipmentT('add-document-step'),
    shipmentT('qr-review'),
  ];

  const renderTitle = () => {
    switch (formStep) {
      case FormShipmentStepEnum.ShipmentIdentity:
        return shipmentT('shipment-identity');
      case FormShipmentStepEnum.DurianInformationStep:
        return shipmentT('information-step');
      case FormShipmentStepEnum.ReceiptStep:
        return shipmentT('receipt-step');
      case FormShipmentStepEnum.UploadDocumentStep:
        return shipmentT('add-document-step');
      case FormShipmentStepEnum.QrReviewStep:
        return shipmentT('qr-review');
      default:
        break;
    }
  };

  const checkDisableNextButton = () => {
    let isValid = true;

    switch (formStep) {
      case FormShipmentStepEnum.DurianInformationStep:
        isValid = informationFormValues.length > 0;
        break;
      case FormShipmentStepEnum.ReceiptStep:
        isValid = !!ocrFile;
        break;
      case FormShipmentStepEnum.UploadDocumentStep:
      default:
        isValid = true;
        break;
    }

    return isValid;
  };

  const onSubmitShipment = async (asDraft?: boolean) => {
    const isValid = await shipmentForm.trigger();
    if (!isValid) {
      updateStep(FormShipmentStepEnum.ShipmentIdentity);
      return;
    }

    const fileIds = additionalFiles.map((it) => it.url)?.map((it) => removeImgSuffix(it));
    const photoIds = shipmentPhotos?.map((it) => removeImgSuffix(it.url));
    setLoadingPage(true);
    const ocrFormValue = receiptFormOptions.getValues();

    await mutateAsync({
      status: asDraft ? 'draft' : 'published',
      shipmentName: shipmentForm.getValues().name ?? '',
      receivingProductIds: receivingIds,
      receipt: ocrFormValue.receiptNumber
        ? {
            numberOfBoxes: Number(ocrFormValue.numberOfBoxes),
            destinationCountry: ocrFormValue.destinationCountry,
            exportDate: dayjs(ocrFormValue.exportDate).unix(),
            totalWeightKg: Number(ocrFormValue.totalWeightKg),
            receiptNumber: ocrFormValue.receiptNumber,
            transportationMode: ocrFormValue.transportationMode,
            containerNumber: ocrFormValue.containerNumber ?? '',
            truckRegistrationNumber: ocrFormValue.truckNumber ?? '',
            trailerRegistrationNumber: ocrFormValue.trailerNumber ?? '',
            orchardRegisterNumber: ocrFormValue.orchardNo ?? '',
            borderCheckpointName: ocrFormValue.borderCheckpointName,
            nameOfExportingCompany: ocrFormValue.nameOfExportingCompany ?? '',
          }
        : undefined,
      latitude: FIXED_LOCATION.SHIPMENT.latitude,
      longitude: FIXED_LOCATION.SHIPMENT.longitude,
      packages: informationFormValues?.map((packageInfo, idx) => {
        const varietyParse = safeJSONParse<VarietyOptionValue>(packageInfo.variety);

        const packingDate = packageInfo.packingDate ? dayjs(packageInfo.packingDate).unix() : undefined;

        return {
          brandName: packageInfo.brand,
          productType: packageInfo.boxType,
          varietyGradeJoinId: packageInfo.grade,
          weightKg: Number(packageInfo.netWeight),
          numberOfBoxes: Number(packageInfo.totalBoxes),
          qrCode: qrCodeBatchlots[idx]?.qrId,
          batchlot: qrCodeBatchlots[idx]?.batchlot,
          varietyId: varietyParse?.originalId ?? packageInfo.variety,
          varietyName: varietyParse?.name ? varietyParse.name : undefined,
          gradeId: packageInfo.grade,
          packingDate,
        };
      }),
      documentIds: fileIds.length ? [...fileIds] : undefined,
      shipmentPhotoIds: photoIds?.length ? [...photoIds] : undefined,
    });
  };

  const handleSubmitReceiptForm = (values: Receipt) => {
    const id = ocrFile?.id ?? v4();

    updateOrcForm({
      ...values,
      id,
      sourceFrom: values.sourceFrom ?? 'ephyto',
    });

    setIsUploadOcrError(false);
  };

  const onNext = async () => {
    try {
      if (formStep === FormShipmentStepEnum.QrReviewStep) {
        await onSubmitShipment();
        return;
      } else if (formStep === FormShipmentStepEnum.UploadDocumentStep) {
        setLoadingPage(true);
        sendEvent('document');
        await generateQrCodeBatchLot();
        sendEvent('generate_qr');
        sendEvent('assign_grade');
        setLoadingPage(false);
      } else if (formStep === FormShipmentStepEnum.ReceiptStep) {
        const { trigger, getValues } = receiptFormOptions;
        const isValid = await trigger();

        if (!isValid) return;

        if (isValid) {
          handleSubmitReceiptForm(getValues());
        }
      } else if (formStep === FormShipmentStepEnum.ShipmentIdentity) {
        const values = await shipmentForm.trigger();
        if (!values) return;
        updateShipmentIdentity(shipmentForm.getValues().name);
      } else if (formStep === FormShipmentStepEnum.DurianInformationStep) {
        sendEvent('packaging');
      }

      updateStep(formStep + 1);
    } catch {
      toastMessages.error(commonT('common-error'));
      setLoadingPage(false);
    } finally {
      setLoadingPage(false);
    }
  };

  const onGoBack = () => {
    if (formStep === FormShipmentStepEnum.ShipmentIdentity) {
      const shipmentIdentityValues = shipmentForm.getValues();
      updateShipmentIdentity(shipmentIdentityValues?.name ?? '');
    }

    updateStep(formStep - 1);
  };

  const breadcrumbs = [
    {
      label: commonT('shipment'),
      href: clientRoutes.shipment,
    },
    {
      label: commonT('create-shipment-title'),
      href: clientRoutes.createShipment,
    },
  ];

  const isReceipt = formStep === FormShipmentStepEnum.ReceiptStep;

  const continueBtnLabel = useMemo(() => {
    switch (formStep) {
      case FormShipmentStepEnum.UploadDocumentStep:
        return shipmentT('generate-qr-code');
      case FormShipmentStepEnum.QrReviewStep:
        return commonT('submit');
      case FormShipmentStepEnum.ReceiptStep:
      case FormShipmentStepEnum.DurianInformationStep:
      default:
        return commonT('continue');
    }
  }, [commonT, formStep, shipmentT]);

  const actualStep = formStep - 1;

  return (
    <LoadingLayout loading={loadingPage}>
      <Box component="div" id="create-shipment-wrapper" sx={{ flex: 1, p: '20px' }}>
        <Breadcrumbs items={breadcrumbs} />
        <Root>
          <Box sx={{ display: 'flex', gap: '8px', height: '60px', alignItems: 'center' }}>
            <IconButton onClick={() => setShowWarningDialog(true)}>
              <ArrowBackIcon />
            </IconButton>
            <Typography variant="h6" my="12px">
              {commonT('create-shipment-title')}
            </Typography>
          </Box>
          <Box
            sx={{
              display: 'flex',
              gap: '12px',
              alignItems: 'center',
              backgroundColor: theme.palette.customColors.primary100,
              p: '12px 16px',
              boxShadow: 1,
              borderRadius: 2,
            }}
          >
            <Typography>
              {shipmentT.markup('batch-lot-selected', {
                x: receivingIds?.length ?? 0,
              })}
            </Typography>{' '}
            <Typography
              component="a"
              onClick={() => setOpenDetailModal(true)}
              variant="body1"
              color="primary"
              sx={{
                cursor: 'pointer',
                '&:hover': {
                  textDecoration: 'underline',
                },
              }}
            >
              {commonT('view-detail')}
            </Typography>
          </Box>
          <Stepper activeStep={actualStep} steps={steps} />
          <StepContent
            sx={{ boxShadow: 1, p: '20px 16px', position: 'relative', height: `fit-content`, flex: 'unset' }}
          >
            <Typography variant="body1" fontWeight="bold">
              {renderTitle()}
            </Typography>
            {isReceipt && (ocrFile?.receiptNumber || manualInputOrc) && (
              <IconButton
                color="error"
                size="medium"
                onClick={() => {
                  updateOrcForm(undefined);
                  receiptFormOptions.reset({ ...defaultReceiptFormValues });
                  setManualInput(false);
                }}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  width: '50px',
                  position: 'absolute',
                  right: '12px',
                  top: '16px',
                  cursor: 'pointer',
                  justifyContent: 'flex-end',
                }}
              >
                <DeleteIcon fontSize="inherit" />
              </IconButton>
            )}
            <Box
              component="div"
              ref={stepContentRef}
              sx={{
                width: '100%',
                minHeight: `${minHeight}px`,
                position: 'relative',
                display: 'flex',
              }}
            >
              <FormContent active={formStep === FormShipmentStepEnum.ShipmentIdentity} minHeight={minHeight}>
                <ShipmentIdentityContent formOptions={shipmentForm} />
              </FormContent>
              <FormContent active={formStep === FormShipmentStepEnum.DurianInformationStep} minHeight={minHeight}>
                <SelectPackagingInformationContent />
              </FormContent>
              <FormContent active={formStep === FormShipmentStepEnum.ReceiptStep} minHeight={minHeight}>
                <UploadReceiptContent receiptFormOptions={receiptFormOptions} />
              </FormContent>
              <FormContent active={formStep === FormShipmentStepEnum.UploadDocumentStep} minHeight={minHeight}>
                <UploadDocumentsContent minHeight={minHeight} />
              </FormContent>
              <FormContent active={formStep === FormShipmentStepEnum.QrReviewStep} minHeight={minHeight}>
                <QrCodeReviewContent />
              </FormContent>
            </Box>
          </StepContent>
          <ActionButtonGroup>
            {formStep !== FormShipmentStepEnum.QrReviewStep && (
              <Button
                variant="text"
                onClick={() => {
                  setShowWarningDialog(true);
                }}
                sx={{
                  color: theme.palette.customColors.neutral700,
                  position: 'absolute',
                  top: 0,
                  left: '-10px',
                }}
              >
                {shipmentT('discard')}
              </Button>
            )}

            <Button
              variant="text"
              sx={{ width: '200px', color: theme.palette.customColors.neutral700 }}
              onClick={() => {
                onSubmitShipment(true);
              }}
            >
              {commonT('save-as-draft')}
            </Button>
            <Button variant="outlined" sx={{ width: '200px' }} onClick={onGoBack} disabled={isPending}>
              {commonT('back')}
            </Button>
            <Button
              variant="contained"
              sx={{ width: '200px' }}
              disabled={!checkDisableNextButton()}
              loading={isPending || loadingPage}
              onClick={onNext}
            >
              {continueBtnLabel}
            </Button>
          </ActionButtonGroup>
        </Root>
      </Box>
      <DetailsDrawer open={openDetailModal} toggle={setOpenDetailModal} />
      <SelectPackagingInformationDrawer />
    </LoadingLayout>
  );
};
