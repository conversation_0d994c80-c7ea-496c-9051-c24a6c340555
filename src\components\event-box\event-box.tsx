'use client';

import { FC, ReactNode } from 'react';

import { Box, Typography } from '@mui/material';
import { theme } from 'styles/theme';
import { ImageReviewModal } from 'components/image-review-modal';

interface EventBoxProps {
  title: string;
  description: string;
  customDescription?: ReactNode;
  onClick?: () => void;
  imgUrl?: string | null;
  hasBorderBottom?: boolean;
  inDrawer?: boolean;
  padding?: string;
}

export const EventBox: FC<EventBoxProps> = ({
  title,
  description,
  customDescription,
  onClick,
  imgUrl,
  hasBorderBottom,
  inDrawer,
  padding = '16px 20px',
}) => (
  <Box
    component="div"
    sx={{
      display: 'flex',
      alignItems: 'center',
      borderBottom: hasBorderBottom ? `1px solid ${theme.palette.grey[300]}` : 'unset',
      padding,
      gap: '12px',
      cursor: onClick ? 'pointer' : 'unset',
      transition: 'background-color 0.2s ease',
      '&:hover': onClick
        ? {
            backgroundColor: theme.palette.customColors.blueTint,
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
          }
        : {},
      borderRadius: '4px',
      position: 'relative',
      '&::after':
        onClick && !inDrawer
          ? {
              content: '""',
              position: 'absolute',
              top: 0,
              right: 0,
              width: '4px',
              height: '100%',
              backgroundColor: 'transparent',
              transition: 'background-color 0.2s ease',
            }
          : {},
      '&:hover::after':
        onClick && !inDrawer
          ? {
              backgroundColor: theme.palette.primary.main,
            }
          : {},
    }}
  >
    <Box
      sx={{
        height: '100%',
        width: '80px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <Box
        sx={{
          height: '80px',
          width: '80px',
          borderRadius: '8px',
          transition: onClick && !inDrawer ? 'transform 0.2s ease' : 'none',
          '&:hover': onClick
            ? {
                transform: 'scale(1.05)',
              }
            : {},
        }}
      >
        <ImageReviewModal imageUrl={imgUrl || null} isZoomImage={!inDrawer} />
      </Box>
    </Box>
    <Box
      onClick={onClick}
      sx={{
        flex: 1,
        display: 'flex',
        height: '100%',
        width: '100%',
        flexDirection: 'column',
        justifyContent: 'space-between',
      }}
    >
      <Typography
        component="p"
        variant="body3"
        sx={{
          transition: onClick ? 'color 0.2s ease' : 'none',
          fontWeight: 500,
          ...(onClick && { '&:hover': { color: theme.palette.primary.main } }),
        }}
      >
        {title}
      </Typography>
      <Typography component="p" variant="body4" sx={{ color: 'text.secondary' }}>
        {description}
      </Typography>
      <Typography
        component="div"
        variant="body4"
        sx={{
          color: 'text.secondary',
          overflowX: 'hidden',
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis',
        }}
      >
        {customDescription}
      </Typography>
    </Box>
    {onClick && (
      <Box
        sx={{
          opacity: 0,
          transition: 'opacity 0.2s ease',
          position: 'absolute',
          right: 16,
          color: theme.palette.primary.main,
          '&:parent:hover': {
            opacity: 1,
          },
        }}
      />
    )}
  </Box>
);
