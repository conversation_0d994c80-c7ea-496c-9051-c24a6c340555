'use client';

import { ReactNode } from 'react';

import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import buddhistEra from 'dayjs/plugin/buddhistEra';
import { getCookieLocale } from 'utils/cookie-client';
import customParseFormat from 'dayjs/plugin/customParseFormat';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);
dayjs.extend(buddhistEra);


const ClientLocalizationProvider = ({ children }: { children: ReactNode }) => {
  const locale = getCookieLocale() ?? 'th';


  dayjs.locale(locale);

  return (
    <LocalizationProvider
      dateAdapter={AdapterDayjs}
      adapterLocale={locale}
    >
      {children}
    </LocalizationProvider>
  );
};

export default ClientLocalizationProvider;
