'use client';

import { Box, Typography } from '@mui/material';
import ImageIcon from '@mui/icons-material/Image';
import { useTranslations } from 'next-intl';
import { FC } from 'react';
import { theme } from 'styles/theme';
import { ShipmentHistoryChange } from 'types/shipment-history';

interface ShipmentPhotoChangeProps {
  changes: ShipmentHistoryChange[];
}

export const ShipmentPhotoChange: FC<ShipmentPhotoChangeProps> = ({ changes }) => {
  const t = useTranslations('shipment');

  const removedChanges = changes.filter((c) => c.changeType === 'removed');
  const addedChanges = changes.filter((c) => c.changeType === 'added');

  const renderPhotoItem = (filename: string | null) => (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
      <Box
        sx={{
          width: 24,
          height: 24,
          borderRadius: 1,
          backgroundColor: theme.palette.customColors.gray6,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '1px solid #e0e0e0',
        }}
      >
        <ImageIcon sx={{ fontSize: 14, color: theme.palette.customColors.primary }} />
      </Box>
      <Typography
        variant="body2"
        color="text.secondary"
        sx={{
          fontSize: '14px',
        }}
      >
        {filename || ''}
      </Typography>
    </Box>
  );

  const renderChangeSection = (
    sectionChanges: ShipmentHistoryChange[],
    title: string,
    valueExtractor: (change: ShipmentHistoryChange) => string | null,
    keyPrefix: string,
    marginBottom?: number
  ) => (
    <Box sx={{ mb: marginBottom }}>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontSize: '14px' }}>
        {title}
      </Typography>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          background: theme.palette.customColors.neutral100,
          borderRadius: 1,
          px: 2,
          py: 1,
        }}
      >
        {sectionChanges.map((change, index) => (
          <Box key={`${keyPrefix}-${change.field}-${index}`}>{renderPhotoItem(valueExtractor(change))}</Box>
        ))}
      </Box>
    </Box>
  );

  return (
    <Box sx={{ background: theme.palette.customColors.neutral50, borderRadius: 1, px: 2, py: 1 }}>
      <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1, fontSize: '14px' }}>
        {t('history-field-shipment-photos')}
      </Typography>

      {removedChanges.length > 0 &&
        renderChangeSection(
          removedChanges,
          t('history-value-removed'),
          (change) => (typeof change.oldValue === 'string' ? change.oldValue : null),
          'removed',
          2
        )}

      {addedChanges.length > 0 &&
        renderChangeSection(
          addedChanges,
          t('history-value-added'),
          (change) => (typeof change.newValue === 'string' ? change.newValue : null),
          'added'
        )}
    </Box>
  );
};
