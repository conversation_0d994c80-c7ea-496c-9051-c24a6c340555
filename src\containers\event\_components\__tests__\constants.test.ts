/* eslint-disable @typescript-eslint/no-explicit-any */
// Mock the getCookieLocale function before importing the constants
jest.mock('utils/cookie-client', () => ({
  getCookieLocale: jest.fn(() => 'en'), // Default to English
}));

import { isOtherRejectReason, rejectReasonOptions } from '../constants';

describe('constants.ts', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('isOtherRejectReason', () => {
    it('should return true when value matches English "Other" option', () => {
      const result = isOtherRejectReason('Other');
      expect(result).toBe(true);
    });

    it('should return false when value matches other English options', () => {
      const result = isOtherRejectReason('Durians are harvested too early.');
      expect(result).toBe(false);
    });

    it('should return false when value is undefined', () => {
      const result = isOtherRejectReason();
      expect(result).toBe(false);
    });

    it('should return false when value is empty string', () => {
      const result = isOtherRejectReason('');
      expect(result).toBe(false);
    });

    it('should return false when value does not match any option', () => {
      const result = isOtherRejectReason('random text');
      expect(result).toBe(false);
    });

    it('should handle null values gracefully', () => {
      const result = isOtherRejectReason(null as any);
      expect(result).toBe(false);
    });

    it('should handle whitespace-only strings', () => {
      expect(isOtherRejectReason('   ')).toBe(false);
      expect(isOtherRejectReason('\t')).toBe(false);
      expect(isOtherRejectReason('\n')).toBe(false);
    });

    it('should be case sensitive', () => {
      expect(isOtherRejectReason('other')).toBe(false); // lowercase
      expect(isOtherRejectReason('OTHER')).toBe(false); // uppercase
      expect(isOtherRejectReason('Other')).toBe(true);  // correct case
    });
  });

  describe('rejectReasonOptions', () => {
    it('should return English reject reason options by default', () => {
      expect(rejectReasonOptions).toEqual([
        'Durians are harvested too early.',
        'Durians are overripe.',
        'Durians have diseases (black spots, mold, or signs of rot).',
        'Durians have pest problems (holes/ damage from insects/worms).',
        'The harvested weight and delivery weight differ by more than 3%.',
        'Durian does not have the correct GAP number.',
        'Durians were exposed to sun or rain for too long.',
        'Durians arrived too long after harvest and are no longer fresh.',
        'Harvest information (e.g., date or quantity) is incorrect.',
        'Other',
      ]);
    });

    it('should have "Other" as the last option', () => {
      expect(rejectReasonOptions[rejectReasonOptions.length - 1]).toBe('Other');
    });

    it('should have exactly 10 options', () => {
      expect(rejectReasonOptions.length).toBe(10);
    });

    it('should contain all expected English options', () => {
      const expectedOptions = [
        'Durians are harvested too early.',
        'Durians are overripe.',
        'Durians have diseases (black spots, mold, or signs of rot).',
        'Durians have pest problems (holes/ damage from insects/worms).',
        'The harvested weight and delivery weight differ by more than 3%.',
        'Durian does not have the correct GAP number.',
        'Durians were exposed to sun or rain for too long.',
        'Durians arrived too long after harvest and are no longer fresh.',
        'Harvest information (e.g., date or quantity) is incorrect.',
        'Other',
      ];

      expectedOptions.forEach(option => {
        expect(rejectReasonOptions).toContain(option);
      });
    });
  });

  describe('integration tests', () => {
    it('should work correctly with English locale for both functions', () => {
      // Test that the "Other" option from the array works with isOtherRejectReason
      const otherOption = rejectReasonOptions[rejectReasonOptions.length - 1];
      expect(isOtherRejectReason(otherOption)).toBe(true);

      // Test that other options don't match
      expect(isOtherRejectReason(rejectReasonOptions[0])).toBe(false);
    });

    it('should correctly identify the "Other" option', () => {
      // Test with the exact "Other" string
      expect(isOtherRejectReason('Other')).toBe(true);

      // Test with other options from the array
      expect(isOtherRejectReason('Durians are harvested too early.')).toBe(false);
      expect(isOtherRejectReason('Durians are overripe.')).toBe(false);
      expect(isOtherRejectReason('Durians have diseases (black spots, mold, or signs of rot).')).toBe(false);
    });
  });

  describe('function behavior validation', () => {
    it('should validate isOtherRejectReason function logic', () => {
      // Test the function with various inputs
      expect(isOtherRejectReason('Other')).toBe(true);
      expect(isOtherRejectReason('other')).toBe(false);
      expect(isOtherRejectReason('OTHER')).toBe(false);
      expect(isOtherRejectReason('')).toBe(false);
      expect(isOtherRejectReason()).toBe(false);
      expect(isOtherRejectReason(null as any)).toBe(false);
      expect(isOtherRejectReason('Some random text')).toBe(false);
    });

    it('should validate array structure', () => {
      // Ensure the array is not empty
      expect(rejectReasonOptions.length).toBeGreaterThan(0);

      // Ensure all items are strings
      rejectReasonOptions.forEach(option => {
        expect(typeof option).toBe('string');
        expect(option.length).toBeGreaterThan(0);
      });

      // Ensure no duplicate options
      const uniqueOptions = [...new Set(rejectReasonOptions)];
      expect(uniqueOptions.length).toBe(rejectReasonOptions.length);
    });

    it('should have consistent array indexing', () => {
      // Test that the last item (index 9) is "Other"
      expect(rejectReasonOptions[9]).toBe('Other');

      // Test that accessing the array with length-1 gives the same result
      expect(rejectReasonOptions[rejectReasonOptions.length - 1]).toBe('Other');
    });
  });

  describe('constants module structure', () => {
    it('should export the expected functions and constants', () => {
      expect(typeof isOtherRejectReason).toBe('function');
      expect(Array.isArray(rejectReasonOptions)).toBe(true);
    });

    it('should have isOtherRejectReason function with correct signature', () => {
      // Function should accept optional string parameter
      expect(isOtherRejectReason.length).toBe(1); // Function has 1 parameter
    });
  });
});
