'use client';

import { Box, Fade, Typography } from '@mui/material';
import cn from 'clsx';
import { useTranslations } from 'next-intl';
import { FC, useCallback, useEffect, useRef, useState } from 'react';
import OtpInput from 'react-otp-input';
import { useAuthStore } from 'store/useAuthStore';
import { formatCountdownTimerAuto } from 'utils';

interface OtpInputStepProps {
  handleRequestOtp: (phone: string) => void;
}

export const OtpInputStep: FC<OtpInputStepProps> = ({ handleRequestOtp }) => {
  const authT = useTranslations('auth');
  const [secondsLeft, setSecondsLeft] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const { remainingTime, phoneNumber, otp, setOtp, showCountDown, setShowCountDown, sampleOtp, errorOtp } =
    useAuthStore();

  const startCountdown = useCallback(() => {
    if (intervalRef.current !== null) return;

    intervalRef.current = setInterval(() => {
      setSecondsLeft((prev) => {
        if (prev <= 1) {
          clearInterval(intervalRef.current!);
          intervalRef.current = null;
          setShowCountDown(false);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  }, [setShowCountDown]);

  useEffect(() => {
    if (showCountDown) {
      startCountdown();
      setSecondsLeft(remainingTime);
    }
    return () => {
      stopCountdown();
    };
  }, [remainingTime, showCountDown, startCountdown]);

  const stopCountdown = () => {
    clearInterval(intervalRef.current!);
    intervalRef.current = null;
  };

  const lengthPhoneNumber = phoneNumber.length;

  return (
    <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }} component="div">
      <Typography variant="body1" textAlign="center" mb="16px">
        {authT.rich('otp-description', {
          strong: (chunk) => <b>{chunk}</b>,
          phoneValue:
            lengthPhoneNumber > 3
              ? `${phoneNumber.slice(0, lengthPhoneNumber - 3).replace(/\d/g, 'X')}${phoneNumber.slice(-3)}`
              : '',
        })}
      </Typography>
      <OtpInput
        inputStyle={{
          width: '52px',
          height: '52px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          padding: 0,
          fontSize: '20px',
          borderRadius: '4px',
          borderWidth: '2px',
          borderStyle: 'solid',
          transition: 'border-color 0.3s',
          outline: 'none',
        }}
        containerStyle={{
          width: '100%',
          display: 'flex',
          justifyContent: 'space-between',
        }}
        shouldAutoFocus
        value={otp}
        onChange={setOtp}
        numInputs={6}
        inputType="number"
        renderInput={(props) => (
          <>
            <style>
              {`
              .otp-input::-webkit-outer-spin-button,
              .otp-input::-webkit-inner-spin-button {
                -webkit-appearance: none;
                margin: 0;
              }
              .otp-input[type=number] {
                -moz-appearance: textfield;
              }
              `}
            </style>
            <input
              {...props}
              className={cn('otp-input', {
                error: !!errorOtp,
              })}
            />
          </>
        )}
      />

      {errorOtp && (
        <Fade in={true}>
          <Typography variant="caption" textAlign="center" color="error" my="8px">
            {authT('otp-error')}
          </Typography>
        </Fade>
      )}

      <Typography variant="caption" component="p" my="16px" textAlign="center">
        {showCountDown &&
          authT.rich('otp-resend-countdown', {
            otpResend: (chunk) => (
              <Typography component="span" variant="body1" color="primary">
                {chunk}
              </Typography>
            ),
            second: formatCountdownTimerAuto(secondsLeft),
          })}
        {!showCountDown && (
          <Typography
            component="span"
            variant="caption"
            color="primary"
            onClick={() => handleRequestOtp(phoneNumber)}
            sx={{ cursor: 'pointer' }}
          >
            {authT('otp-resend')}
          </Typography>
        )}
      </Typography>

      <Fade in={!!sampleOtp}>
        <Typography variant="caption" textAlign="center">
          {sampleOtp}
        </Typography>
      </Fade>
    </Box>
  );
};
