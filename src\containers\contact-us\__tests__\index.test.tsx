/* eslint-disable @next/next/no-img-element */
/* eslint-disable @typescript-eslint/no-require-imports */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { ContactUs } from '../index';
import { theme } from 'styles/theme';

// Mock dependencies
jest.mock('next-intl', () => ({
  useTranslations: jest.fn(),
}));

jest.mock('utils/toastMessages', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    error: jest.fn(),
    success: jest.fn(),
    warning: jest.fn(),
    default: jest.fn(),
  },
}));

jest.mock('components', () => ({
  Image: ({ src, alt, width, height }: { src: string; alt: string; width: number; height: number }) => (
    <img src={src} alt={alt} width={width} height={height} data-testid="mock-image" />
  ),
}));

jest.mock('configs/app-config', () => ({
  basePathProd: '/test-base-path',
}));

// Mock assets
jest.mock('assets/icons/line.svg', () => 'line-icon.svg', { virtual: true });
jest.mock('assets/icons/contact-us.svg', () => 'contact-us-icon.svg', { virtual: true });

// Mock clipboard API
const mockWriteText = jest.fn();

// Mock clipboard in jest.setup.ts style
Object.assign(navigator, {
  clipboard: {
    writeText: mockWriteText,
  },
});

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider theme={theme}>{children}</ThemeProvider>
);

describe('ContactUs', () => {
  const mockContactT = jest.fn();
  const mockToastMessages = require('utils/toastMessages').default;

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup useTranslations mock
    const { useTranslations } = require('next-intl');
    useTranslations.mockReturnValue(mockContactT);

    // Setup default translation returns
    mockContactT.mockImplementation((key: string) => {
      const translations: Record<string, string> = {
        'title': 'Contact Us',
        'help-title': 'How can we help you?',
        'tell-us': 'Tell us about your issue and we will get back to you.',
        'contact-directly': 'Contact us directly',
        'line-support': 'Line Support: @propass_support',
        'phone-support': 'Phone: 02-123-4567',
        'email-support': 'Email: <EMAIL>',
        'email-template-title': 'Email Template',
        'email-template-name': 'Your name and contact information',
        'email-template-description': 'Detailed description of the issue',
        'email-template-screenshots': 'Screenshots or attachments if applicable',
        'copy-button': 'Copy Email Template',
        'copy-success': 'Email template copied to clipboard',
        'copy-error': 'Failed to copy email template',
      };
      return translations[key] || key;
    });

    // Setup clipboard mock to resolve successfully by default
    mockWriteText.mockResolvedValue(undefined);
  });

  describe('Component Rendering', () => {
    it('renders contact us component with correct structure', () => {
      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      expect(screen.getByText('Contact Us')).toBeInTheDocument();
      expect(screen.getByText('How can we help you?')).toBeInTheDocument();
      expect(screen.getByText('Tell us about your issue and we will get back to you.')).toBeInTheDocument();
      expect(screen.getByText('Contact us directly')).toBeInTheDocument();
    });

    it('renders contact information correctly', () => {
      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      expect(screen.getByText('Line Support: @propass_support')).toBeInTheDocument();
      expect(screen.getByText('Phone: 02-123-4567')).toBeInTheDocument();
      expect(screen.getByText('Email: <EMAIL>')).toBeInTheDocument();
    });

    it('renders email template section correctly', () => {
      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      expect(screen.getByText('Email Template')).toBeInTheDocument();
      expect(screen.getByText('Your name and contact information')).toBeInTheDocument();
      expect(screen.getByText('Detailed description of the issue')).toBeInTheDocument();
      expect(screen.getByText('Screenshots or attachments if applicable')).toBeInTheDocument();
    });

    it('renders copy email button', () => {
      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      const copyButton = screen.getByRole('button', { name: /copy email template/i });
      expect(copyButton).toBeInTheDocument();
      expect(copyButton).toHaveTextContent('Copy Email Template');
    });

    it('renders images with correct props', () => {
      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      const images = screen.getAllByTestId('mock-image');
      expect(images).toHaveLength(2);

      // Contact us icon
      expect(images[0]).toHaveAttribute('src', 'contact-us-icon.svg');
      expect(images[0]).toHaveAttribute('alt', 'line');
      expect(images[0]).toHaveAttribute('width', '80');
      expect(images[0]).toHaveAttribute('height', '80');

      // Line icon
      expect(images[1]).toHaveAttribute('src', 'line-icon.svg');
      expect(images[1]).toHaveAttribute('alt', 'line');
      expect(images[1]).toHaveAttribute('width', '24');
      expect(images[1]).toHaveAttribute('height', '24');
    });
  });

  describe('Background Image', () => {
    it('renders background image with correct path', () => {
      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      // Find the element with background image
      const backgroundElement = screen.getByText('How can we help you?').closest('div');
      expect(backgroundElement).toHaveStyle({
        backgroundImage: "url('/test-base-path/assets/images/contact-bg.png')",
      });
    });
  });

  describe('MUI Icons', () => {
    it('renders MUI icons correctly', () => {
      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      // Check for FileCopyOutlined icon in the button
      const copyButton = screen.getByRole('button', { name: /copy email template/i });
      expect(copyButton.querySelector('svg')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('handles successful email template copy to clipboard', async () => {
      mockWriteText.mockResolvedValue(undefined);

      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      const copyButton = screen.getByRole('button', { name: /copy email template/i });
      fireEvent.click(copyButton);

      const expectedTemplate = [
        'Your name and contact information',
        'Detailed description of the issue',
        'Screenshots or attachments if applicable'
      ].join('\n');

      await waitFor(() => {
        expect(mockWriteText).toHaveBeenCalledWith(expectedTemplate);
        expect(mockToastMessages.info).toHaveBeenCalledWith('Email template copied to clipboard');
      });
    });

    it('handles failed email template copy to clipboard', async () => {
      mockWriteText.mockRejectedValue(new Error('Clipboard access denied'));

      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      const copyButton = screen.getByRole('button', { name: /copy email template/i });
      fireEvent.click(copyButton);

      const expectedTemplate = [
        'Your name and contact information',
        'Detailed description of the issue',
        'Screenshots or attachments if applicable'
      ].join('\n');

      await waitFor(() => {
        expect(mockWriteText).toHaveBeenCalledWith(expectedTemplate);
        expect(mockToastMessages.error).toHaveBeenCalledWith('Failed to copy email template');
      });
    });

    it('handles copy button click with fireEvent', async () => {
      mockWriteText.mockResolvedValue(undefined);

      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      const copyButton = screen.getByRole('button', { name: /copy email template/i });
      fireEvent.click(copyButton);

      const expectedTemplate = [
        'Your name and contact information',
        'Detailed description of the issue',
        'Screenshots or attachments if applicable'
      ].join('\n');

      await waitFor(() => {
        expect(mockWriteText).toHaveBeenCalledWith(expectedTemplate);
      });
    });
  });

  describe('Translation Integration', () => {
    it('uses correct translation namespace', () => {
      const { useTranslations } = require('next-intl');

      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      expect(useTranslations).toHaveBeenCalledWith('contact-us');
    });

    it('handles missing translations gracefully', () => {
      mockContactT.mockReturnValue('missing-translation');

      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      expect(screen.getAllByText('missing-translation').length).toBeGreaterThan(0);
    });

    it('renders with custom translation values', () => {
      mockContactT.mockImplementation((key: string) => {
        const customTranslations: Record<string, string> = {
          'title': 'Custom Contact Title',
          'help-title': 'Custom Help Title',
          'copy-button': 'Custom Copy Button',
        };
        return customTranslations[key] || `custom-${key}`;
      });

      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      expect(screen.getByText('Custom Contact Title')).toBeInTheDocument();
      expect(screen.getByText('Custom Help Title')).toBeInTheDocument();
      expect(screen.getByText('Custom Copy Button')).toBeInTheDocument();
    });
  });

  describe('Toast Messages Integration', () => {
    it('uses toastMessages utility correctly', () => {
      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      // Component should render without errors, indicating toastMessages is properly imported
      expect(screen.getByRole('button', { name: /copy email template/i })).toBeInTheDocument();
    });

    it('handles toast messages with different variants', async () => {
      // Test success case first
      mockWriteText.mockResolvedValue(undefined);

      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      const copyButton = screen.getByRole('button', { name: /copy email template/i });
      fireEvent.click(copyButton);

      await waitFor(() => {
        expect(mockToastMessages.info).toHaveBeenCalledWith('Email template copied to clipboard');
      });

      // Reset mocks and test error case
      jest.clearAllMocks();
      mockWriteText.mockRejectedValue(new Error('Test error'));

      fireEvent.click(copyButton);

      await waitFor(() => {
        expect(mockToastMessages.error).toHaveBeenCalledWith('Failed to copy email template');
      });
    });
  });

  describe('Component Structure and Styling', () => {
    it('renders with correct semantic HTML structure', () => {
      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      // Check for semantic HTML elements
      const article = screen.getByRole('article');
      expect(article).toBeInTheDocument();

      const heading = screen.getByRole('heading', { level: 1 });
      expect(heading).toHaveTextContent('Contact Us');

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('renders with correct MUI component structure', () => {
      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      // Check for MUI Paper component (should render as div with elevation)
      const paperElement = screen.getByText('How can we help you?').closest('div[class*="MuiPaper"]');
      expect(paperElement).toBeInTheDocument();
    });

    it('renders list items correctly', () => {
      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      // Check for list structure in email template
      const listItems = screen.getAllByRole('listitem');
      expect(listItems).toHaveLength(3);
      expect(listItems[0]).toHaveTextContent('Your name and contact information');
      expect(listItems[1]).toHaveTextContent('Detailed description of the issue');
      expect(listItems[2]).toHaveTextContent('Screenshots or attachments if applicable');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('handles clipboard API error', async () => {
      // Mock writeText to throw an error
      mockWriteText.mockRejectedValue(new Error('Clipboard API error'));

      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      const copyButton = screen.getByRole('button', { name: /copy email template/i });
      fireEvent.click(copyButton);

      await waitFor(() => {
        expect(mockToastMessages.error).toHaveBeenCalledWith('Failed to copy email template');
      });
    });

    it('handles empty translation returns', () => {
      mockContactT.mockReturnValue('');

      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      // Component should still render without crashing
      const copyButton = screen.getByRole('button');
      expect(copyButton).toBeInTheDocument();
    });

    it('handles null translation returns', () => {
      mockContactT.mockReturnValue(null);

      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      // Component should still render without crashing
      const copyButton = screen.getByRole('button');
      expect(copyButton).toBeInTheDocument();
    });

    it('handles multiple rapid clicks on copy button', async () => {
      mockWriteText.mockResolvedValue(undefined);

      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      const copyButton = screen.getByRole('button', { name: /copy email template/i });

      // Click multiple times rapidly
      fireEvent.click(copyButton);
      fireEvent.click(copyButton);
      fireEvent.click(copyButton);

      const expectedTemplate = [
        'Your name and contact information',
        'Detailed description of the issue',
        'Screenshots or attachments if applicable'
      ].join('\n');

      await waitFor(() => {
        expect(mockWriteText).toHaveBeenCalledTimes(3);
        expect(mockWriteText).toHaveBeenCalledWith(expectedTemplate);
        expect(mockToastMessages.info).toHaveBeenCalledTimes(3);
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper button accessibility', () => {
      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      const copyButton = screen.getByRole('button', { name: /copy email template/i });
      expect(copyButton).toBeEnabled();
      expect(copyButton).toHaveAttribute('type', 'button');
    });

    it('has proper heading hierarchy', () => {
      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      const mainHeading = screen.getByRole('heading', { level: 1 });
      expect(mainHeading).toHaveTextContent('Contact Us');
    });

    it('has proper image alt text', () => {
      render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      const images = screen.getAllByTestId('mock-image');
      images.forEach(image => {
        expect(image).toHaveAttribute('alt');
      });
    });
  });

  describe('Performance and Optimization', () => {
    it('renders without unnecessary re-renders', () => {
      const { rerender } = render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      // Re-render with same props
      rerender(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      // Component should render successfully
      expect(screen.getByText('Contact Us')).toBeInTheDocument();
    });

    it('handles component unmounting gracefully', () => {
      const { unmount } = render(
        <TestWrapper>
          <ContactUs />
        </TestWrapper>
      );

      expect(() => unmount()).not.toThrow();
    });
  });
});
