import dayjs, { Dayjs } from 'dayjs';
import { colors } from 'styles/colors';
import { GroupedSelectedDurian, DurianVariety, EventStatusEnum } from 'types';

import {
  ACCEPTED_FILE_TYPES,
  ACCEPTED_IMAGE_TYPES,
  MAX_FILE_SIZE,
  PLOT_GAP_NUMBER_LENGTH,
  PLOT_ID_LENGTH,
} from 'constant/common';
import { AppConfig } from 'configs/app-config';
import parsePhoneNumberFromString from 'libphonenumber-js';

export const getStatus = (productType: string, productStatus: string) => {
  if (productType === 'receiving') {
    return EventStatusEnum.RECEIVED;
  } else if (productType === 'harvesting') {
    if (productStatus === 'published') return EventStatusEnum.WAITING;
    if (productStatus === 'draft') return EventStatusEnum.DRAFT;
    if (productStatus === 'consumed') return EventStatusEnum.RECEIVED;
    if (productStatus === 'sealed') return EventStatusEnum.SEALED;
    if (productStatus === 'rejected') return EventStatusEnum.REJECTED;
  } else if (productType === 'shipment') {
    if (productStatus === 'published') return EventStatusEnum.WAITING;
    if (productStatus === 'draft') return EventStatusEnum.DRAFT;
    if (productStatus === 'consumed') return EventStatusEnum.RECEIVED;
    if (productStatus === 'sealed') return EventStatusEnum.SEALED;
    if (productStatus === 'rejected') return EventStatusEnum.REJECTED;
  } else if (productType === 'qr') {
    if (productStatus === 'available') return EventStatusEnum.AVAILABLE;
    if (productStatus === 'assigned') return EventStatusEnum.ASSIGNED;
  }

  return 'N/A';
};

export const getStatusColor = (status: EventStatusEnum) => {
  if (status === EventStatusEnum.WAITING) return colors.warning700;
  if (status === EventStatusEnum.REJECTED) return colors.reject;
  if (status === EventStatusEnum.DRAFT) return colors.neutral700;
  if (status === EventStatusEnum.AVAILABLE) return colors.neutral700;
  return colors.success700;
};

export const getStatusBgColor = (status: EventStatusEnum) => {
  if (status === EventStatusEnum.WAITING) return colors.warning100;
  if (status === EventStatusEnum.REJECTED) return colors.rejectBg;
  if (status === EventStatusEnum.DRAFT) return colors.neutral100;
  if (status === EventStatusEnum.AVAILABLE) return colors.neutral100;
  return colors.success100;
};

export const getStatusBorderColor = (status: EventStatusEnum) => {
  if (status === EventStatusEnum.WAITING) return colors.warning400;
  if (status === EventStatusEnum.REJECTED) return colors.reject;
  if (status === EventStatusEnum.DRAFT) return colors.neutralBorder;
  if (status === EventStatusEnum.AVAILABLE) return colors.neutralBorder;
  return colors.success400;
};

export const getTotalWeight = (durianData: GroupedSelectedDurian[]) => {
  return durianData.reduce((total, item) => {
    const itemWeight = item.grades.reduce((gradeTotal, grade) => {
      return gradeTotal + Number(grade.weight);
    }, 0);

    return total + itemWeight;
  }, 0);
};

export function transformDurianData(rawData: DurianVariety[]): GroupedSelectedDurian[] {
  if (!rawData || rawData.length === 0) return [];
  const varietyMap = new Map<string, GroupedSelectedDurian>();

  rawData.forEach((item) => {
    const varietyId = item.id;

    if (!varietyId) return;

    if (!varietyMap.has(varietyId)) {
      varietyMap.set(varietyId, {
        id: varietyId,
        grades: item.grades,
        flowerBloomingDay: item.flowerBloomingDay as number,
      });
    }

    const varietyEntry = varietyMap.get(varietyId)!;

    if (!varietyEntry) return;

    varietyEntry.grades.push({
      id: item?.id || '',
      weight: item.grades.reduce((prev, current) => {
        return (prev += current?.weight ?? 0);
      }, 0),
    });
  });

  return Array.from(varietyMap.values());
}

export const removeImgSuffix = (img?: string) => {
  if (!img) return '';

  return img.split('.')[0];
};

export const formatDate = (value?: number | string | Dayjs, format?: string) => {
  if (!value) return '';
  return `${dayjs(value).format(format ?? 'DD/MM/YYYY')}`;
};

export const isValidThaiNumber = (value?: string) => {
  const trimValue = value?.trim();

  if (!trimValue) return false;

  const phone = parsePhoneNumberFromString(trimValue, 'TH');

  if (!phone || !phone.isValid()) {
    return false;
  }

  return true;
};

export const formatThaiPhone = (value: string) => {
  const trimValue = value?.trim();

  if (!trimValue) return '';
  const phone = parsePhoneNumberFromString(trimValue, 'TH');

  if (!phone || !phone.isValid()) {
    return '';
  }

  const nationalNumber = phone.nationalNumber;

  return nationalNumber;
};

export const formatNumberWithCommas = (value?: number | string) => {
  if (value === 0) return '0';
  if (!value) return '';
  return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

export const delayPromise = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

/**
 * Format seconds into a countdown timer string (HH:MM:SS)
 * @param totalSeconds - Total time in seconds
 * @returns Formatted string in HH:MM:SS format
 */
export const formatCountdownTimer = (totalSeconds: number): string => {
  if (totalSeconds < 0) return '00:00:00';

  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.floor(totalSeconds % 60);

  const formattedHours = hours.toString().padStart(2, '0');
  const formattedMinutes = minutes.toString().padStart(2, '0');
  const formattedSeconds = seconds.toString().padStart(2, '0');

  return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
};

/**
 * Format seconds into MM:SS if under an hour, or HH:MM:SS if one hour or more
 * @param totalSeconds - Total time in seconds
 * @returns Formatted string in appropriate format
 */
export const formatCountdownTimerAuto = (totalSeconds: number): string => {
  if (totalSeconds < 0) return '00:00';

  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.floor(totalSeconds % 60);

  const formattedMinutes = minutes.toString().padStart(2, '0');
  const formattedSeconds = seconds.toString().padStart(2, '0');

  if (hours > 0) {
    const formattedHours = hours.toString().padStart(2, '0');
    return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
  }

  return `${formattedMinutes}:${formattedSeconds}`;
};

export function bytesToMB(bytes: number, decimals = 2): number {
  if (bytes === 0) return 0;
  const mb = bytes / (1024 * 1024);
  return parseFloat(mb.toFixed(decimals));
}

export function removeEmptyFields<T extends object>(obj: T): Partial<T> {
  const result: Partial<T> = {};

  for (const key in obj) {
    const value = obj[key];
    if (value !== undefined && value !== null && value?.toString().trim() !== '') {
      result[key] = value;
    }
  }

  return result as Partial<T>;
}

export const clearAllBrowserStorage = async () => {
  const consentValue = localStorage.getItem('isAcceptConsent');

  localStorage.clear();

  if (consentValue !== null) {
    localStorage.setItem('isAcceptConsent', consentValue);
  }

  sessionStorage.clear();

  if ('caches' in window) {
    const cacheNames = await caches.keys();
    await Promise.all(cacheNames.map((name) => caches.delete(name)));
  }
};

export function safeJSONParse<T>(
  input: unknown,
  reviver?: (this: unknown, key: string, value: unknown) => unknown
): T | undefined {
  if (typeof input !== 'string') {
    return undefined;
  }

  try {
    return JSON.parse(input, reviver as (this: unknown, key: string, value: unknown) => unknown) as T;
  } catch {
    return undefined;
  }
}

export function validateFile(file: File) {
  let isValid = true;
  let wrongFileType = false;
  let wrongFileSize = false;

  if (!ACCEPTED_FILE_TYPES.includes(file.type)) {
    isValid = false;
    wrongFileType = true;
  }

  if (file.size > MAX_FILE_SIZE) {
    isValid = false;
    wrongFileSize = true;
  }

  return {
    isValid,
    wrongFileType,
    wrongFileSize,
  };
}

export function validateFileImage(file: File) {
  let isValid = true;
  let wrongFileType = false;
  let wrongFileSize = false;

  if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
    isValid = false;
    wrongFileType = true;
  }

  if (file.size > MAX_FILE_SIZE) {
    isValid = false;
    wrongFileSize = true;
  }

  return {
    isValid,
    wrongFileType,
    wrongFileSize,
  };
}

export const getImageUrl = (imgUrl?: string | null) => {
  if (!imgUrl) return null;

  const newImageUrl = `${AppConfig.ASSET_DOMAIN_URL}/${imgUrl}`;

  return newImageUrl;
};

export function isValidNumberInput(rawValue: string): boolean {
  return Boolean(/^[\d.]*$/.test(rawValue));
}

/**
 * Formats a raw plot ID to the pattern: xxxxxxxx-xxxx-xxxx-xxxx
 * @param {string|number} rawPlotId - Raw plot ID (all numbers)
 * @returns {string} Formatted plot ID
 */
export function formatPlotId(rawPlotId: string | number): string {
  const digits = rawPlotId.toString().replaceAll('-', '');

  if (digits.length !== PLOT_ID_LENGTH) {
    return digits;
  }

  return `${digits.slice(0, 8)}-${digits.slice(8, 12)}-${digits.slice(12, 16)}-${digits.slice(16, 20)}`;
}

/**
 * Formats a raw gap number to the pattern: xx-xxxx-xx-xxx-xxxxxx
 * @param {string|number} rawGap - Raw gap number (all numbers)
 * @returns {string} Formatted gap number
 */
export function formatGap(rawGap: string | number): string {
  const input = rawGap.toString().replaceAll('-', '').trim();
  const digits = input.includes(' ') ? input.split(' ')[1] : input;

  if (!digits || digits.length !== PLOT_GAP_NUMBER_LENGTH) {
    return digits;
  }

  return `${digits.slice(0, 2)}-${digits.slice(2, 6)}-${digits.slice(6, 8)}-${digits.slice(8, 11)}-${digits.slice(11, 17)}`;
}

// Allow all characters
export const REGEX_INPUT_NAME_FIELD = /^.+$/;
