services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        VERSION: ${VERSION:-dev-local}
        COMMIT_SHA: ${COMMIT_SHA:-unknown}
    image: dt-packaging-house-web:latest
    container_name: dt-packaging-house-web
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=development
      - PORT=3000
      - NEXT_PUBLIC_WEB_VERSION=${VERSION:-dev-local}
      - NEXT_PUBLIC_COMMIT_SHA=${COMMIT_SHA:-unknown}
    env_file:
      - .env
    restart: unless-stopped
    networks:
      - app-network
networks:
  app-network:
    driver: bridge

volumes:
  redis-data:
    driver: local
