import { SvgIcon, SvgIconProps } from '@mui/material';
import { FC } from 'react';

export const QrSolidIcon: FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon {...props}>
      <svg width={props.width} height={props.height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M15.001 1.11111C17.228 1.11111 19.0331 2.91638 19.0332 5.14334V15.0008C19.0332 17.2279 17.2281 19.033 15.001 19.033H5.14355C2.91659 19.0328 1.11133 17.2278 1.11133 15.0008V5.14334C1.11148 2.91647 2.91668 1.11126 5.14355 1.11111H15.001ZM5.90234 11.1111C4.95287 11.1113 4.44445 11.6112 4.44434 12.5691V14.2361C4.44447 15.194 4.95289 15.6939 5.90234 15.6941H7.56934C8.51921 15.6941 9.0272 15.1942 9.02734 14.2361V12.5691C9.02723 11.611 8.51923 11.1111 7.56934 11.1111H5.90234ZM12.5693 11.1111C11.6197 11.1112 11.1114 11.6111 11.1113 12.5691V14.2361C11.1115 15.1941 11.6197 15.694 12.5693 15.6941H14.2363C15.186 15.694 15.6942 15.1941 15.6943 14.2361V12.5691C15.6942 11.6111 15.186 11.1112 14.2363 11.1111H12.5693ZM5.90234 4.44412C4.95298 4.44435 4.44457 4.94444 4.44434 5.90213V7.56912C4.44434 8.52724 4.95276 9.0269 5.90234 9.02713H7.56934C8.51934 9.02713 9.02734 8.52746 9.02734 7.56912V5.90213C9.02711 4.94422 8.51912 4.44412 7.56934 4.44412H5.90234ZM12.5693 4.44412C11.6198 4.44424 11.1116 4.94433 11.1113 5.90213V7.56912C11.1113 8.52735 11.6195 9.02702 12.5693 9.02713H14.2363C15.1861 9.02702 15.6943 8.52735 15.6943 7.56912V5.90213C15.6941 4.94433 15.1859 4.44424 14.2363 4.44412H12.5693Z"
          fill="#2266D9"
        />
      </svg>
    </SvgIcon>
  );
};
