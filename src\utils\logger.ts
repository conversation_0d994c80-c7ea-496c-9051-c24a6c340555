/* eslint-disable no-console */
type LogLevel = 'info' | 'warn' | 'error' | 'debug';

type LogMeta = object | string;

// Color codes for different log levels
const colors = {
  info: '\x1b[36m',    // Cyan
  warn: '\x1b[33m',    // Yellow
  error: '\x1b[31m',   // Red
  debug: '\x1b[35m',   // Magenta
  reset: '\x1b[0m',    // Reset
};

const log = (level: LogLevel, message: string, meta: LogMeta = {}) => {
  const logEntry = {
    timestamp: new Date().toISOString(),
    level,
    message,
    meta,
  };

  const coloredOutput = `${colors[level]}${JSON.stringify(logEntry)}${colors.reset}`;

  // Structured JSON log
  switch (level) {
    case 'info':
      console.info(coloredOutput);
      break;
    case 'warn':
      console.warn(coloredOutput);
      break;
    case 'error':
      console.error(coloredOutput);
      break;
    case 'debug':
      console.debug(coloredOutput);
      break;
    default:
      console.log(coloredOutput);
      break;
  }
};

// Exported functions for each level
export const logger = {
  info: (msg: string, meta?: LogMeta) => log('info', msg, meta),
  warn: (msg: string, meta?: LogMeta) => log('warn', msg, meta),
  error: (msg: string, meta?: LogMeta) => log('error', msg, meta),
  debug: (msg: string, meta?: LogMeta) => log('debug', msg, meta),
};
