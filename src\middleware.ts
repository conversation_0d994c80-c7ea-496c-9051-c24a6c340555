import { basePathProd } from 'configs/app-config';
import { NextRequest, NextResponse } from 'next/server';
import { authRoutes, clientRoutes, protectedPaths } from 'routes/client-routes';

export function middleware(req: NextRequest) {
  const pathname = req.nextUrl.pathname;

  const token = req.cookies.get('access_token_pkg_house')?.value;

  const isAuthenticated = !!token;

  // check redirect with login
  if (isAuthenticated && pathname === authRoutes.login) {
    const redirectUrl = new URL(`${basePathProd}${clientRoutes.home}`, req.url);
    return NextResponse.redirect(redirectUrl);
  }

  const isProtected = protectedPaths.some((protectPath) => {
    if (protectPath.exact) {
      return protectPath.path === pathname;
    }

    return pathname.startsWith(protectPath.path);
  });

  if (isProtected && !isAuthenticated) {
    return NextResponse.next();
  }

  return NextResponse.next();
}
