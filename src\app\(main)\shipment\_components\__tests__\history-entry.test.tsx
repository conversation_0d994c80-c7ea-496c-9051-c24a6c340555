import { render, screen } from '@testing-library/react';
import { HistoryEntry } from '../history-entry';
import { ShipmentHistory } from 'types/shipment-history';

// Mock the PackagingDurianBox component
jest.mock('../packaging-durian-box', () => ({
  PackagingDurianBox: ({ brandName, variety, grade, typeOfBox, netWeight, totalBox }: {
    brandName: string;
    variety: string;
    grade: string;
    typeOfBox: string;
    netWeight: string;
    totalBox: string;
  }) => (
    <div data-testid="packaging-durian-box">
      <div>{String(brandName || '')}</div>
      <div>{String(variety || '')}</div>
      <div>{String(grade || '')}</div>
      <div>{String(typeOfBox || '')}</div>
      <div>{String(netWeight || '')}</div>
      <div>{String(totalBox || '')}</div>
    </div>
  ),
}));

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: jest.fn(() => (key: string) => {
    const translations: Record<string, string> = {
      'history-action-created': 'created',
      'history-action-updated': 'updated',
      'history-field-shipment-name': 'Shipment name',
    };
    return translations[key] || key;
  }),
}));

// Mock dayjs
jest.mock('dayjs', () => {
  const mockDayjs = jest.fn(() => ({
    format: jest.fn(() => '15/01/2025 14:32'),
  }));
  // Add extend method to the mock
  Object.assign(mockDayjs, { extend: jest.fn() });
  return mockDayjs;
});

// Mock formatDateTimeWithLocale and formatDateWithLocale functions
jest.mock('containers/event/_components', () => ({
  formatDateTimeWithLocale: jest.fn(() => '15/01/2025 14:32'),
  formatDateWithLocale: jest.fn(() => '15/01/2025'),
}));

describe('HistoryEntry', () => {
  const mockEntry: ShipmentHistory['entries'][0] = {
    id: '1',
    timestamp: '2025-01-15T14:32:00Z',
    user: {
      id: 'user1',
      name: 'Ayahidi Srumputa',
    },
    action: 'updated',
    changes: [
      {
        field: 'shipmentName',
        oldValue: '5000 kg durian to Shanghai China',
        newValue: '4800 kg durian to China',
        changeType: 'updated',
      },
    ],
  };

  it('renders user avatar with initials', () => {
    render(<HistoryEntry entry={mockEntry} />);

    // Avatar should show first letters of name (AS for Ayahidi Srumputa)
    const avatar = screen.getByText('AS');
    expect(avatar).toBeInTheDocument();
  });

  it('renders user name and action', () => {
    render(<HistoryEntry entry={mockEntry} />);

    expect(screen.getByText('Ayahidi Srumputa')).toBeInTheDocument();
    expect(screen.getByText('updated')).toBeInTheDocument();
  });

  it('renders description when provided', () => {
    render(<HistoryEntry entry={mockEntry} />);

    expect(screen.getByText('Shipment name')).toBeInTheDocument();
  });

  it('renders formatted timestamp', () => {
    render(<HistoryEntry entry={mockEntry} />);

    expect(screen.getByText('15/01/2025 14:32')).toBeInTheDocument();
  });

  it('renders change items with arrow for updates', () => {
    render(<HistoryEntry entry={mockEntry} />);

    expect(screen.getByText('5000 kg durian to Shanghai China')).toBeInTheDocument();
    expect(screen.getByText('→')).toBeInTheDocument();
    expect(screen.getByText('4800 kg durian to China')).toBeInTheDocument();
  });



  it('renders packaging information when provided', () => {
    const entryWithPackaging: ShipmentHistory['entries'][0] = {
      ...mockEntry,
      changes: [
        {
          field: 'packagingInformation',
          changeType: 'added',
          oldValue: null,
          newValue: {
            id: 'pkg-test',
            brandNameInfo: {
              id: 'brand-test',
              label: { en: 'King Durian Brand', th: 'แบรนด์ทุเรียนคิง' },
            },
            productTypeInfo: {
              id: 'type-test',
              label: { en: 'Fresh Durian', th: 'ทุเรียนสด' },
            },
            varietyGradeJoinId: 1,
            weightKg: 9,
            quantity: '40',
            numberOfBoxes: 40,
            sealNumber: 'SEAL-TEST',
            varietyGrade: {
              gradeValue: 'AB',
              gradeDisplayText: 'Grade AB',
              varietyValue: 'king-durian',
              varietyDisplayText: 'King Durian',
            },
            varieties: [
              {
                id: 'var-test',
                value: 'monthong',
                label: { en: 'Monthong', th: 'หมอนทอง' },
                flowerBloomingDay: 1705276800,
                grades: [
                  {
                    id: 'grade-ab',
                    value: 'AB',
                    label: { en: 'Grade AB', th: 'เกรด AB' },
                    weight: 9,
                  },
                ],
                name: 'Monthong',
              },
            ],
            batchlot: 'BATCH-TEST',
            varietyName: 'King Durian',
            packingDate: 1705276800000,
          },
        },
      ],
    };

    render(<HistoryEntry entry={entryWithPackaging} />);

    expect(screen.getByTestId('packaging-durian-box')).toBeInTheDocument();
  });

  it('handles entry without changes', () => {
    const entryWithoutChanges: ShipmentHistory['entries'][0] = {
      ...mockEntry,
      changes: undefined,
    };

    render(<HistoryEntry entry={entryWithoutChanges} />);

    expect(screen.getByText('Ayahidi Srumputa')).toBeInTheDocument();
    expect(screen.getByText('updated')).toBeInTheDocument();
  });

  it('handles entry without description', () => {
    const entryWithoutDescription: ShipmentHistory['entries'][0] = {
      ...mockEntry,
    };

    render(<HistoryEntry entry={entryWithoutDescription} />);

    expect(screen.getByText('Ayahidi Srumputa')).toBeInTheDocument();
    expect(screen.getByText('updated')).toBeInTheDocument();
  });

  it('handles user with single name for initials', () => {
    const entryWithSingleName: ShipmentHistory['entries'][0] = {
      ...mockEntry,
      user: {
        id: 'user1',
        name: 'John',
      },
    };

    render(<HistoryEntry entry={entryWithSingleName} />);

    // Should show first 2 characters of single name (JO)
    const avatar = screen.getByText('JO');
    expect(avatar).toBeInTheDocument();
  });

  it('renders shipment name for non-updated actions', () => {
    const entryWithCreatedAction: ShipmentHistory['entries'][0] = {
      ...mockEntry,
      action: 'created',
      changes: [
        {
          field: 'shipmentName',
          oldValue: null,
          newValue: 'Test Shipment',
          changeType: 'created',
        },
      ],
    };

    render(<HistoryEntry entry={entryWithCreatedAction} />);

    // Check that shipment name appears in the header (for non-updated actions)
    const shipmentNameElements = screen.getAllByText('Test Shipment');
    expect(shipmentNameElements.length).toBeGreaterThan(0);
    expect(screen.getByText('created')).toBeInTheDocument();
  });

  it('renders shipment name for sealed actions', () => {
    const entryWithSealedAction: ShipmentHistory['entries'][0] = {
      ...mockEntry,
      action: 'sealed',
      changes: [
        {
          field: 'shipmentName',
          oldValue: null,
          newValue: 'Sealed Shipment',
          changeType: 'sealed',
        },
      ],
    };

    render(<HistoryEntry entry={entryWithSealedAction} />);

    // Check that shipment name appears in the header (for non-updated actions)
    expect(screen.getByText('Sealed Shipment')).toBeInTheDocument();
    expect(screen.getByText('history-action-sealed')).toBeInTheDocument();
  });

  it('does not render changes for created actions', () => {
    const entryWithCreatedAction: ShipmentHistory['entries'][0] = {
      ...mockEntry,
      action: 'created',
      changes: [
        {
          field: 'shipmentName',
          oldValue: null,
          newValue: 'Test Shipment',
          changeType: 'created',
        },
      ],
    };

    render(<HistoryEntry entry={entryWithCreatedAction} />);

    // Should show shipment name in header but not render changes section
    expect(screen.getByText('Test Shipment')).toBeInTheDocument();
    expect(screen.getByText('created')).toBeInTheDocument();

    // Should not render the "Shipment name" field label from changes
    expect(screen.queryByText('Shipment name')).not.toBeInTheDocument();
  });

  it('does not render changes for sealed actions', () => {
    const entryWithSealedAction: ShipmentHistory['entries'][0] = {
      ...mockEntry,
      action: 'sealed',
      changes: [
        {
          field: 'shipmentName',
          oldValue: null,
          newValue: 'Sealed Shipment',
          changeType: 'sealed',
        },
      ],
    };

    render(<HistoryEntry entry={entryWithSealedAction} />);

    // Should show shipment name in header but not render changes section
    expect(screen.getByText('Sealed Shipment')).toBeInTheDocument();
    expect(screen.getByText('history-action-sealed')).toBeInTheDocument();

    // Should not render the "Shipment name" field label from changes
    expect(screen.queryByText('Shipment name')).not.toBeInTheDocument();
  });

  it('handles shipment photos changes', () => {
    const entryWithPhotos: ShipmentHistory['entries'][0] = {
      ...mockEntry,
      changes: [
        {
          field: 'shipmentPhotos',
          oldValue: 'photo1.jpg',
          newValue: null,
          changeType: 'removed',
        },
      ],
    };

    render(<HistoryEntry entry={entryWithPhotos} />);

    expect(screen.getByText('Ayahidi Srumputa')).toBeInTheDocument();
  });

  it('handles additional documents changes', () => {
    const entryWithDocuments: ShipmentHistory['entries'][0] = {
      ...mockEntry,
      changes: [
        {
          field: 'additionalDocuments',
          oldValue: null,
          newValue: 'document1.pdf',
          changeType: 'added',
        },
      ],
    };

    render(<HistoryEntry entry={entryWithDocuments} />);

    expect(screen.getByText('Ayahidi Srumputa')).toBeInTheDocument();
  });
});
