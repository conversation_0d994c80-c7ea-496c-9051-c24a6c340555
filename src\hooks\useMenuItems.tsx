import { useTranslations } from 'next-intl';
import { JSX } from 'react';
import { clientRoutes } from 'routes/client-routes';
import {
  DashboardIcon,
  ReceiveIcon,
  TruckIcon,
  DashboardSolidIcon,
  ReceiveSolidIcon,
  TruckSolidIcon,
  SettingIcon,
  SettingSolidIcon,
  QrIcon,
  QrSolidIcon,
} from 'assets/react-icons';
import { useFeatureFlag } from './useFeatureFlag';

export type MenuItem = {
  text: string;
  icon?: JSX.Element;
  path?: string;
  hide?: boolean;
  children?: MenuItem[];
  isChildren?: boolean;
  onClick?: () => void;
  exact?: boolean;
  hasChildren?: boolean;
  itemKey: string;
  parentKey?: string;
  solidIcon?: JSX.Element;
  hidden?: boolean;
};

export const iconSize = 30;

interface SearchParams {
  page: number;
  pageSize: number;
  sortBy: string;
  order?: 'desc' | 'asc';
}

export const generateDefaultSearchParams = (
  searchParams: SearchParams = { page: 0, pageSize: 10, sortBy: 'dateCreated', order: 'desc' }
) => {
  const params = new URLSearchParams();
  Object.entries(searchParams).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      params.append(key, String(value));
    }
  });

  return params.toString();
};

export const useMenuItems = () => {
  const commonT = useTranslations('common');
  const { ENABLED_QR_MANAGEMENT } = useFeatureFlag();

  const menuItemsTop: MenuItem[] = [
    {
      text: commonT('dashboard'),
      icon: <DashboardIcon sx={{ fontSize: `${iconSize}px` }} />,
      path: clientRoutes.home,
      exact: true,
      itemKey: 'dashboard',
      solidIcon: <DashboardSolidIcon sx={{ fontSize: `${iconSize}px` }} color="primary" />,
    },
    {
      text: commonT('durian-procurement'),
      icon: <ReceiveIcon sx={{ fontSize: `${iconSize}px` }} />,
      path: clientRoutes.event,
      hasChildren: true,
      itemKey: 'durian-procurement',
      solidIcon: <ReceiveSolidIcon sx={{ fontSize: `${iconSize}px` }} color="primary" />,
      children: [
        {
          text: commonT('incoming-batch/lot'),
          path: `${clientRoutes.eventIncoming}?${generateDefaultSearchParams()}`,
          isChildren: true,
          itemKey: 'incoming-batch/lot',
          parentKey: 'durian-procurement',
        },
        {
          text: commonT('receiving-batch/lot'),
          path: `${clientRoutes.eventReceiving}?${generateDefaultSearchParams()}`,
          isChildren: true,
          itemKey: 'receiving-batch/lot',
          parentKey: 'durian-procurement',
        },
      ],
    },
    {
      text: commonT('shipment'),
      icon: <TruckIcon sx={{ fontSize: `${iconSize}px` }} />,
      path: `${clientRoutes.shipment}?${generateDefaultSearchParams()}`,
      itemKey: 'shipment',
      solidIcon: <TruckSolidIcon sx={{ fontSize: `${iconSize}px` }} color="primary" />,
    },
    {
      text: commonT('qr-management'),
      icon: <QrIcon sx={{ fontSize: `${iconSize}px` }} />,
      path: `${clientRoutes.qr}`,
      itemKey: 'qr',
      solidIcon: <QrSolidIcon sx={{ fontSize: `${iconSize}px` }} color="primary" />,
      hidden: !ENABLED_QR_MANAGEMENT,
    },
    {
      text: commonT('settings'),
      icon: <SettingIcon sx={{ fontSize: `${iconSize}px` }} />,
      path: clientRoutes.setting,
      itemKey: 'settings',
      hasChildren: true,
      solidIcon: <SettingSolidIcon sx={{ fontSize: `${iconSize}px` }} color="primary" />,
      children: [
        {
          text: commonT('packing-house-information'),
          itemKey: 'setting-packing-house-information',
          parentKey: 'settings',
          path: clientRoutes.packingHouseInformation,
          isChildren: true,
        },
      ],
    },
  ];

  return { menuItemsTop };
};
