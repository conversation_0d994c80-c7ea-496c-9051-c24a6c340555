import { Box, Button, IconButton, Typography } from '@mui/material';
import { DetailRow, ImageReviewModal, PlotOptionBox } from 'components';
import { Farm, Plot } from 'types';
import { FC, useCallback } from 'react';
import { useTranslations } from 'next-intl';
import emptyStateIcon from 'assets/icons/empty-state.svg';
import Image from 'next/image';
import { theme } from 'styles/theme';
import { getImageUrl } from 'utils';
import UploadPlotImage from './upload-plot-image';
import { Add, ClearRounded, DeleteOutlineOutlined } from '@mui/icons-material';

interface FarmDetailProps {
  farm: Farm;
  plots: Plot[];
  addPlot: () => void;
  addImageToPlot: (id: string, image: string) => void;
  removeImageFromPlot: (id: string) => void;
  removePlot: (id: string) => void;
}

export const FarmDetail: FC<FarmDetailProps> = ({
  farm,
  plots,
  addPlot,
  addImageToPlot,
  removeImageFromPlot,
  removePlot,
}) => {
  const receivingTranslation = useTranslations('receive');

  const renderUploadImage = useCallback(
    (id: string) => {
      return (
        <UploadPlotImage
          onChange={(imageString: string) => {
            addImageToPlot(id, imageString);
          }}
        />
      );
    },
    [addImageToPlot]
  );

  const renderImageContent = useCallback((plot: Plot) => {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, position: 'relative' }}>
        <ImageReviewModal imageUrl={getImageUrl(plot.image) ?? null} imageSize={120} />
        <IconButton
          size="small"
          sx={{ position: 'absolute', right: 0, top: 0, p: 0 }}
          color="error"
          onClick={() => removeImageFromPlot(plot.id)}
        >
          <ClearRounded fontSize="inherit" color="inherit" />
        </IconButton>
      </Box>
    );
  }, [removeImageFromPlot]);

  const renderDeletePlotContent = useCallback(
    (id: string) => {
      return (
        <IconButton color="error" onClick={() => removePlot(id)}>
          <DeleteOutlineOutlined fontSize="inherit" color="inherit" />
        </IconButton>
      );
    },
    [removePlot]
  );

  const renderPlotData = useCallback(() => {
    if (plots.length) {
      return plots.map((plot) => {
        return (
          <Box key={plot.id} sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
            <PlotOptionBox
              key={plot.id}
              plotNumber={plot?.name ?? ''}
              plotId={plot?.plotId ?? ''}
              plotGap={plot?.gap ?? ''}
              plotArea={plot?.area ?? 0}
              plotAreaUnit={plot?.areaUnit ?? ''}
              renderActionButtons={() => (
                <Box sx={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                  {plot.image ? renderImageContent(plot) : renderUploadImage(plot.id)}
                  {renderDeletePlotContent(plot.id)}
                </Box>
              )}
            />
          </Box>
        );
      });
    }
    return (
      <>
        <Image src={emptyStateIcon} width={160} height={160} unoptimized alt="empty-state-icon" />
        <Typography my="12px" fontSize="16px" color="text.secondary">
          {receivingTranslation('no-plot-selected')}
        </Typography>
      </>
    );
  }, [plots, renderImageContent, renderUploadImage, renderDeletePlotContent, receivingTranslation]);

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <Box>
        <DetailRow
          title={receivingTranslation('farm-name')}
          content={farm.name ?? '--'}
          sx={{ flexDirection: 'row' }}
        />
        <DetailRow
          title={receivingTranslation('farm-address')}
          content={farm.address ?? '--'}
          sx={{ flexDirection: 'row' }}
        />
      </Box>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Typography>
          {receivingTranslation('farm-plot')} ({receivingTranslation('required')})
        </Typography>
        <Typography sx={{ fontWeight: 300, color: theme.palette.customColors.gray, fontSize: 14 }}>
          {receivingTranslation('farm-plot-img-support')}
        </Typography>
        <Box
          component="div"
          sx={{
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'column',
            mt: 3,
            gap: 3,
          }}
        >
          {renderPlotData()}
          <Button
            onClick={() => {
              addPlot();
            }}
            variant="outlined"
            sx={{
              minWidth: '140px',
              px: 3,
              py: 1,
              fontSize: '0.875rem',
              fontWeight: 500,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 1,
            }}
          >
            <Add />
            <Typography>{receivingTranslation('select-plot')}</Typography>
          </Button>
        </Box>
      </Box>
    </Box>
  );
};
