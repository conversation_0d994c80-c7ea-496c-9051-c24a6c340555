import React from 'react';
import { render } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { CustomDatePicker } from '../custom-date-picker';

// Mock the cookie utility to return English locale
jest.mock('utils/cookie-client', () => ({
  getCookieLocale: jest.fn(() => 'en'),
}));

jest.mock('constant/common', () => ({
  DD_MMMM_YYYY_WITH_DASH: 'DD-MMMM-YYYY',
}));

const theme = createTheme();

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider theme={theme}>
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      {children}
    </LocalizationProvider>
  </ThemeProvider>
);

describe('CustomDatePicker - English Locale', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render with English locale', () => {
    const testDate = dayjs('2023-12-25');
    const { container } = render(
      <TestWrapper>
        <CustomDatePicker
          value={testDate}
          onChange={mockOnChange}
          label="Test Date Picker"
        />
      </TestWrapper>
    );
    expect(container).toMatchSnapshot();
  });

  it('should render with English locale and no Thai year adjustment', () => {
    const testDate = dayjs('2023-12-25');
    const { container } = render(
      <TestWrapper>
        <CustomDatePicker
          value={testDate}
          onChange={mockOnChange}
          label="English Date Picker"
          error={false}
        />
      </TestWrapper>
    );
    expect(container).toMatchSnapshot();
  });
});
