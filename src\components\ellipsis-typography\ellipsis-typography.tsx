import { useEffect, useRef, useState } from 'react';
import { Tooltip, Typography } from '@mui/material';

export const EllipsisTypography = ({ text }: { text: string }) => {
  const textRef = useRef<HTMLSpanElement | null>(null);
  const [isEllipsed, setIsEllipsed] = useState(false);

  useEffect(() => {
    const el = textRef.current;
    if (el) {
      const isOverflowing = el.scrollWidth > el.clientWidth;
      setIsEllipsed(isOverflowing);
    }
  }, [text]);

  return (
    <Tooltip title={isEllipsed ? text : ''}>
      <Typography
        noWrap
        variant='body2'
        ref={textRef}
        fontSize='inherit'
        sx={{
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          width: '100%',
        }}
      >
        {text}
      </Typography>
    </Tooltip>
  );
};
