import { Dayjs } from 'dayjs';
import { EphytoExportDocument } from './ephyto';
import { TranslateLabel } from './durian';

export type BrandName = {
  id: string;
  label: TranslateLabel;
};

export type ProductType = {
  id: string;
  label: TranslateLabel;
};

export type GetMasterDataResponse = {
  brandNames: BrandName[];
  productTypes: ProductType[];
};

export type OcrSourceFrom = 'ocr' | 'ephyto';

export type Receipt = {
  numberOfBoxes: string | number; // numberOfBoxes
  destinationCountry: string; // destination country
  exportDate: number | null | Dayjs; // exportDate
  totalWeightKg: string | number; // totalWeightKg
  receiptNumber: string;
  transportationMode: string;
  id?: string;
  truckNumber?: string;
  trailerNumber?: string;
  orchardNo?: string;
  sourceFrom?: OcrSourceFrom;
  containerNumber?: string;
  trailerRegistrationNumber?: string;
  truckRegistrationNumber?: string;
  orchardRegisterNumber?: string;
  ephytoResponse?: EphytoExportDocument;
  borderCheckpointName?: string;
  nameOfExportingCompany?: string;
};

export type SuggestOcrResponse = {
  destinationCountry: string;
  exportDate: number;
  numberOfBoxes: number;
  receiptNumber: string;
  totalWeightKg: number;
  transportationMode: string;
  borderCheckpointName?: string;
  nameOfExportingCompany?: string;
};

export type OcrResponse = {
  suggest: SuggestOcrResponse;
  source: OcrSourceFrom;
};

export type AcceptFileTypes = 'application/pdf' | 'image/png' | 'image/jpeg';
