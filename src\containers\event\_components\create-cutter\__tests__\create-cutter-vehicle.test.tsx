/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CreateCutterVehicle } from '../create-cutter-vehicle';
import { CutterVehicle, Province } from 'types';
import { useQuery } from '@tanstack/react-query';

// Mock the UploadImage component
jest.mock('../upload-images', () => {
  const MockUploadImage = ({ onChange }: { onChange: (imgString: string) => void }) => {
    return (
      <div data-testid="upload-image">
        <button
          data-testid="upload-image-button"
          onClick={() => onChange('test-image-filename.jpg')}
        >
          Upload Image
        </button>
      </div>
    );
  };
  return {
    __esModule: true,
    default: MockUploadImage,
  };
});

// Mock the components
jest.mock('components', () => ({
  TextInput: ({ value, onChange, maxLength, required, ...props }: any) => (
    <input
      data-testid="text-input"
      value={value ?? ''}
      onChange={onChange}
      maxLength={maxLength}
      required={required}
      {...props}
    />
  ),
  ImageReviewModal: ({ children, ...props }: any) => (
    <div data-testid="image-review-modal" {...props}>
      {children}
    </div>
  ),
}));

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => key,
}));

// Mock utils/cookie-client
jest.mock('utils/cookie-client', () => ({
  getCookieLocale: jest.fn(() => 'th'),
}));

// Mock @tanstack/react-query
jest.mock('@tanstack/react-query', () => ({
  useQuery: jest.fn(),
}));

// Mock services
jest.mock('services/resource.service', () => ({
  fetchProvinceService: jest.fn(),
}));

// Mock utils
jest.mock('utils', () => ({
  getImageUrl: jest.fn((filename: string) => `mocked-url/${filename}`),
}));

const mockUseQuery = useQuery as jest.MockedFunction<typeof useQuery>;

describe('CreateCutterVehicle', () => {
  const mockProvinces: Province[] = [
    {
      label: {
        th: 'กรุงเทพมหานคร',
        en: 'Bangkok',
      },
      provinceVehicleCode: 'BKK',
      provinceCode: '10',
      regionCode: '1',
    },
    {
      label: {
        th: 'เชียงใหม่',
        en: 'Chiang Mai',
      },
      provinceVehicleCode: 'CM',
      provinceCode: '50',
      regionCode: '1',
    },
    {
      label: {
        th: 'ภูเก็ต',
        en: 'Phuket',
      },
      provinceVehicleCode: 'PK',
      provinceCode: '83',
      regionCode: '5',
    },
  ];

  const mockVehicle: CutterVehicle = {
    image: {
      id: 'img-1',
      filenameDisk: 'vehicle-image.jpg',
      filenameDownload: 'vehicle.jpg',
    },
    provinceRegistrationNumber: 'BKK',
    vehicleRegistrationNumber: 'ABC123',
  };

  const mockSetVehicle = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();

    // Default mock for useQuery
    mockUseQuery.mockReturnValue({
      data: { data: mockProvinces },
      isLoading: false,
      error: null,
      isError: false,
      isSuccess: true,
      refetch: jest.fn(),
    } as any);
  });

  it('renders all required elements', () => {
    render(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    // Check if vehicle information title is rendered
    expect(screen.getByText('vehicle-information')).toBeInTheDocument();

    // Check if vehicle photo section is rendered
    expect(screen.getByText('vehicle-photo')).toBeInTheDocument();
    expect(screen.getByText('file-size-limit')).toBeInTheDocument();
    // Since mockVehicle has an image, it should show the image review modal instead of upload
    expect(screen.getByTestId('image-review-modal')).toBeInTheDocument();

    // Check if vehicle registration number field is rendered
    expect(screen.getByText('vehicle-registration-number')).toBeInTheDocument();
    expect(screen.getAllByText('*')).toHaveLength(2); // Required asterisks

    // Check if province field is rendered
    expect(screen.getByText('province-vehicle')).toBeInTheDocument();
  });

  it('displays current vehicle data in form fields', () => {
    render(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    const registrationInput = screen.getByTestId('text-input');
    expect(registrationInput).toHaveValue('ABC123');
  });

  it('handles vehicle registration number input changes', async () => {
    render(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    const registrationInput = screen.getByTestId('text-input');

    // Simulate direct input change
    fireEvent.change(registrationInput, { target: { value: 'XYZ789' } });

    // Check that setVehicle was called with the new value
    expect(mockSetVehicle).toHaveBeenCalledWith({
      ...mockVehicle,
      vehicleRegistrationNumber: 'XYZ789',
    });
  });

  it('filters out special characters from vehicle registration number', async () => {
    render(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    const registrationInput = screen.getByTestId('text-input');

    // Simulate typing with special characters
    fireEvent.change(registrationInput, { target: { value: 'ABC@123#$%' } });

    expect(mockSetVehicle).toHaveBeenCalledWith({
      ...mockVehicle,
      vehicleRegistrationNumber: 'ABC123',
    });
  });

  it('allows Thai characters in vehicle registration number input', () => {
    render(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    const registrationInput = screen.getByTestId('text-input');

    // Test input with Thai characters
    fireEvent.change(registrationInput, { target: { value: 'กข123 ดอ456' } });

    expect(mockSetVehicle).toHaveBeenCalledWith({
      ...mockVehicle,
      vehicleRegistrationNumber: 'กข123 ดอ456',
    });
  });

  it('filters out special characters but keeps Thai characters and alphanumeric', () => {
    render(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    const registrationInput = screen.getByTestId('text-input');

    // Test input with mixed characters including Thai, alphanumeric, spaces, and special characters
    fireEvent.change(registrationInput, { target: { value: 'กข123!@# ABC ดอ456$%^' } });

    expect(mockSetVehicle).toHaveBeenCalledWith({
      ...mockVehicle,
      vehicleRegistrationNumber: 'กข123 ABC ดอ456',
    });
  });

  it('handles image upload', async () => {
    const user = userEvent.setup();
    const vehicleWithoutImage: CutterVehicle = {
      ...mockVehicle,
      image: {
        id: '',
        filenameDisk: '',
        filenameDownload: '',
      },
    };

    render(<CreateCutterVehicle vehicle={vehicleWithoutImage} setVehicle={mockSetVehicle} />);

    const uploadButton = screen.getByTestId('upload-image-button');
    await user.click(uploadButton);

    expect(mockSetVehicle).toHaveBeenCalledWith({
      ...vehicleWithoutImage,
      image: {
        ...vehicleWithoutImage.image,
        filenameDisk: 'test-image-filename.jpg',
        id: vehicleWithoutImage.image?.id ?? '',
        filenameDownload: vehicleWithoutImage.image?.filenameDownload ?? '',
      },
    });
  });

  it('handles province selection', async () => {
    render(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    // Find the autocomplete input
    const autocompleteInput = screen.getByRole('combobox');

    // Open the dropdown
    fireEvent.mouseDown(autocompleteInput);

    await waitFor(() => {
      expect(screen.getByText('เชียงใหม่')).toBeInTheDocument();
    });

    // Select Chiang Mai
    const chiangMaiOption = screen.getByText('เชียงใหม่');
    fireEvent.click(chiangMaiOption);

    expect(mockSetVehicle).toHaveBeenCalledWith({
      ...mockVehicle,
      provinceRegistrationNumber: 'CM',
    });
  });

  it('displays correct province in autocomplete based on vehicle data', () => {
    render(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    // The autocomplete should show Bangkok (BKK) as selected
    const autocompleteInput = screen.getByRole('combobox');
    expect(autocompleteInput).toHaveValue('กรุงเทพมหานคร');
  });

  it('handles empty province list when data is loading', () => {
    mockUseQuery.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
      isError: false,
      isSuccess: false,
      refetch: jest.fn(),
    } as any);

    render(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    // Should still render the component without crashing
    expect(screen.getByText('vehicle-information')).toBeInTheDocument();
  });

  it('handles empty vehicle data', () => {
    const emptyVehicle: CutterVehicle = {
      image: undefined,
      provinceRegistrationNumber: '',
      vehicleRegistrationNumber: '',
    };

    render(<CreateCutterVehicle vehicle={emptyVehicle} setVehicle={mockSetVehicle} />);

    const registrationInput = screen.getByTestId('text-input');
    expect(registrationInput).toHaveValue('');

    const autocompleteInput = screen.getByRole('combobox');
    expect(autocompleteInput).toHaveValue('');
  });

  it('respects maxLength constraint on vehicle registration number', () => {
    render(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    const registrationInput = screen.getByTestId('text-input');
    expect(registrationInput).toHaveAttribute('maxLength', '10');
  });

  it('respects maxLength constraint on province autocomplete input', () => {
    render(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    const autocompleteInput = screen.getByRole('combobox');
    expect(autocompleteInput).toHaveAttribute('maxLength', '100');
  });

  it('handles image upload with undefined image object', async () => {
    const user = userEvent.setup();
    const vehicleWithoutImage: CutterVehicle = {
      ...mockVehicle,
      image: undefined,
    };

    render(<CreateCutterVehicle vehicle={vehicleWithoutImage} setVehicle={mockSetVehicle} />);

    const uploadButton = screen.getByTestId('upload-image-button');
    await user.click(uploadButton);

    expect(mockSetVehicle).toHaveBeenCalledWith({
      ...vehicleWithoutImage,
      image: {
        filenameDisk: 'test-image-filename.jpg',
        id: '',
        filenameDownload: '',
      },
    });
  });

  it('uses Thai locale by default', () => {
    render(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    // Should display Thai province names
    const autocompleteInput = screen.getByRole('combobox');
    expect(autocompleteInput).toHaveValue('กรุงเทพมหานคร');
  });

  it('handles province selection with null value', async () => {
    render(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    const autocompleteInput = screen.getByRole('combobox');

    // Clear the selection by simulating autocomplete onChange with null value
    const autocomplete = screen.getByRole('combobox').closest('.MuiAutocomplete-root');
    const clearButton = autocomplete?.querySelector('.MuiAutocomplete-clearIndicator button');

    if (clearButton) {
      fireEvent.click(clearButton);
    } else {
      // Fallback: simulate onChange with null value directly
      fireEvent.change(autocompleteInput, { target: { value: '' } });
    }

    // This should call setVehicle with empty string for provinceRegistrationNumber
    // The onChange handler sets value?.provinceVehicleCode || '' (empty string when null)
    expect(mockSetVehicle).toHaveBeenCalledWith({
      ...mockVehicle,
      provinceRegistrationNumber: '',
    });
  });

  it('handles English locale correctly', () => {
    // Mock getCookieLocale to return 'en'
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { getCookieLocale } = require('utils/cookie-client');
    getCookieLocale.mockReturnValue('en');

    render(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    // Should display English province names
    const autocompleteInput = screen.getByRole('combobox');
    expect(autocompleteInput).toHaveValue('Bangkok');

    // Reset to Thai for other tests
    getCookieLocale.mockReturnValue('th');
  });

  it('handles missing province labels gracefully', () => {
    const provincesWithMissingLabels: Province[] = [
      {
        label: undefined,
        provinceVehicleCode: 'TEST',
        provinceCode: '99',
        regionCode: '9',
      },
    ];

    mockUseQuery.mockReturnValue({
      data: { data: provincesWithMissingLabels },
      isLoading: false,
      error: null,
      isError: false,
      isSuccess: true,
      refetch: jest.fn(),
    } as any);

    const vehicleWithTestProvince: CutterVehicle = {
      ...mockVehicle,
      provinceRegistrationNumber: 'TEST',
    };

    render(<CreateCutterVehicle vehicle={vehicleWithTestProvince} setVehicle={mockSetVehicle} />);

    // Should render without crashing and show empty label
    expect(screen.getByText('vehicle-information')).toBeInTheDocument();
  });

  it('handles query error state', () => {
    mockUseQuery.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: new Error('Failed to fetch provinces'),
      isError: true,
      isSuccess: false,
      refetch: jest.fn(),
    } as any);

    render(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    // Should still render the component without crashing
    expect(screen.getByText('vehicle-information')).toBeInTheDocument();
    expect(screen.getByText('province-vehicle')).toBeInTheDocument();
  });

  it('handles vehicle registration number with only spaces', async () => {
    render(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    const registrationInput = screen.getByTestId('text-input');

    // Type only spaces
    fireEvent.change(registrationInput, { target: { value: '   ' } });

    expect(mockSetVehicle).toHaveBeenCalledWith({
      ...mockVehicle,
      vehicleRegistrationNumber: '   ', // Spaces are allowed
    });
  });

  it('handles mixed alphanumeric input correctly', async () => {
    render(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    const registrationInput = screen.getByTestId('text-input');

    // Type mixed alphanumeric with special characters
    fireEvent.change(registrationInput, { target: { value: 'ABC123def456!@#' } });

    expect(mockSetVehicle).toHaveBeenCalledWith({
      ...mockVehicle,
      vehicleRegistrationNumber: 'ABC123def456', // Only alphanumeric and spaces allowed
    });
  });

  it('maintains component state when provinces data changes', () => {
    // Reset the mock to ensure clean state
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { getCookieLocale } = require('utils/cookie-client');
    getCookieLocale.mockReturnValue('th');

    const { rerender } = render(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    // Initially shows Bangkok in Thai
    const autocompleteInput = screen.getByRole('combobox');
    expect(autocompleteInput).toHaveValue('กรุงเทพมหานคร');

    // Update provinces data
    const newProvinces: Province[] = [
      {
        label: {
          th: 'สงขลา',
          en: 'Songkhla',
        },
        provinceVehicleCode: 'SK',
        provinceCode: '90',
        regionCode: '5',
      },
    ];

    mockUseQuery.mockReturnValue({
      data: { data: newProvinces },
      isLoading: false,
      error: null,
      isError: false,
      isSuccess: true,
      refetch: jest.fn(),
    } as any);

    // Re-render with same vehicle data but different provinces
    rerender(<CreateCutterVehicle vehicle={mockVehicle} setVehicle={mockSetVehicle} />);

    // Should still maintain the vehicle's province selection even if not in new list
    expect(screen.getByText('vehicle-information')).toBeInTheDocument();
  });

  it('calls setVehicle with correct structure when image is uploaded', async () => {
    const user = userEvent.setup();
    const vehicleWithPartialImage: CutterVehicle = {
      ...mockVehicle,
      image: {
        id: 'existing-id',
        filenameDisk: '', // No existing image so upload component is shown
        filenameDownload: 'old-download.jpg',
      },
    };

    render(<CreateCutterVehicle vehicle={vehicleWithPartialImage} setVehicle={mockSetVehicle} />);

    const uploadButton = screen.getByTestId('upload-image-button');
    await user.click(uploadButton);

    expect(mockSetVehicle).toHaveBeenCalledWith({
      ...vehicleWithPartialImage,
      image: {
        filenameDisk: 'test-image-filename.jpg',
        id: 'existing-id',
        filenameDownload: 'old-download.jpg',
      },
    });
  });
});
