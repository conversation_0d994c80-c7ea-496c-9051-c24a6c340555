/* eslint-disable @typescript-eslint/no-explicit-any */

process.env.NEXT_PUBLIC_PATH = 'https://api.example.com';
process.env.NEXT_PUBLIC_ASSET_DOMAIN = 'mocked-value';
process.env.NEXT_PUBLIC_HOST = 'https://example.com';
process.env.NEXT_PUBLIC_WEB_VERSION = 'v1.0.0';
process.env.NEXT_PUBLIC_ENV = 'development';
process.env.NEXT_PUBLIC_APP_VERSION = '1.0.0';
import { TextEncoder, TextDecoder } from 'util';

Object.assign(global, {
  TextEncoder,
  TextDecoder,
});

jest.mock('@mui/x-data-grid', () => ({
  DataGrid: () => 'Mocked DataGrid',
}));

jest.mock(
  'next/link',
  () =>
    ({ children }: { children: React.ReactNode }) =>
      children
);

jest.mock('store/useMaterDataStore.ts', () => ({
  useMasterDataStore: () => ({
    brandNames: [],
    productTypes: [],
    varieties: [],
    grades: [],
    getBrandLabel: jest.fn((id) => `Brand ${id}`),
    getProductTypeLabel: jest.fn((id) => `ProductType ${id}`),
    getVarietyLabel: jest.fn((id) => `Variety ${id}`),
    getGradeLabel: jest.fn((id) => `Grade ${id}`),
    updateMasterData: jest.fn(),
    setVarieties: jest.fn(),
    reset: jest.fn(),
    getTranslatedLabel: jest.fn(() => ''),
  }),
}));

jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => key, // or return a mock translation string
}));

jest.mock('@tanstack/react-query', () => ({
  ...jest.requireActual('@tanstack/react-query'),
  useQuery: jest.fn(),
}));

jest.mock('camelcase-keys', () => ({
  default: (data: any) => data,
}));

jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
  useRouter: jest.fn(),
  useSearchParams: jest.fn(() => ({
    get: jest.fn(),
  })),
}));

jest.mock('hooks/useDeviceHeight');
jest.mock('hooks/useGetEventStatus');
jest.mock('hooks/mutates/useDeleteDraftMutate');
jest.mock('store/useToastStore');
jest.mock('utils/posthog', () => ({
  capturePosthog: jest.fn(),
}));

(useQuery as jest.Mock).mockImplementation(({ queryKey }) => {
  if (queryKey[0] === 'varieties') {
    return {
      data: {
        data: [
          {
            id: 'v1',
            value: 'mon-thong',
            label: {
              th: 'หมอนทอง',
              en: 'Mon Thong',
            },
            flowerBloomingDay: 90,
            grades: [
              {
                id: 'A',
                value: 'A',
                label: {
                  th: 'Grade A',
                  en: 'Grade A (English)',
                },
                weight: 0,
              },
              {
                id: 'B',
                value: 'B',
                label: {
                  th: 'Grade B',
                  en: 'Grade B (English)',
                },
                weight: 0,
              },
            ],
          },
        ],
      },
      isLoading: false,
      error: null,
    };
  }

  return { data: undefined, isLoading: true, error: null };
});

jest.mock('react-hook-form', () => {
  const actual = jest.requireActual('react-hook-form');
  return {
    ...actual,
    useForm: () => ({
      ...actual.useForm(),
      reset: jest.fn(),
    }),
  };
});

jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(),
}));

jest.mock('next/server', () => ({
  NextRequest: jest.fn(),
  NextResponse: jest.fn(),
}));

import { useQuery } from '@tanstack/react-query';
import '@testing-library/jest-dom';
export * from '@testing-library/react';
