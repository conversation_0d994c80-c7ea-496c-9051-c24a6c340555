'use client';

import { Box, Stack } from '@mui/material';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';

import { Drawer, FormDatePickerInput, FormNumberInput, FormSelect, SelectOption } from 'components';
import { DurianInformationForm, useCreateShipmentStore, VarietyOptionValue } from 'store/useCreateShipmentStore';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { getLabelByLocale } from 'utils/cookie-client';
import { v4 } from 'uuid';
import { safeJSONParse } from 'utils';
import dayjs from 'dayjs';

const defaultValues = {
  brand: '',
  boxType: '',
  variety: '',
  grade: '',
  netWeight: '',
  totalBoxes: '',
  packingDate: dayjs().startOf('day').toDate(),
};

export const SelectPackagingInformationDrawer = () => {
  const shipmentT = useTranslations('shipment');
  const { openModalInformation, updateOpenPackagingModal, updateInformationForm, varietiesList } =
    useCreateShipmentStore();

  const {
    brandNames,
    productTypes,

    getGradeLabel,
    getProductTypeLabel,
    getBrandLabel,
  } = useMasterDataStore();

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    register,
    setValue,
    reset,
  } = useForm<DurianInformationForm>({
    defaultValues: { ...defaultValues },
  });

  const variety = watch('variety');

  register('variety', {
    onChange: () => {
      setValue('grade', '');
    },
  });

  const brandOpts = brandNames.map((it) => ({ value: it.id, label: getBrandLabel(it.id) }));
  const boxOpts = productTypes.map((it) => ({ value: it.id, label: getProductTypeLabel(it.id) }));

  const varietyOpts: SelectOption[] = varietiesList.map((it, index) => ({
    value: JSON.stringify({
      id: it.id,
      label: it.label,
      isOther: it.isOther,
      name: it.name,
      originalId: it.originalId,
      index,
    }),
    label: getLabelByLocale(it),
    originalValue: it.originalId,
    it: it.id,
    grades: it.grades,
    name: it.name,
  }));

  const varietyObj = variety ? safeJSONParse<VarietyOptionValue>(variety) : null;

  const selectedGrade = varietiesList.find((it) => it.id === varietyObj?.id)?.grades;

  const gradeOpts = selectedGrade?.map((it) => ({ value: it.id, label: getGradeLabel(it.id) })) ?? [];

  const onClose = () => {
    updateOpenPackagingModal(false);
    reset({ ...defaultValues });
  };

  const onSubmit = (values: DurianInformationForm) => {
    updateInformationForm({
      ...values,
      id: v4(),
    });
    onClose();
  };

  const drawerTitle = shipmentT('packaging-information-title');
  const durianInformationT = useTranslations('durian-packaging-information');

  return (
    <Drawer
      anchor="right"
      drawerTitle={drawerTitle}
      open={openModalInformation}
      onClose={onClose}
      onConfirm={handleSubmit(onSubmit)}
      disableScrollLock
    >
      <Box component="form" sx={{ flex: 1, overflowY: 'auto', p: 2 }}>
        <Stack spacing={2}>
          <FormSelect<DurianInformationForm>
            name="brand"
            control={control}
            errors={errors}
            label={shipmentT('brand-name-label')}
            options={brandOpts}
            required
            placeholder={durianInformationT('placeholder-brand-name')}
            requiredMessage={shipmentT('brand-name-required')}
          />

          <FormSelect<DurianInformationForm>
            name="boxType"
            control={control}
            errors={errors}
            placeholder={durianInformationT('placeholder-product-type')}
            label={shipmentT('type-of-box-label')}
            options={boxOpts}
            required
            requiredMessage={shipmentT('product-type-required')}
          />

          <FormSelect<DurianInformationForm>
            name="variety"
            control={control}
            errors={errors}
            label={shipmentT('variety-label')}
            placeholder={durianInformationT('placeholder-variety')}
            options={varietyOpts}
            required
            requiredMessage={shipmentT('variety-required')}
          />

          <FormSelect<DurianInformationForm>
            name="grade"
            disabled={!variety}
            control={control}
            errors={variety ? errors : {}}
            label={shipmentT('grade-label')}
            options={gradeOpts}
            required
            placeholder={durianInformationT('placeholder-grade')}
            requiredMessage={shipmentT('grade-required')}
          />

          <FormNumberInput<DurianInformationForm>
            errors={errors}
            name="netWeight"
            control={control}
            label={shipmentT('net-weight-label')}
            required
            placeholder={durianInformationT('placeholder-net-weight')}
            decimalScale={1}
            max={9999999.9}
            requiredMessage={shipmentT('box-net-weight-required')}
            minRequireMessage={shipmentT('box-net-weight-min')}
            min={1}
          />

          <FormNumberInput<DurianInformationForm>
            name="totalBoxes"
            control={control}
            errors={errors}
            label={shipmentT('total-of-boxes-label')}
            placeholder={durianInformationT('placeholder-number-of-box')}
            decimalScale={0}
            required
            requiredMessage={shipmentT('total-boxes-required')}
            minRequireMessage={shipmentT('total-boxes-min')}
            min={1}
            max={9999999}
          />

          <FormDatePickerInput<DurianInformationForm>
            errors={errors}
            name="packingDate"
            required
            requiredMessage={shipmentT('packaging-date-required')}
            control={control}
            label={shipmentT('packaging-date')}
          />
        </Stack>
      </Box>
    </Drawer>
  );
};
