import posthog from 'posthog-js';

export const FEATURE_FLAGS = {
  FEAT_CHECK: 'FEAT_CHECK',
  ENABLE_DELETE_SHIPMENT: 'ENABLE_DELETE_SHIPMENT',
  ENABLED_SHIPMENT_HISTORY: 'ENABLED_SHIPMENT_HISTORY', // sprint 8
  ENABLED_QR_MANAGEMENT: 'ENABLED_QR_MANAGEMENT', // sprint 8
} as const;

type FeatureFlag = {
  FEAT_CHECK: boolean;
  ENABLE_DELETE_SHIPMENT: boolean;
  ENABLED_SHIPMENT_HISTORY: boolean; // sprint 8
  ENABLED_QR_MANAGEMENT: boolean; // sprint 8
};

export const isFeatureEnabled = (flagName: string, defaultValue = false): boolean => {
  return posthog?.isFeatureEnabled(flagName) ?? defaultValue;
};

export const useFeatureFlag = (): FeatureFlag => {
  return {
    FEAT_CHECK: isFeatureEnabled(FEATURE_FLAGS.FEAT_CHECK),
    ENABLE_DELETE_SHIPMENT: isFeatureEnabled(FEATURE_FLAGS.ENABLE_DELETE_SHIPMENT),
    ENABLED_SHIPMENT_HISTORY: isFeatureEnabled(FEATURE_FLAGS.ENABLED_SHIPMENT_HISTORY), // sprint 8
    ENABLED_QR_MANAGEMENT: isFeatureEnabled(FEATURE_FLAGS.ENABLED_QR_MANAGEMENT), // sprint 8
  };
};
