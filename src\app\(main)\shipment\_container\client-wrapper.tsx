'use client';

import { PackingHouseDetail } from 'types';
import dynamic from 'next/dynamic';

const ShipmentDetail = dynamic(() => import('containers/shipment').then((mod) => mod.EventDetail), {
  ssr: false
});

type ClientWrapperProps = {
  eventDetail: PackingHouseDetail;
  id: string;
  isReceiving?: boolean; // Optional prop to indicate if it's a receiving event
};

export default function ClientWrapper({ eventDetail, id }: ClientWrapperProps) {
  return <ShipmentDetail key={id} shipmentDetail={eventDetail} />;
}
