# .github/workflows/release.yml
name: Manual Release
on:
  workflow_dispatch:
    inputs:
      env:
        description: "Environment to deploy"
        required: true
        default: "staging"
        type: choice
        options:
          - staging

      bump:
        description: "Version bump type"
        required: true
        default: "patch"
        type: choice
        options:
          - patch
          - minor
          - major

jobs:
  Create-Release:
    runs-on: ubuntu-latest

    permissions:
      contents: write   # Required to push tags & releases

    steps:
      - name: 📥 Checkout Repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🔧 Install Dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y jq

      - name: 🚀 Run Release Script
        id: create_release
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          if [ "${{ github.event.inputs.env }}" == "staging" ]; then
            ./scripts/release.sh --bump=${{ github.event.inputs.bump }} --yes
          elif [ "${{ github.event.inputs.env }}" == "sandbox" ]; then
            ./scripts/release-sandbox.sh --bump=${{ github.event.inputs.bump }} --yes
          elif [ "${{ github.event.inputs.env }}" == "prod" ]; then
            ./scripts/release-prod.sh --bump=${{ github.event.inputs.bump }} --yes
          else
            echo "Invalid environment: ${{ github.event.inputs.env }}"
            exit 1
          fi
    outputs:
      version: ${{ steps.create_release.outputs.version }}
      release_branch: ${{ steps.create_release.outputs.release_branch }}

  Trigger-CICD:
    runs-on: ubuntu-latest
    needs: [Create-Release]
    if: github.event.inputs.env == 'staging'
    env:
      DISPATCH_REPO: Vietnam-Silicon/cicd-workflows
      DISPATCH_WORKFLOW: cicd_backend.yml

    steps:
      - name: Set Environment
        id: set_environment
        run: |
          echo "Triggering the workflow"
          echo "ENVIRONMENT=${{ github.event.inputs.env }}" >> $GITHUB_ENV

      - name: Set version to deploy
        id: set_version
        run: |
          echo "VERSION=${{ needs.Create-Release.outputs.version }}" >> $GITHUB_ENV
          echo "RELEASE_BRANCH=${{ needs.Create-Release.outputs.release_branch }}" >> $GITHUB_ENV

      - name: Trigger repository dispatch
        uses: peter-evans/repository-dispatch@v3
        if: steps.set_environment.conclusion == 'success'
        with:
          token: ${{ secrets.GHA_CICD_TOKEN }}
          repository: Vietnam-Silicon/cicd-workflows
          event-type: cicd-backend
          client-payload: '{"triggered_by": "${{ github.repository }}", "commit_sha": "${{ github.sha }}", "branch": "${{ env.RELEASE_BRANCH }}",
                            "project": "${{ vars.PROJECT }}", "service": "${{ vars.SERVICE_NAME }}", "environment": "${{ env.ENVIRONMENT }}",
                            "full_svc_name": "${{ vars.PROJECT }}-${{ env.ENVIRONMENT }}-${{ vars.SERVICE_NAME }}", "version": "${{ env.VERSION }}"}'

      - name: Wait for CICD Workflow to start
        if: steps.set_environment.conclusion == 'success'
        run: |
          echo "Waiting for CICD Workflow to start..."
          sleep 10

      - name: Get Workflow Run Info and Monitor Status
        if: steps.set_environment.conclusion == 'success'
        id: monitor
        run: |
          echo "Fetching latest run of CICD Workflow...."

          # Call GitHub API to get the most recent run
          response=$(curl -s -H "Authorization: Bearer ${{ secrets.GHA_CICD_TOKEN }}" \
          https://api.github.com/repos/${{ env.DISPATCH_REPO }}/actions/workflows/${{ env.DISPATCH_WORKFLOW }}/runs?branch=main&event=repository_dispatch)

          # Use jq to parse the first workflow run
          first_run=$(echo "$response" | jq -r '.workflow_runs[0]')

          # Extract run details using jq
          run_id=$(echo "$first_run" | jq -r '.id')
          run_number=$(echo "$first_run" | jq -r '.run_number')
          html_url=$(echo "$first_run" | jq -r '.html_url')

          echo "CICD Workflow Run ID: $run_id"
          echo "CICD Workflow Run Number: $run_number"
          echo "CICD Workflow URL: $html_url"

          echo "RUN_ID=$run_id" >> $GITHUB_ENV
          echo "RUN_URL=$html_url" >> $GITHUB_ENV

      - name: Generate Workflow Summary
        if: always()
        run: |
          echo "## Workflow Trigger Summary" >> $GITHUB_STEP_SUMMARY
          echo "### Triggered Workflow Details" >> $GITHUB_STEP_SUMMARY
          echo "- **Repository:** ${{ env.DISPATCH_REPO }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Workflow:** ${{ env.DISPATCH_WORKFLOW }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Run ID:** ${{ env.RUN_ID }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Run URL:** [${{ env.RUN_URL }}](${{ env.RUN_URL }})" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Trigger Details" >> $GITHUB_STEP_SUMMARY
          echo "- **Triggered by:** ${{ github.repository }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Commit SHA:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Branch:** ${{ env.RELEASE_BRANCH }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
