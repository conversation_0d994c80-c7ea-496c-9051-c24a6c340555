/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { AppConfig } from 'configs/app-config';
import { getCookieServer } from 'configs/cookie';
import { createServerApi } from 'services/api/serverApi';
import { AxiosError, AxiosRequestConfig } from 'axios';
import { logger } from 'utils/logger';
import { toCurl } from 'utils/handler';

const RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000,
  retryableStatuses: [408, 429, 500, 502, 503, 504],
};

const retryRequest = async (api: any, config: AxiosRequestConfig, attempt = 1): Promise<any> => {
  try {
    return await api.request(config);
  } catch (error) {
    if (error instanceof AxiosError) {
      const shouldRetry =
        attempt < RETRY_CONFIG.maxRetries &&
        (RETRY_CONFIG.retryableStatuses.includes(error.response?.status || 0) || !error.response); // Network errors

      if (shouldRetry) {
        const delay = RETRY_CONFIG.baseDelay * Math.pow(2, attempt - 1);
        logger.info(`API request failed (attempt ${attempt}/${RETRY_CONFIG.maxRetries}). Retrying in ${delay}ms...`);

        await new Promise((resolve) => setTimeout(resolve, delay));
        return retryRequest(api, config, attempt + 1);
      }
    }

    throw error;
  }
};

export const createServerEventService = async (cacheAge?: number) => {
  const accessToken = await getCookieServer<string>('access_token_pkg_house');
  const headers: Record<string, string> = {};
  headers['content-type'] = 'application/json';

  if (accessToken) {
    headers['authorization'] = `Bearer ${accessToken}`;
  }

  if (cacheAge) {
    headers['cache-control'] = `max-age=${cacheAge}`;
  }

  const api = await createServerApi(AppConfig.CONTROLLER_URL, headers, undefined, 'json');

  // Add retry interceptor
  api.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config;

      if (originalRequest._retry) {
        return error;
      }

      originalRequest._retry = true;

      return retryRequest(api, originalRequest);
    }
  );

  api.interceptors.request.use((config) => {
    console.info('\x1b[36m%s\x1b[0m', `------------- Start Curl ${config.url} ------------- `);
    console.info(
      'Curl',
      toCurl({
        url: `${config.baseURL}/${config.url}`,
        method: config.method,
        headers: config.headers,
        body: config.data,
      }).join(' ')
    );
    console.info('\x1b[36m%s\x1b[0m', `End ------------- End Curl ${config.url} ------------- `);

    return config;
  });

  return api;
};
