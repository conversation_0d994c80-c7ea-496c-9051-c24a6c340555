import { Box, Chip, IconButton, Stack, styled, Typography } from '@mui/material';
import { FC } from 'react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';

import durianIcon from 'assets/icons/durian.svg';

interface PackagingDurianBoxProps {
  brandName: string;
  variety: string;
  typeOfBox: string;
  grade: string;
  netWeight: string;
  totalBox: string;
  isDelete?: boolean;
  packingDate?: string;
  onDelete?: () => void;
}

const StyledChip = styled(Chip)`
  background-color: ${({ theme }) => theme.palette.customColors.primary100};
  color: ${({ theme }) => theme.palette.customColors.primary10};
  font-size: 14px;
`;

export const PackagingDurianBox: FC<PackagingDurianBoxProps> = ({
  brandName,
  variety,
  typeOfBox,
  grade,
  netWeight,
  totalBox,
  isDelete = true,
  onDelete,
  packingDate,
}) => {
  const commonT = useTranslations('common');
  const shipmentT = useTranslations('shipment');
  return (
    <Box
      component="div"
      sx={{
        width: '100%',
        p: '12px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        gap: '8px',
        bgcolor: (theme) => theme.palette.customColors.primary50,
        borderRadius: '8px',
        boxSizing: 'border-box',
        height: '110px',
        position: 'relative',
      }}
    >
      <Image width={48} height={48} unoptimized alt="durian-icon" src={durianIcon} />
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          flex: 1,
          justifyContent: 'space-between',
          gap: '4px',
        }}
      >
        <Typography component="h6" fontWeight="500">
          {brandName}
        </Typography>
        <Stack direction="row" spacing={'4px'} useFlexGap flexWrap={'wrap'} sx={{ width: '100%', flex: 1 }}>
          {variety && <StyledChip label={variety} />}
          {grade && <StyledChip label={grade} />}
          <StyledChip label={`${typeOfBox}`} />
          <StyledChip label={`${netWeight}${commonT('kg')}`} />
          <StyledChip label={`${totalBox} ${commonT('boxes')}`} />
          {packingDate && <StyledChip label={shipmentT('packed-on', { date: packingDate })} />}

        </Stack>
      </Box>
      {isDelete && onDelete && (
        <IconButton onClick={onDelete} color="error" size="small" sx={{ position: 'absolute', top: '12px', right: 0 }}>
          <DeleteOutlineOutlinedIcon fontSize="inherit" />
        </IconButton>
      )}
    </Box>
  );
};
