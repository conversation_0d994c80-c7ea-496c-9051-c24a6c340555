'use client';

import { Dialog, DialogProps } from '@mui/material';
import { FC } from 'react';

type MobileDialogProps = DialogProps & {
  onClose: () => void;
};

export const MobileDialog: FC<MobileDialogProps> = ({ open, onClose, children }) => {
  return (
    <Dialog
      slotProps={{
        paper: {
          sx: {
            minHeight: '800px',
            maxWidth: '800px',
            display: 'flex',
            flexDirection: 'column',
            backgroundColor: 'transparent',
          },
        },
      }}
      open={open}
      onClose={onClose}
      fullWidth
      fullScreen
      maxWidth='sm'
    >
      {children}
    </Dialog>
  );
};
