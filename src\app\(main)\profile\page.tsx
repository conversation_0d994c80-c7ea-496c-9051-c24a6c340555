import camelcaseKeys from 'camelcase-keys';
import { AppConfig } from 'configs/app-config';
import { getCookieServer } from 'configs/cookie';
import { NextPage } from 'next';
import dynamic from 'next/dynamic';
import { redirect } from 'next/navigation';
import { createServerApi } from 'services/api/serverApi';
import { User } from 'types/user';

const ProfileWrapper = dynamic(() => import('containers/profile/wrapper-profile'));

const Index: NextPage = async () => {
  const accessToken = await getCookieServer<User>('access_token_pkg_house');

  const userInfo = accessToken && ((await getCookieServer<User>('user_info')) as User);

  const headers: Record<string, string> = {};

  headers['content-type'] = 'application/json';

  if (accessToken) {
    headers['authorization'] = `Bearer ${accessToken}`;
  }

  const api = await createServerApi(AppConfig.CONTROLLER_URL, headers, undefined, 'json');

  if (!userInfo) {
    const userInfoResponse = await api.get('/v1/user/me');

    const userResponseData = userInfoResponse.data;

    const userData = userResponseData.data;

    if (!userData) {
      return redirect('not-found');
    }
    return <ProfileWrapper user={camelcaseKeys(userData as Record<string, unknown>, { deep: true }) as User} />;
  }
  return <ProfileWrapper user={camelcaseKeys(userInfo as Record<string, unknown>, { deep: true }) as User} />;
};

export default Index;
