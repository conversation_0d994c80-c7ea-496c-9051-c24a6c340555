/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import { Box } from '@mui/material';
import { basePathProd } from 'configs/app-config';
import { FC, useEffect, useState } from 'react';
import sanitizeHtml from 'sanitize-html';
import { getCookieLocale } from 'utils/cookie-client';
import { logger } from 'utils/logger';

type EventDetailProps = {
  onClose?: (data: any) => void;
};

export const CookieModal: FC<EventDetailProps> = () => {
  const [html, setHtml] = useState('');

  useEffect(() => {
    const locale = getCookieLocale();
    fetch(`${basePathProd}/assets/cookies/${locale === 'en' ? 'EN_Cookies' : 'TH_Cookies'}.html`)
      .then((response) => response.text())
      .then((data) => {
        setHtml(data);
      })
      .catch((error) => logger.error('Error loading HTML file:', { error }));
  }, []);

  return (
    <Box
      sx={{
        padding: '20px',
        display: 'flex',
        justifyContent: 'center',
        flexDirection: 'column',
        width: '100%',
        alignItems: 'center',
        gap: 4,
      }}
    >
      <div
        style={{ height: '60vh', overflowY: 'scroll', width: '50vw', margin: 32, fontSize: '14px' }}
        dangerouslySetInnerHTML={{ __html: sanitizeHtml(html) || '' }}
      />
    </Box>
  );
};
