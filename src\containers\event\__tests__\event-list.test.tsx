/* eslint-disable @typescript-eslint/no-require-imports */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Event } from '../event-list';
import { ThemeProvider } from '@mui/material/styles';
import { theme } from 'styles/theme';
import { EventStatusEnum } from 'types';

// Mock dependencies
jest.mock('@tanstack/react-query', () => ({
  useQuery: jest.fn(),
  useMutation: jest.fn(),
  useQueryClient: jest.fn(),
}));

jest.mock('hooks/useDeviceHeight', () => ({
  useDeviceHeight: jest.fn(),
}));

jest.mock('hooks/useGetEventStatus', () => ({
  useGetEventStatus: jest.fn(),
}));

jest.mock('hooks/mutates/useDeleteDraftMutate', () => ({
  useDeleteDraftMutate: jest.fn(),
}));

jest.mock('store/useToastStore', () => ({
  useToastStore: jest.fn(),
}));

jest.mock('next-intl', () => ({
  useTranslations: jest.fn(),
}));

jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(),
}));

jest.mock('utils', () => ({
  formatNumberWithCommas: jest.fn((value) => value?.toString() ?? '0'),
  getStatus: jest.fn(),
}));

jest.mock('utils/posthog', () => ({
  capturePosthog: jest.fn(),
}));

jest.mock('utils/toastMessages', () => ({
  __esModule: true,
  default: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warning: jest.fn(),
  },
}));

jest.mock('components', () => ({
  Dialog: ({ isOpen, title, content, onConfirm, onCancel, okButtonText }: any) =>
    isOpen ? (
      <div data-testid="dialog">
        <h2>{title}</h2>
        <p>{content}</p>
        <button onClick={onConfirm} data-testid="dialog-confirm">
          {okButtonText ?? 'Confirm'}
        </button>
        <button onClick={onCancel} data-testid="dialog-cancel">
          Cancel
        </button>
      </div>
    ) : null,
  EventDataTable: ({
    columns,
    onRowClick,
    eventType,
    queryKey,
    filterStatusOptions,
    tableHeight,
    defaultSortModel,
    hasFilterUserRole
  }: any) => (
    <div data-testid="event-data-table">
      <div data-testid="event-type">{eventType}</div>
      <div data-testid="query-key">{queryKey}</div>
      <div data-testid="table-height">{tableHeight}</div>
      <div data-testid="has-filter-user-role">{hasFilterUserRole?.toString()}</div>
      <div data-testid="filter-status-options">{JSON.stringify(filterStatusOptions)}</div>
      <div data-testid="default-sort-model">{JSON.stringify(defaultSortModel)}</div>
      <div data-testid="columns-count">{columns?.length}</div>
      <button
        onClick={() => onRowClick?.({ row: { productId: 'test-id', type: 'harvesting', status: 'waiting' } })}
        data-testid="mock-row-click"
      >
        Mock Row Click
      </button>
    </div>
  ),
}));

jest.mock('../_components', () => ({
  renderRecordBy: jest.fn((params) => <span data-testid="record-by">{params.value}</span>),
  renderRecordOn: jest.fn((params) => <span data-testid="record-on">{params.value}</span>),
  renderStatus: jest.fn((status, label) => <span data-testid="status">{label}</span>),
  renderUpdatedBy: jest.fn((params) => <span data-testid="updated-by">{params.value}</span>),
  renderUpdatedOn: jest.fn((params) => <span data-testid="updated-on">{params.value}</span>),
}));

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider theme={theme}>{children}</ThemeProvider>
);

describe('Event Component', () => {
  const mockMutateAsync = jest.fn();
  const mockRouter = { push: jest.fn() };
  const mockToastStore = { toast: null, resetToast: jest.fn() };
  const mockGetStatusLabel = jest.fn();
  const mockTranslation = jest.fn((key: string) => key);
  const mockDeviceHeight = 800;

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup mocks
    const { useDeviceHeight } = require('hooks/useDeviceHeight');
    const { useGetEventStatus } = require('hooks/useGetEventStatus');
    const { useDeleteDraftMutate } = require('hooks/mutates/useDeleteDraftMutate');
    const { useToastStore } = require('store/useToastStore');
    const { useTranslations } = require('next-intl');
    const { useRouter, usePathname } = require('next/navigation');
    const { getStatus } = require('utils');
    require('utils/posthog');

    useDeviceHeight.mockReturnValue(mockDeviceHeight);
    useGetEventStatus.mockReturnValue({
      getStatusLabel: mockGetStatusLabel,
    });
    useDeleteDraftMutate.mockReturnValue({
      mutateAsync: mockMutateAsync,
    });
    useToastStore.mockReturnValue(mockToastStore);
    useTranslations.mockReturnValue(mockTranslation);
    useRouter.mockReturnValue(mockRouter);
    usePathname.mockReturnValue('/event/incoming');
    getStatus.mockReturnValue(EventStatusEnum.WAITING);

    mockGetStatusLabel.mockReturnValue({
      eventStatus: 'waiting',
      eventStatusLabel: 'Waiting',
    });
  });

  describe('Component Rendering', () => {
    it('renders event list component with incoming batch lot title', () => {
      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      expect(screen.getByText('incomingBatchLot')).toBeInTheDocument();
      // Todo: update for accessibility tests for feature flags
      // expect(screen.getByText('create-harvest-log')).toBeInTheDocument();
      expect(screen.getByTestId('event-data-table')).toBeInTheDocument();
    });

    it('renders receiving batch lot title when not incoming', () => {
      const { usePathname } = require('next/navigation');
      usePathname.mockReturnValue('/event/receiving');

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      expect(screen.getByText('receivingBatchLot')).toBeInTheDocument();
      expect(screen.queryByText('create-harvest-log')).not.toBeInTheDocument();
    });

    it('renders EventDataTable with correct props for incoming', () => {
      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      expect(screen.getByTestId('event-type')).toHaveTextContent('harvesting');
      expect(screen.getByTestId('query-key')).toHaveTextContent('harvesting-event-data');
      expect(screen.getByTestId('has-filter-user-role')).toHaveTextContent('true');
      expect(screen.getByTestId('table-height')).toHaveTextContent('526'); // 800 - 64 - 210
    });

    it('renders EventDataTable with correct props for receiving', () => {
      const { usePathname } = require('next/navigation');
      usePathname.mockReturnValue('/event/receiving');

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      expect(screen.getByTestId('event-type')).toHaveTextContent('receiving');
      expect(screen.getByTestId('query-key')).toHaveTextContent('receiving-event-data');
      expect(screen.getByTestId('filter-status-options')).toHaveTextContent('');
    });
  });

  // Todo: need to update for accessibility tests for feature flags
  describe('User Interactions', () => {
    it('handles create harvest log button click', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      const createButton = screen.getByText('create-harvest-log');
      await user.click(createButton);

      expect(mockRouter.push).toHaveBeenCalledWith('/event/incoming/create');
    });

    it('handles row click for non-draft status', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      const mockRowButton = screen.getByTestId('mock-row-click');
      await user.click(mockRowButton);

      expect(mockRouter.push).toHaveBeenCalledWith('/event/incoming/test-id');
    });

    it('handles row click for draft status', async () => {
      const user = userEvent.setup();
      const { getStatus } = require('utils');
      getStatus.mockReturnValue(EventStatusEnum.DRAFT);

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      const mockRowButton = screen.getByTestId('mock-row-click');
      await user.click(mockRowButton);

      expect(mockRouter.push).toHaveBeenCalledWith('/event/incoming/create/test-id');
    });

    it('handles row click for receiving mode', async () => {
      const user = userEvent.setup();
      const { usePathname } = require('next/navigation');
      usePathname.mockReturnValue('/event/receiving');

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      const mockRowButton = screen.getByTestId('mock-row-click');
      await user.click(mockRowButton);

      expect(mockRouter.push).toHaveBeenCalledWith('/event/receiving/test-id');
    });
  });

  describe('Toast Notifications', () => {
    it('displays toast notification when available', () => {
      const mockToast = {
        type: 'success',
        message: 'Operation successful',
      };
      const { useToastStore } = require('store/useToastStore');
      useToastStore.mockReturnValue({
        toast: mockToast,
        resetToast: jest.fn(),
      });

      const toastMessages = require('utils/toastMessages').default;

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      expect(toastMessages.success).toHaveBeenCalledWith('Operation successful');
    });

    it('resets toast after displaying', () => {
      const mockResetToast = jest.fn();
      const mockToast = {
        type: 'error',
        message: 'Operation failed',
      };
      const { useToastStore } = require('store/useToastStore');
      useToastStore.mockReturnValue({
        toast: mockToast,
        resetToast: mockResetToast,
      });

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      expect(mockResetToast).toHaveBeenCalled();
    });
  });

  describe('PostHog Analytics', () => {
    it('captures incoming batch lot view event', () => {
      const { capturePosthog } = require('utils/posthog');

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      expect(capturePosthog).toHaveBeenCalledWith('view_incoming_batch_lot');
    });

    it('captures receiving batch lot view event', () => {
      const { usePathname } = require('next/navigation');
      const { capturePosthog } = require('utils/posthog');
      usePathname.mockReturnValue('/event/receiving');

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      expect(capturePosthog).toHaveBeenCalledWith('view_receiving_batch_lot');
    });
  });

  describe('Delete Dialog', () => {
    it('does not show dialog initially', () => {
      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
    });

    it('shows delete confirmation dialog with correct content', () => {
      // This test would require more complex state mocking
      // For now, we'll test the dialog structure when it's open
      // In a real scenario, this would be triggered by user interaction

      // Mock the component to have dialog open
      const mockSetState = jest.fn();
      const useStateSpy = jest.spyOn(React, 'useState');
      useStateSpy.mockImplementationOnce(() => ['test-product-id', mockSetState]);

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      expect(screen.getByTestId('dialog')).toBeInTheDocument();
      expect(screen.getByText('delete-harvest-title')).toBeInTheDocument();
      expect(screen.getByText('delete-harvest-content')).toBeInTheDocument();
      expect(screen.getByText('delete-modal-btn')).toBeInTheDocument();

      useStateSpy.mockRestore();
    });

    it('handles delete confirmation', async () => {
      const user = userEvent.setup();

      // Mock useState to simulate dialog open state
      const mockSetState = jest.fn();
      const useStateSpy = jest.spyOn(React, 'useState');
      useStateSpy.mockImplementationOnce(() => ['test-product-id', mockSetState]);

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      const confirmButton = screen.getByTestId('dialog-confirm');
      await user.click(confirmButton);

      await waitFor(() => {
        expect(mockMutateAsync).toHaveBeenCalledWith('test-product-id');
        expect(mockSetState).toHaveBeenCalledWith(null);
      });

      useStateSpy.mockRestore();
    });

    it('handles delete cancellation', async () => {
      const user = userEvent.setup();

      // Mock useState to simulate dialog open state
      const mockSetState = jest.fn();
      const useStateSpy = jest.spyOn(React, 'useState');
      useStateSpy.mockImplementationOnce(() => ['test-product-id', mockSetState]);

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      const cancelButton = screen.getByTestId('dialog-cancel');
      await user.click(cancelButton);

      expect(mockSetState).toHaveBeenCalledWith(null);
      expect(mockMutateAsync).not.toHaveBeenCalled();

      useStateSpy.mockRestore();
    });
  });

  describe('Column Configuration', () => {
    it('configures incoming columns correctly', () => {
      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      // Should have 9 columns for incoming (name, batchlot, recordBy, status, totalVarietiesWeight, userUpdated, dateUpdated, dateCreated, action)
      expect(screen.getByTestId('columns-count')).toHaveTextContent('9');
    });

    it('configures receiving columns correctly', () => {
      const { usePathname } = require('next/navigation');
      usePathname.mockReturnValue('/event/receiving');

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      // Should have 8 columns for receiving (batchlot, originBatchlot, recordBy, totalVarietiesWeight, userUpdated, dateUpdated, dateCreated, action)
      expect(screen.getByTestId('columns-count')).toHaveTextContent('8');
    });

    it('includes filter status options for incoming', () => {
      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      const filterOptions = JSON.parse(screen.getByTestId('filter-status-options').textContent ?? '[]');
      expect(filterOptions).toHaveLength(4);
      expect(filterOptions[0]).toEqual({ label: 'status-incoming-waiting', value: 'waiting' });
      expect(filterOptions[1]).toEqual({ label: 'status-received', value: 'received' });
      expect(filterOptions[2]).toEqual({ label: 'status-rejected', value: 'rejected' });
      expect(filterOptions[3]).toEqual({ label: 'status-draft', value: 'draft' });
    });

    it('does not include filter status options for receiving', () => {
      const { usePathname } = require('next/navigation');
      usePathname.mockReturnValue('/event/receiving');

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      expect(screen.getByTestId('filter-status-options')).toHaveTextContent('');
    });
  });

  describe('Table Height Calculation', () => {
    it('calculates table height correctly with device height', () => {
      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      // deviceHeight (800) - headerHeight (64) - paddingHeight (210) = 526
      expect(screen.getByTestId('table-height')).toHaveTextContent('526');
    });

    it('returns 0 when device height is not available', () => {
      const { useDeviceHeight } = require('hooks/useDeviceHeight');
      useDeviceHeight.mockReturnValue(0);

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      expect(screen.getByTestId('table-height')).toHaveTextContent('0');
    });
  });

  describe('Default Sort Model', () => {
    it('sets default sort model correctly', () => {
      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      const sortModel = JSON.parse(screen.getByTestId('default-sort-model').textContent ?? '[]');
      expect(sortModel).toEqual([{ field: 'dateCreated', sort: 'desc' }]);
    });
  });

  describe('Action Buttons Rendering', () => {
    // These tests would require more complex mocking of the renderAction callback
    // For now, we'll test the basic structure and leave detailed action button testing
    // for integration tests or component-specific tests

    it('renders action column in columns configuration', () => {
      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      // The action column should be included in the column count
      expect(screen.getByTestId('columns-count')).toHaveTextContent('9');
    });
  });

  describe('Error Handling', () => {
    it('handles missing translations gracefully', () => {
      const { useTranslations } = require('next-intl');
      useTranslations.mockReturnValue(() => 'missing-translation');

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      expect(screen.getAllByText('missing-translation').length).toBeGreaterThan(0);
    });

    it('handles missing device height gracefully', () => {
      const { useDeviceHeight } = require('hooks/useDeviceHeight');
      useDeviceHeight.mockReturnValue(null);

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      expect(screen.getByTestId('table-height')).toHaveTextContent('0');
    });

    it('calls mutateAsync when delete is confirmed', async () => {
      const user = userEvent.setup();

      // Mock useState to simulate dialog open state
      const mockSetState = jest.fn();
      const useStateSpy = jest.spyOn(React, 'useState');
      useStateSpy.mockImplementationOnce(() => ['test-product-id', mockSetState]);

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      const confirmButton = screen.getByTestId('dialog-confirm');
      await user.click(confirmButton);

      await waitFor(() => {
        expect(mockMutateAsync).toHaveBeenCalledWith('test-product-id');
        expect(mockSetState).toHaveBeenCalledWith(null);
      });

      useStateSpy.mockRestore();
    });
  });


  // Todo: update for accessibility tests for feature flags
  describe('Accessibility', () => {
    it('has proper heading structure', () => {
      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      const heading = screen.getByRole('heading', { level: 1 });
      expect(heading).toHaveTextContent('incomingBatchLot');
    });

    it('has accessible buttons', () => {
      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      const createButton = screen.getByRole('button', { name: /create-harvest-log/i });
      expect(createButton).toBeInTheDocument();
    });
  });

  describe('Component State Management', () => {
    it('initializes with correct default state', () => {
      // Reset all mocks to ensure clean state
      jest.clearAllMocks();

      // Setup fresh mocks without dialog open
      const { useDeviceHeight } = require('hooks/useDeviceHeight');
      const { useGetEventStatus } = require('hooks/useGetEventStatus');
      const { useDeleteDraftMutate } = require('hooks/mutates/useDeleteDraftMutate');
      const { useToastStore } = require('store/useToastStore');
      const { useTranslations } = require('next-intl');
      const { useRouter, usePathname } = require('next/navigation');
      const { getStatus } = require('utils');

      useDeviceHeight.mockReturnValue(mockDeviceHeight);
      useGetEventStatus.mockReturnValue({ getStatusLabel: mockGetStatusLabel });
      useDeleteDraftMutate.mockReturnValue({ mutateAsync: mockMutateAsync });
      useToastStore.mockReturnValue({ toast: null, resetToast: jest.fn() });
      useTranslations.mockReturnValue(mockTranslation);
      useRouter.mockReturnValue(mockRouter);
      usePathname.mockReturnValue('/event/incoming');
      getStatus.mockReturnValue(EventStatusEnum.WAITING);
      mockGetStatusLabel.mockReturnValue({
        eventStatus: 'waiting',
        eventStatusLabel: 'Waiting',
      });

      render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );

      // Dialog should not be open initially
      expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
    });

    it('manages isIncoming state based on pathname', () => {
      const { usePathname } = require('next/navigation');

      // Test incoming path
      usePathname.mockReturnValue('/event/incoming');
      const { rerender } = render(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );
      expect(screen.getByText('incomingBatchLot')).toBeInTheDocument();

      // Test receiving path
      usePathname.mockReturnValue('/event/receiving');
      rerender(
        <TestWrapper>
          <Event />
        </TestWrapper>
      );
      expect(screen.getByText('receivingBatchLot')).toBeInTheDocument();
    });
  });
});
