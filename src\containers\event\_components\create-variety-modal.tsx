/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  IconButton,
  List,
  ListItem,
  ListItemText,
  TextField,
  Typography,
} from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { CustomDatePicker } from 'components/date-picker/custom-date-picker';
import { FormTextInput } from 'components';
import dayjs, { Dayjs } from 'dayjs';
import { useTranslations } from 'next-intl';
import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { fetchVarietiesService } from 'services/resource.service';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { theme } from 'styles/theme';
import { DurianGrade, DurianVariety, ReceiveUpdatePayload } from 'types';
import { formatNumberWithCommas, isValidNumberInput, REGEX_INPUT_NAME_FIELD } from 'utils';
import { getCookieLocale } from 'utils/cookie-client';
import emptyStateIcon from 'assets/icons/empty-state.svg';
import {
  cleanDecimalInput,
  ensureVarietyInMap,
  findVarietyAndGrade,
  hasLeadingZero,
  isValidValue,
  normalizeValue,
  processDecimalValue,
  processIntegerValue,
} from 'utils/input-grade';
import * as z from 'zod';
import Image from 'next/image';

interface VarietiesModalProps {
  open: boolean;
  onClose?: () => void;
  onSave?: (data: ReceiveUpdatePayload, varietyData: DurianVariety[]) => void;
  initialWeights?: Record<string, string>;
  initialCustomName?: string;
  varietyBloomDays?: Record<string, number>;
  cuttingDay?: Dayjs;
}
interface FormData {
  weights: Record<string, string | undefined>;
  customVarietyName?: string;
  varietyBloomDays: Record<string, number>;
}

export const CreateVarietiesModal: FC<VarietiesModalProps> = ({
  open,
  onClose,
  onSave,
  initialWeights = {},
  initialCustomName = '',
  varietyBloomDays = {},
  cuttingDay,
}) => {
  const { getVarietyLabel, getGradeLabel } = useMasterDataStore();
  const receiveTranslation = useTranslations('receive');
  const commonT = useTranslations('common');
  const formTranslation = useTranslations('form');
  const [selectedVariety, setSelectedVariety] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const locale = getCookieLocale() ?? 'th';

  const {
    data: varietiesResponse,
    isLoading: isLoadingVarieties,
    error: varietiesError,
  } = useQuery({
    queryKey: ['varieties'],
    queryFn: fetchVarietiesService,
  });

  const varieties = useMemo(() => {
    return varietiesResponse?.data || [];
  }, [varietiesResponse]);

  const createWeightsSchema = () => {
    const weightsShape: Record<string, z.ZodType<any>> = {};

    varieties.forEach((variety) => {
      variety.grades.forEach((grade) => {
        const key = `${variety.id}-${grade.id}`;
        weightsShape[key] = z.string().optional();
      });
    });

    return z.record(z.string(), z.string().optional());
  };

  const createBloomDaysSchema = () => {
    const bloomDaysShape: Record<string, z.ZodType<any>> = {};

    varieties.forEach((variety) => {
      const key = variety.id;
      bloomDaysShape[key] = z.number().nullable().optional();
    });

    return z.object(bloomDaysShape);
  };

  const formSchema = z.object({
    weights: createWeightsSchema(),
    customVarietyName: z.string().optional(),
    varietyBloomDays: createBloomDaysSchema(),
  });

  const {
    control,
    handleSubmit,
    reset,
    watch,
    setError,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      weights: initialWeights as Record<string, string | undefined>,
      customVarietyName: initialCustomName,
      varietyBloomDays: varietyBloomDays,
    },
    mode: 'onChange',
  });
  const watchField = watch();
  const weights = useWatch({
    control,
    name: 'weights',
    defaultValue: initialWeights as Record<string, string | undefined>,
  });

  useEffect(() => {
    if (open) {
      reset({
        weights: initialWeights as Record<string, string | undefined>,
        customVarietyName: initialCustomName,
        varietyBloomDays: varietyBloomDays,
      });
      if (varieties.length > 0 && !selectedVariety) {
        setSelectedVariety(varieties[0].id);
      }
      if (varietyBloomDays) {
        setSelectedVariety(Object.keys(varietyBloomDays)[0]);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  const customVarietyName = useWatch({
    control,
    name: 'customVarietyName',
    defaultValue: initialCustomName,
  });

  const prepareApiPayload = (
    formData: FormData
  ): {
    update: {
      varieties: Array<{
        id: string;
        grades: Array<{ id: string; weight: number; name?: string }>;
        flowerBloomingDay?: number;
      }>;
    };
  } => {
    const varietiesMap: Record<
      string,
      {
        id: string;
        grades: Array<{ id: string; weight: number; name?: string }>;
        flowerBloomingDay: number;
        name?: string;
      }
    > = {};

    Object.entries(formData.weights || {}).forEach(([key, value]) => {
      if (!isValidValue(value)) return;

      const { varietyId, grade } = findVarietyAndGrade(key, varieties);

      if (!varietyId || !grade) return;

      ensureVarietyInMap(varietyId, varietiesMap, formData.varietyBloomDays);

      varietiesMap[varietyId].grades.push({
        id: grade.id,
        weight: Number((value ?? '').trim()),
      });
    });

    const otherVariety = varieties.find((v) => v.value === 'other');

    if (otherVariety) {
      let otherGrade = otherVariety.grades.find((g) => g.value === 'D');

      if (!otherGrade && otherVariety.grades.length > 0) {
        otherGrade = otherVariety.grades[0];
      }

      if (otherGrade) {
        const currentName = formData.customVarietyName ?? '';
        if (currentName.trim() !== '' && varietiesMap[otherVariety.id]) {
          const _otherGrade = varietiesMap[otherVariety.id].grades.map((grade) => ({ ...grade }));
          varietiesMap[otherVariety.id] = {
            id: otherVariety.id,
            grades: _otherGrade,
            flowerBloomingDay: formData.varietyBloomDays[otherVariety.id],
            name: currentName,
          };
        }
      }
    }
    const varietiesArray = Object.values(varietiesMap);
    return {
      update: {
        varieties: varietiesArray,
      },
    };
  };

  const isOtherVariety = useCallback(
    (varietyId: string) => {
      const variety = varieties.find((v) => v.id === varietyId);
      return variety?.value === 'other';
    },
    [varieties]
  );

  const isEnableSaveButton = useMemo(() => {
    const varietyIdsFromWeights = Object.entries(watchField.weights)
      .filter(([, value]) => value !== null && value !== undefined && value !== '')
      .map(([key]) => key)
      .map((key) => key.split('-')[0]);

    const uniqueVarietyIds = [...new Set(varietyIdsFromWeights)];

    const varietyIdsWithBloomDays = Object.entries(watchField.varietyBloomDays)
      .filter(([, value]) => value !== null && value !== undefined)
      .map(([key]) => key);

    if (varietyIdsWithBloomDays.length === 0) {
      return false;
    }

    const allVarietiesHasBloomingDate = uniqueVarietyIds.every((varietyId) => {
      return varietyIdsWithBloomDays.some((id) => id.includes(varietyId));
    });

    if (!allVarietiesHasBloomingDate) {
      return false;
    }

    const hasAtLeastOneWeight = Object.values(watchField.weights).some(
      (value) => value !== '' && value !== undefined && value !== null
    );
    if (!hasAtLeastOneWeight) {
      return false;
    }

    const hasValidCustomVarietyNames = varietyIdsWithBloomDays.every((varietyId) => {
      if (isOtherVariety(varietyId)) {
        const values = Object.entries(watchField.weights)
          .filter(([key]) => key.startsWith(varietyId))
          .some(([, value]) => value !== '' && value !== undefined && value !== null);
        return watchField.customVarietyName && watchField.customVarietyName.trim() !== '' && values;
      }
      return true;
    });

    return hasValidCustomVarietyNames;
  }, [watchField, isOtherVariety]);

  const onSubmit = (data: FormData) => {
    try {
      setIsSubmitting(true);

      // Validate customVarietyName against regex pattern before submission
      if (data.customVarietyName) {
        const trimmedName = data.customVarietyName.trim();
        if (!trimmedName || !REGEX_INPUT_NAME_FIELD.test(trimmedName)) {
          setError('customVarietyName', {
            type: 'pattern',
            message: formTranslation('not-allow-characters'),
          });
          return;
        }
      }
      setSubmitError(null);

      const payload = prepareApiPayload({
        ...data,
      });
      if (payload.update.varieties.length === 0) {
        onClose?.();
        return;
      }
      if (onSave) {
        onSave(payload, varieties);
        onClose?.();
      } else {
        onClose?.();
      }
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : commonT('unknown-error'));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    onClose?.();
  };

  const handleVarietySelect = useCallback((varietyId: string) => {
    setSelectedVariety(varietyId);
  }, []);

  const checkVarietyHasData = (varietyId: string) => {
    for (const [key, value] of Object.entries(weights)) {
      if (key.startsWith(varietyId) && value && value.trim() !== '') {
        if (isOtherVariety(varietyId)) {
          const otherVariety = varieties.find((v) => v.id === varietyId && v.value === 'other');
          if (otherVariety && customVarietyName && customVarietyName.trim() !== '') {
            return true;
          }
          return false;
        }
        return true;
      }
    }
    return false;
  };
  const isLoading = isLoadingVarieties || isSubmitting;

  const renderBloomingDayInput = (variety: DurianVariety) => {
    const warningDayRange = cuttingDay?.subtract(variety?.flowerBloomingDuration ?? 0, 'day');

    return (
      <Controller
        key={`${variety.id}`}
        name={`varietyBloomDays.${variety.id}`}
        control={control}
        render={({ field }) => {
          const selectedDate = field.value ? dayjs(field.value * 1000) : null;
          const isInWarningRange =
            selectedDate &&
            warningDayRange &&
            selectedDate.isAfter(warningDayRange) &&
            selectedDate.isBefore(cuttingDay);

          return (
            <>
              <Typography sx={{ fontSize: '18px' }} variant="caption">
                {receiveTranslation('flower-blooming-day')}{' '}
                <Typography variant="caption" color="error">
                  *
                </Typography>
              </Typography>
              <CustomDatePicker
                value={dayjs(field.value * 1000 || '')}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    fullWidth: true,
                    margin: 'normal',
                    helperText: isInWarningRange
                      ? receiveTranslation('warning-varieties', {
                          varietyName: variety.label[locale] as string,
                          day: variety?.flowerBloomingDuration ?? 0,
                        })
                      : undefined,
                    FormHelperTextProps: {
                      sx: {
                        color: theme.palette.customColors.waiting,
                        fontWeight: 'bold',
                      },
                    },
                  },
                }}
                maxDate={cuttingDay || dayjs()}
                onChange={(e: dayjs.Dayjs | null) => {
                  field.onChange(e ? e.startOf('day').unix() : null);
                }}
                sx={{
                  mb: 2,
                  '& .MuiPickersInputBase-root': {
                    bgcolor: 'white',
                  },
                }}
              />
            </>
          );
        }}
      />
    );
  };

  const renderGradeInputsSide = () => {
    if (!selectedVariety) return;

    const variety = varieties.find((v) => v.id === selectedVariety);
    if (!variety) return null;

    return (
      <>
        {isOtherVariety(variety.id) && (
          <FormTextInput<FormData>
            name="customVarietyName"
            required
            requiredMessage={formTranslation('required', {
              formName: 'Variety name',
            })}
            pattern={REGEX_INPUT_NAME_FIELD}
            patternMessage={formTranslation('not-allow-characters')}
            errors={errors}
            control={control}
            placeholder={receiveTranslation('enter-variety-name')}
            maxLength={100}
            label={commonT('variety-name')}
            sx={{
              mb: 2,
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'white',
              },
            }}
          />
        )}
        {renderBloomingDayInput(variety)}
        {variety.grades.map((grade) => renderGradeInput(variety, grade))}
      </>
    );
  };

  const renderGradeInput = (variety: DurianVariety, grade: DurianGrade) => {
    return (
      <Controller
        key={`${variety.id}-${grade.id}`}
        name={`weights.${variety.id}-${grade.id}`}
        control={control}
        render={({ field }) => (
          <>
            <Typography sx={{ fontSize: '18px', mb: 1 }} variant="caption">
              {getGradeLabel(grade.id)}{' '}
            </Typography>
            <TextField
              {...field}
              fullWidth
              value={formatNumberWithCommas(field.value)}
              variant="outlined"
              margin="normal"
              error={!!errors.weights?.[`${variety.id}-${grade.id}`]}
              helperText={errors.weights?.[`${variety.id}-${grade.id}`]?.message}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                const rawValue = e.target.value.replace(/,/g, '');

                // Early return for empty value
                if (!rawValue) {
                  field.onChange('');
                  return;
                }

                // Early return for invalid input or leading zeros
                if (!isValidNumberInput(rawValue) || hasLeadingZero(rawValue)) {
                  return;
                }

                // Clean and normalize the value
                const cleanValue = cleanDecimalInput(rawValue);
                const processedValue = normalizeValue(cleanValue);

                // Process based on whether it has a decimal point
                const hasDot = processedValue.includes('.');
                const finalValue = hasDot ? processDecimalValue(processedValue) : processIntegerValue(processedValue);

                // Early return if validation failed
                if (finalValue === null) {
                  return;
                }

                // Update field value
                field.onChange(finalValue);
              }}
              onBlur={field.onBlur}
              disabled={isSubmitting}
              sx={{
                mb: 2,
                '& .MuiOutlinedInput-root': {
                  bgcolor: 'white',
                },
              }}
            />
          </>
        )}
      />
    );
  };

  const renderVarietyColor = (id: string) => {
    if (selectedVariety === id) {
      return theme.palette.customColors.blueHighlight;
    }
    return 'text.primary';
  };

  const renderVarietyBgColor = (id: string) => {
    if (selectedVariety === id) {
      return theme.palette.customColors.lightGray;
    }
    return 'rgba(0, 0, 0, 0.04)';
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      slotProps={{
        paper: {
          sx: { borderRadius: 1 },
        },
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 2,
          pb: 1,
        }}
      >
        <Typography component="div" variant="h6">
          {receiveTranslation('specify-durian-weight-by-variety')}
        </Typography>
        <IconButton edge="end" onClick={handleClose} aria-label="close" disabled={isLoading}>
          <CloseIcon />
        </IconButton>
      </Box>

      {varietiesError && (
        <Box sx={{ p: 2, bgcolor: theme.palette.error.light, color: theme.palette.error.main }}>
          <Typography variant="body2">
            Error loading varieties:{' '}
            {varietiesError instanceof Error ? varietiesError.message : commonT('unknown-error')}
          </Typography>
        </Box>
      )}

      <DialogContent dividers sx={{ p: 0 }}>
        {isLoading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
            <CircularProgress />
          </Box>
        )}

        {!isLoading && (
          <Box component="form" sx={{ display: 'flex', height: '100%', px: 0 }}>
            <Box sx={{ width: '40%', borderRight: '0px solid #e0e0e0', p: 2 }}>
              <Typography variant="subtitle1" sx={{ mb: 1, fontSize: '16px', fontWeight: 500 }}>
                {receiveTranslation('varieties')}
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 2 }}>
                {receiveTranslation('varieties-description')}
              </Typography>

              <List sx={{ pt: 0, mx: -2 }}>
                {varieties.map((variety) => (
                  <ListItem
                    key={variety.id}
                    disablePadding
                    sx={{
                      padding: '16px 24px',
                      mb: 0.5,
                      bgcolor: selectedVariety === variety.id ? theme.palette.customColors.lightGray : 'transparent',
                      color: renderVarietyColor(variety.id),
                      '&:hover': {
                        bgcolor: renderVarietyBgColor(variety.id),
                        cursor: 'pointer',
                      },
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      alignSelf: 'stretch',
                      position: 'relative',
                      pl: 3,
                      transition: 'all 0.2s ease-in-out',
                      opacity: 1,
                      pointerEvents: 'auto',
                    }}
                    onClick={() => {
                      handleVarietySelect(variety.id);
                    }}
                  >
                    <ListItemText
                      primary={getVarietyLabel(variety.id)}
                      slotProps={{
                        primary: {
                          sx: {
                            color:
                              selectedVariety === variety.id ? theme.palette.customColors.blueHighlight : 'inherit',
                            fontWeight: selectedVariety === variety.id ? 500 : 400,
                          },
                        },
                      }}
                    />
                    {checkVarietyHasData(variety.id) && (
                      <CheckIcon
                        sx={{
                          color: theme.palette.customColors.blueHighlight,
                          fontSize: '1.2rem',
                        }}
                      />
                    )}
                  </ListItem>
                ))}
              </List>
            </Box>
            <Box sx={{ width: '60%', p: 2, bgcolor: theme.palette.customColors.lightGray }}>
              <Typography variant="subtitle1" sx={{ mb: 1 }}>
                {receiveTranslation('grade-weight')}
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 2 }}>
                {receiveTranslation('grade-help-text')}
              </Typography>

              <Box sx={{ mt: 2 }}>
                {selectedVariety && renderGradeInputsSide()}

                {!selectedVariety && varieties.length > 0 && (
                  <Box
                    component="div"
                    sx={{
                      width: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      flexDirection: 'column',
                      height: '100%',
                      mt: 16,
                    }}
                  >
                    <Image src={emptyStateIcon} width={160} height={160} unoptimized alt="empty-state-icon" />
                    <Typography color="text.secondary" sx={{ mt: 4, textAlign: 'center', fontSize: '14px' }}>
                      {receiveTranslation('varieties-help-text')}
                    </Typography>
                  </Box>
                )}

                {varieties.length === 0 && !isLoading && (
                  <Typography color="text.secondary" sx={{ mt: 4, textAlign: 'center' }}>
                    {receiveTranslation('no-varieties-available')}
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>
        )}
      </DialogContent>

      {submitError && (
        <Box sx={{ p: 2, bgcolor: theme.palette.error.light, color: theme.palette.error.main }}>
          <Typography variant="body2">{submitError}</Typography>
        </Box>
      )}

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button
          onClick={handleClose}
          variant="outlined"
          disabled={isLoading}
          sx={{
            minWidth: '140px',
            px: 3,
            py: 1,
            fontSize: '0.875rem',
            fontWeight: 500,
          }}
        >
          {commonT('cancel-modal-btn')}
        </Button>
        <Button
          onClick={handleSubmit(onSubmit)}
          variant="contained"
          color="primary"
          disabled={isLoading || !varietiesResponse || !isEnableSaveButton}
          startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : null}
          sx={{
            minWidth: '140px',
            px: 3,
            py: 1,
            fontSize: '0.875rem',
            fontWeight: 500,
          }}
        >
          {commonT('save-modal-btn')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
