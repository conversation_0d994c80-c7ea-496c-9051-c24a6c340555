import { FormControl, TextareaAutosize, Typography, useTheme } from '@mui/material';
import { JSX, ReactElement } from 'react';
import { Control, Controller, FieldErrors, FieldValues, Path } from 'react-hook-form';

export interface FormTextInputAreaProps<TFormValues extends FieldValues> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  errors?: FieldErrors<TFormValues>;
  label?: string;
  required?: boolean;
  requiredMessage?: string;
  placeholder?: string;
  patternMessage?: string;
  pattern?: RegExp;
  minLength?: number;
  maxLength?: number;
  minLengthMessage?: string;
  maxLengthMessage?: string;
  validate?: Record<string, (value: unknown) => boolean | string>;
  fullWidth?: boolean;
  customBtn?: JSX.Element;
  minRows?: number;
  maxRows?: number;
}

export function FormTextInputArea<TFormValues extends FieldValues>({
  name,
  control,
  errors,
  label,
  required = false,
  requiredMessage,
  placeholder,
  pattern,
  patternMessage,
  minLength,
  maxLength = 100,
  minLengthMessage,
  maxLengthMessage,
  validate,
  fullWidth = true,
  customBtn,
  minRows,
  maxRows,
}: Readonly<FormTextInputAreaProps<TFormValues>>): ReactElement {
  const rules = {
    ...(required && { required: requiredMessage ?? `${label} is required` }),
    ...(pattern && { pattern: { value: pattern, message: patternMessage ?? `Invalid format for ${label}` } }),
    ...(minLength && {
      minLength: { value: minLength, message: minLengthMessage ?? `${label} must be at least ${minLength} characters` },
    }),
    ...(maxLength && {
      maxLength: { value: maxLength, message: maxLengthMessage ?? `${label} must not exceed ${maxLength} characters` },
    }),
    ...(validate && { validate }),
  };

  const theme = useTheme();

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field }) => {
        return (
          <FormControl fullWidth={fullWidth} error={!!errors?.[name]}>
            {label && (
              <Typography sx={{ fontSize: '18px', mb: 1 }} variant="caption">
                {label}{' '}
                <Typography sx={{ visibility: required ? 'visible' : 'hidden' }} variant="caption" color="error">
                  *
                </Typography>
              </Typography>
            )}

            <TextareaAutosize
              style={{
                padding: '12px',
                borderRadius: '4px',
                borderColor: errors?.[name] ? theme.palette.error.main : undefined,
                resize: 'vertical',
                overflow: 'auto',
                whiteSpace: 'pre-wrap',
                height:'50px'
              }}
              minRows={minRows}
              maxRows={maxRows}
              required={required}
              name={name}
              placeholder={placeholder}
              value={field.value}
              onChange={(event) => {
                if (maxLength && event.target.value.length > maxLength) {
                  return;
                }
                field.onChange(event.target.value);
              }}
            />
            {customBtn}
          </FormControl>
        );
      }}
    />
  );
}
