'use client';

import { FC, ReactNode } from 'react';

import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { Box, Typography } from '@mui/material';

interface NavigationBackProps {
  content: string;
  onBack?: () => void;
  renderSuffix?: () => ReactNode;
}

export const NavigationBack: FC<NavigationBackProps> = ({
  content,
  onBack,
  renderSuffix,
}) => {
  return (
    <Box
      id='navigation-back-component'
      sx={{
        display: 'flex',
        alignItems: 'center',
        cursor: 'pointer',
        mb: 'var(--layout-gap)',
        background: '#fff',
        justifyContent: 'space-between',
        width: '100%',
      }}
      component='div'
      onClick={onBack}
    >
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <ArrowBackIcon sx={{ fontSize: '16px' }} />
        <Typography ml='8px' fontSize='16px' fontWeight='bold'>
          {content}
        </Typography>
      </Box>
      {renderSuffix && renderSuffix()}
    </Box>
  );
};
