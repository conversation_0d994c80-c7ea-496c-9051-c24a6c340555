/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { DeleteOutlineOutlined } from '@mui/icons-material';
import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  Divider,
  Grid,
  IconButton,
  Stack,
  SxProps,
  Typography,
} from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC, useEffect, useMemo, useState } from 'react';
import { colors } from 'styles/colors';
import Image from 'next/image';
import dateSvg from 'assets/icons/calendar.svg';
import boxSvg from 'assets/icons/product-type.svg';
import qrOnlySvg from 'assets/icons/qr-only.svg';
import qrLabelSvg from 'assets/icons/qr-label.svg';
import { useGetEventStatus } from 'hooks/useGetEventStatus';
import { renderStatus } from 'containers/event/_components';
import { useQrDetailQuery } from 'hooks/queries/useQrDetailQuery';
import { EventStatusEnum, QrDetail } from 'types';
import { formatDate } from 'utils';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { useGenerateQrUrlMutate } from 'hooks/queries/useGenerateQrUrlMutate';
import QrCodeReview from 'components/qr-code-review/qr-code-review';
import { formatOrchardNo } from 'utils/format';

type ViewQrDialogState = {
  open: boolean;
  viewedId: string;
  onDelete?: () => void;
};

const initialViewQrDialogState: ViewQrDialogState = {
  open: false,
  viewedId: '',
};

export const useViewQrDialogProps = () => {
  const [dialogState, setDialogState] = useState<ViewQrDialogState>(initialViewQrDialogState);

  const onOpen = (state: Omit<ViewQrDialogState, 'open'>) => {
    setDialogState({ open: true, ...state });
  };

  const onClose = () => {
    setDialogState(initialViewQrDialogState);
  };

  return {
    dialogState,
    onOpen,
    onClose,
  };
};

type Props = Omit<ReturnType<typeof useViewQrDialogProps>, 'onOpen'>;

export const ViewQrDialog: FC<Props> = (props) => {
  const { dialogState, onClose } = props;

  const [url, setUrl] = useState('');

  const { data, isFetching } = useQrDetailQuery(dialogState.viewedId);
  const { mutate } = useGenerateQrUrlMutate();

  useEffect(() => {
    if (!data) return;
    mutate(
      {
        quantity: 1,
        nameOfExportingCompany: [data.nameOfExportingCompany || ''],
      },
      {
        onSuccess: (res) => {
          setUrl(res[0].qrUrl);
        },
      }
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.nameOfExportingCompany]);

  const renderDialogContent = () => {
    if (isFetching || !data || !url) {
      return (
        <>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              p: 2,
              pb: 1,
            }}
          >
            <Typography component="div" variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              QR--
            </Typography>
            <IconButton edge="end" onClick={onClose} aria-label="close" disabled={isFetching}>
              <CloseIcon />
            </IconButton>
          </Box>

          <DialogContent dividers sx={{ px: 3, border: 0 }}>
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '480px' }}>
              <CircularProgress />
            </Box>
          </DialogContent>
        </>
      );
    }

    return <ViewQrDialogInner {...props} qrDetail={data} url={url} />;
  };

  return (
    <Dialog
      open={dialogState.open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      slotProps={{
        paper: {
          sx: { borderRadius: 1 },
        },
      }}
    >
      {renderDialogContent()}
    </Dialog>
  );
};

const ViewQrDialogInner: FC<
  Props & {
    qrDetail: QrDetail;
    url: string;
  }
> = (props) => {
  const { dialogState, onClose, qrDetail, url } = props;

  const qrT = useTranslations('qr');

  const { getStatusLabel } = useGetEventStatus();
  const { getProductTypeLabel } = useMasterDataStore();
  const { eventStatus, eventStatusLabel } = getStatusLabel(qrDetail.status ?? 'available', 'qr');

  const qrExportData = useMemo(
    () => ({
      qrUrl: url,
      exportCompany: qrDetail.nameOfExportingCompany,
      orchardRegisterNumber: `AC ${formatOrchardNo(qrDetail.orchardRegisterNumber)}`,
      packingDate: formatDate(qrDetail.packingDate * 1000),
      boxType: getProductTypeLabel(qrDetail.productType),
      exportTo: qrDetail.exportTo ?? '',
      packingHouseRegisterNumber: qrDetail.packingHouseDoaNumber ?? '',
      batchNumber: qrDetail.batchlot ?? '',
      productOf: 'Thailand',
      brand: 'Fresh Durian',
      variety: '',
      grade: '',
      totalBoxes: '',
      netWeight: '',
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 2,
          pb: 1,
        }}
      >
        <Typography component="div" variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {qrDetail.qrcodeId || 'QR--'} {renderStatus(eventStatus, eventStatusLabel)}
        </Typography>
        <IconButton edge="end" onClick={onClose} aria-label="close">
          <CloseIcon />
        </IconButton>
      </Box>

      <DialogContent dividers sx={{ px: 3, border: 0 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Grid container spacing={0.5}>
            <Grid size={6}>
              <Box sx={boxContentSx}>
                <Image src={dateSvg} alt="" width={16} height={16} />
                {qrT('packing-date')}: {formatDate(qrDetail.packingDate * 1000)}
              </Box>
            </Grid>
            <Grid size={6}>
              <Box sx={boxContentSx}>
                <Image src={boxSvg} alt="" width={16} height={16} />
                {qrT('product-type')}: {getProductTypeLabel(qrDetail.productType)}
              </Box>
            </Grid>
            {(qrDetail.description ?? '').trim().length ? (
              <Grid size={12}>
                <Box sx={boxContentSx}>{qrDetail.description}</Box>
              </Grid>
            ) : null}
          </Grid>
          <Divider />
          <Box>
            <QrCodeReview.Container data={qrExportData} capturedEventName="qr_management_qr_code_review">
              <QrCodeReview.Label />
              <Stack
                direction="row"
                spacing={1}
                sx={{
                  mt: 2,
                  width: '100%',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <Button
                  onClick={dialogState.onDelete}
                  variant="outlined"
                  color="error"
                  size="large"
                  disabled={qrDetail.status === EventStatusEnum.ASSIGNED}
                  sx={{
                    width: '120px',
                    height: '47px',
                    background: colors.error50,
                  }}
                >
                  <DeleteOutlineOutlined fontSize="small" />
                </Button>
                <QrCodeReview.Download
                  startIcon={<Image src={qrLabelSvg} alt="" width={22} height={22} />}
                  sx={{
                    flex: 1,
                    background: colors.primary50,
                    height: '47px',
                  }}
                />
                <QrCodeReview.DownloadQrOnly
                  startIcon={<Image src={qrOnlySvg} alt="" width={22} height={22} />}
                  sx={{
                    flex: 1,
                    background: colors.primary50,
                    height: '47px',
                  }}
                />
              </Stack>
            </QrCodeReview.Container>
          </Box>
        </Box>
      </DialogContent>
    </>
  );
};

const boxContentSx: SxProps = {
  background: colors.primary50,
  borderRadius: '2px',
  padding: '4px 8px',
  display: 'flex',
  alignItems: 'center',
  gap: '4px',
  color: colors.neutral500,
  fontSize: '14px',
  fontWeight: 400,
};
