import { Box, Button, Typography } from '@mui/material';
import UploadCutterImage from './upload-cutter-img';
import { TextInput } from 'components';
import { useTranslations } from 'next-intl';
import { Cutter } from 'types';
import { FC, useState } from 'react';
import { activeButtonSelectStyle, normalButtonSelectStyle } from 'components/filter-table/filter-table.styles';

interface CreateCutterProps {
  cutter: Cutter;
  setCutter: (cutter: Cutter) => void;
  onPhoneNumberErrorChange?: (hasError: boolean) => void;
}

export const CreateCutter: FC<CreateCutterProps> = ({ cutter, setCutter, onPhoneNumberErrorChange }) => {
  const receivingTranslation = useTranslations('receive');
  const [phoneNumberError, setPhoneNumberError] = useState<string>('');

  const validateThaiPhoneNumber = (phoneNumber: string): boolean => {
    // Pattern 1: Start with 0 + 9 digits (total 10 digits)
    if (/^0\d{9}$/.test(phoneNumber)) {
      return true;
    }

    // Pattern 2: Start with "+66" + 9 digits
    if (/^\+66\d{9}$/.test(phoneNumber)) {
      return true;
    }

    // Pattern 3: Start with "66" + 9 digits
    if (/^66\d{9}$/.test(phoneNumber)) {
      return true;
    }

    return false;
  };

  const hasLicenseOptions = [
    {
      label: receivingTranslation('registered-cutter'),
      value: 'yes',
    },
    {
      label: receivingTranslation('unregistered-cutter'),
      value: '',
    },
  ];

  const renderHasLicenseSelection = () => {
    return hasLicenseOptions.map((option) => (
      <Button
        key={option.value}
        sx={cutter.isCertified === Boolean(option.value) ? activeButtonSelectStyle : normalButtonSelectStyle}
        onClick={() => setCutter({ ...cutter, isCertified: Boolean(option.value) })}
      >
        {option.label}
      </Button>
    ));
  };
  return (
    <Box>
      <UploadCutterImage
        image={cutter.avatar?.filenameDisk}
        onChange={(imgString) => {
          setCutter({
            ...cutter,
            avatar: {
              ...cutter.avatar,
              filenameDisk: imgString,
              id: cutter.avatar?.id ?? '',
              filenameDownload: cutter.avatar?.filenameDownload ?? '',
            },
          });
        }}
      />
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Typography fontWeight="bold" sx={{ fontSize: '18px', mb: { xs: 2, sm: 0 } }}>
          {receivingTranslation('general-information')}
        </Typography>
        <Box sx={{ display: 'flex', gap: 0.25, flexDirection: 'column' }}>
          <Typography sx={{ fontSize: '18px' }} variant="caption">
            {receivingTranslation('cutter-phone-number')}
            <Typography sx={{ visibility: 'visible' }} variant="caption" color="error">
              *
            </Typography>
          </Typography>

          <Box sx={{ display: 'flex', gap: '12px', flexDirection: 'column' }}>
            <TextInput
              required={true}
              value={cutter.phoneNumber ?? ''}
              onChange={(event) => {
                const inputValue = event.target.value;

                // Allow only digits and + (no spaces)
                const filteredValue = inputValue.replace(/[^\d+]/g, '');

                setCutter({
                  ...cutter,
                  phoneNumber: filteredValue,
                });

                // Validate the phone number
                if (filteredValue !== '' && !validateThaiPhoneNumber(filteredValue)) {
                  setPhoneNumberError(receivingTranslation('invalid-thai-phone-number'));
                  onPhoneNumberErrorChange?.(true);
                } else {
                  setPhoneNumberError('');
                  onPhoneNumberErrorChange?.(false);
                }
              }}
            />
            {phoneNumberError && (
              <Typography variant="caption" color="error" sx={{ fontSize: '12px', mt: 0.5 }}>
                {phoneNumberError}
              </Typography>
            )}
          </Box>
        </Box>
        <Box sx={{ display: 'flex', gap: 0.25, flexDirection: 'column' }}>
          <Typography sx={{ fontSize: '18px' }} variant="caption">
            {receivingTranslation('cutter-name')}
            <Typography sx={{ visibility: 'visible' }} variant="caption" color="error">
              *
            </Typography>
          </Typography>

          <Box sx={{ display: 'flex', gap: '12px' }}>
            <TextInput
              required={true}
              value={cutter.name ?? ''}
              onChange={(event) => {
                const inputValue = event.target.value;
                const filteredValue = inputValue.replace(/[^a-zA-Z0-9\sก-๙]/g, '');
                setCutter({
                  ...cutter,
                  name: filteredValue,
                });
              }}
              maxLength={100}
            />
          </Box>
        </Box>

        <Box sx={{ display: 'flex', gap: 0.25, flexDirection: 'column' }}>
          <Typography sx={{ fontSize: '18px' }} variant="caption">
            {receivingTranslation('cutter-license-question')}
          </Typography>

          <Box sx={{ display: 'flex', gap: '12px' }}>{renderHasLicenseSelection()}</Box>
        </Box>
      </Box>
    </Box>
  );
};
