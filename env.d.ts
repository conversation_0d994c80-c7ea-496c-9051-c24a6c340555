declare namespace NodeJS {
  interface ProcessEnv {
    NEXT_PUBLIC_APP_VERSION?: string;
    NEXT_PUBLIC_ASSET_DOMAIN?: string;
    NEXT_PUBLIC_PATH?: string;
    NEXT_PUBLIC_ASSET_HOST?: string;

    NEXT_PUBLIC_FIREBASE_API_KEY?: string;
    NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN?: string;
    NEXT_PUBLIC_FIREBASE_PROJECT_ID?: string;
    NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET?: string;
    NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID?: string;
    NEXT_PUBLIC_FIREBASE_APP_ID?: string;
    NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID?: string;
    NEXT_PUBLIC_FIREBASE_VAPID_KEY?: string;
    NEXT_PUBLIC_ENV?: 'development' | 'staging' | 'production';
    NEXT_PUBLIC_GTAG_ID?: string;
  }
}
