'use client';

import { <PERSON>, But<PERSON>, Divider, Fade, Modal, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { FormProvider, useForm } from 'react-hook-form';

import { Image, PhoneNumberInput } from 'components';
import { LoginStepEnum, useAuthStore } from 'store/useAuthStore';
import { delayPromise, isValidThaiNumber } from 'utils';
import toastMessages from 'utils/toastMessages';
import { OtpInputStep } from '../_components/OtpInputStep';
import { useCallback, useEffect, useState } from 'react';
import { clientRoutes } from 'routes/client-routes';
import { AxiosError } from 'axios';
import logo from 'assets/icons/logo.svg';
import { get, isNaN } from 'lodash-es';
import { theme } from 'styles/theme';
import { PDPA as Pdpa } from 'containers/pdpa';
import Link from 'next/link';
import { basePathProd } from 'configs/app-config';
import { CookieModal } from 'containers/cookie';
import { Close } from '@mui/icons-material';
import textLogo from 'assets/icons/text-logo.svg';
import { useSearchParams } from 'next/navigation';

type LoginFormValues = {
  phone: string;
};

const LoginPage = () => {
  const {
    loginStep,
    setLoginStep,
    papdAccept,
    otp,
    loginByOtp,
    checkStatus,
    phoneNumber,
    setErrorOtp,
    requestOtp,
    updateStatus,
    resetState,
    setRemainingTime,
    setShowCountDown,
    logout,
  } = useAuthStore();

  const commonT = useTranslations('common');
  const authT = useTranslations('auth');
  const logoSize = 40;
  const [loading, setLoading] = useState(false);
  const [isAcceptCookieConsent, setIsAcceptCookieConsent] = useState<boolean>(true);
  const [isCookieConsentDocShow, setIsCookieConsentDocShow] = useState<boolean>(false);

  useEffect(() => {
    return () => {
      resetState();
    };
  }, [resetState]);

  useEffect(() => {
    const isAcceptConsent = localStorage.getItem('isAcceptConsent');
    if (!isAcceptConsent) {
      setIsAcceptCookieConsent(false);
    }
  }, []);

  const form = useForm<LoginFormValues>({
    defaultValues: {
      phone: '',
    },
    mode: 'onSubmit',
  });

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = form;

  const disableLoginOtp = loginStep === LoginStepEnum.RequestOtp && otp.length < 6;

  const params = useSearchParams();

  const callBackUrl = params?.get('callbackUrl') || clientRoutes.home;

  const onSubmit = async (data: LoginFormValues) => {
    if (loginStep === LoginStepEnum.VerifyPhone) {
      const isAccepted = await checkStatus(data.phone, authT('phone-number-not-found'));

      if (isAccepted) {
        await handleRequestOtp(data?.phone, true);
        setLoginStep(LoginStepEnum.RequestOtp, data?.phone || '');
        localStorage.removeItem('isWelcome');
        return;
      }
      setLoginStep(LoginStepEnum.VerifyPapd, data.phone);
      localStorage.setItem('isWelcome', 'true');
      return;
    }
    if (loginStep === LoginStepEnum.VerifyPapd) {
      await handleUpdateStatus(phoneNumber, true);
      await handleRequestOtp(phoneNumber, true);
      setLoginStep(LoginStepEnum.RequestOtp);
      return;
    }
    await handleLoginOtp();
  };

  const handleLoginOtp = async () => {
    try {
      setLoading(true);
      await loginByOtp(otp);

      if (typeof window !== 'undefined') {
        if (callBackUrl && callBackUrl !== clientRoutes.home) {
          window.location.href = callBackUrl;
          return;
        }

        window.location.href = basePathProd + clientRoutes.home;
      }
    } catch (error) {
      handleLoginError(error);
    }
  };

  const handleLoginError = async (error: unknown) => {
    if (error instanceof AxiosError) {
      setErrorOtp(true);
      if (error.status === 422) {
        toastMessages.error(authT('otp-error'));
      } else if (error.status === 410) {
        toastMessages.error(authT('otp-expired'));
      }
      await delayPromise(3000);
      setErrorOtp(false);
      setLoading(false);
    }
  };

  const handleRemainingTime = useCallback(
    (error: AxiosError) => {
      const remainingTime = get(error, 'response.data.details.remaining_time');

      if (remainingTime && !isNaN(remainingTime)) {
        setRemainingTime(remainingTime);
        setShowCountDown(true);
      }
    },
    [setRemainingTime, setShowCountDown]
  );

  const showRateLimitMessage = useCallback(
    (error: AxiosError) => {
      const remainingTime = get(error, 'response.data.details.remaining_time');
      const remainingMinutes = remainingTime && !isNaN(remainingTime) ? Math.floor(Number(remainingTime) / 60) : 15;

      toastMessages.error(authT('otp-send-exceed-time', { remainingTime: remainingMinutes }));
    },
    [authT]
  );

  const handleGeneralErrors = useCallback(
    (error: AxiosError, status: number | undefined) => {
      handleRemainingTime(error);

      if (status === 429) {
        showRateLimitMessage(error);
      }
    },
    [handleRemainingTime, showRateLimitMessage]
  );

  const getPhoneCheckingHandlers = useCallback(
    (error: AxiosError) => ({
      404: () => {
        toastMessages.error(authT('phone-number-not-found'));
        throw error;
      },
      403: () => {
        toastMessages.error(authT('invalid-phone-number'));
        throw error;
      },
      429: () => {
        handleRemainingTime(error);
        showRateLimitMessage(error);
      },
    }),
    [authT, handleRemainingTime, showRateLimitMessage]
  );

  const handlePhoneCheckingErrors = useCallback(
    (error: AxiosError, status: number | undefined) => {
      const handlers = getPhoneCheckingHandlers(error);
      const handler = handlers[status as keyof typeof handlers];

      if (handler) {
        handler();
      } else {
        handleGeneralErrors(error, status);
      }
    },
    [getPhoneCheckingHandlers, handleGeneralErrors]
  );

  const handleAxiosError = useCallback(
    (error: AxiosError, isExistPhoneChecking: boolean) => {
      const status = error?.status;

      if (isExistPhoneChecking) {
        handlePhoneCheckingErrors(error, status);
      } else {
        handleGeneralErrors(error, status);
      }
    },
    [handlePhoneCheckingErrors, handleGeneralErrors]
  );

  const handleOtpError = useCallback(
    (error: unknown, isExistPhoneChecking: boolean) => {
      if (error instanceof AxiosError) {
        handleAxiosError(error, isExistPhoneChecking);
      } else {
        toastMessages.error(authT('otp-send-fail'));
      }
    },
    [authT, handleAxiosError]
  );

  const handleRequestOtp = useCallback(
    async (phone: string, isExistPhoneChecking: boolean = false) => {
      if (!phone) return;

      try {
        await requestOtp(phone);
        toastMessages.success(authT('otp-send-success'));
      } catch (error) {
        handleOtpError(error, isExistPhoneChecking);
      }
    },
    [requestOtp, authT, handleOtpError]
  );

  const handleUpdateStatus = useCallback(
    async (phone: string, isExistPhoneChecking: boolean = false) => {
      try {
        if (!phone) return;

        await updateStatus(phone, isExistPhoneChecking);
      } catch (error) {
        if (error instanceof AxiosError) {
          if (isExistPhoneChecking && error?.status === 404) {
            toastMessages.error(authT('phone-number-not-found'));
            setTimeout(() => {
              logout();
            }, 3000);
            return;
          }
        }

        toastMessages.error(authT('otp-send-fail'));
        logout();
        return;
      }
    },
    [authT, logout, updateStatus]
  );

  const renderButtonTitle = () => {
    if (loginStep === LoginStepEnum.VerifyPhone) {
      return commonT('login-title');
    }

    return authT('verify-otp');
  };

  const handleDeclined = () => {
    setIsAcceptCookieConsent(true);
    localStorage.setItem('isAcceptConsent', 'true');
  };
  const handleAccept = () => {
    setIsAcceptCookieConsent(true);
    localStorage.setItem('isAcceptConsent', 'true');
  };

  return (
    <>
      <Box component="div" sx={{ padding: '0 20px', width: '100%' }}>
        <FormProvider {...form}>
          <Box
            component="form"
            onSubmit={handleSubmit(onSubmit)}
            sx={{
              width: '100%',
              display: 'flex',
              flexDirection: 'column',
              gap: 1,
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSubmit(onSubmit)();
              }
            }}
          >
            {loginStep === LoginStepEnum.VerifyPhone && (
              <Box sx={{ width: '100%', position: 'relative' }}>
                <Typography sx={{ fontSize: '18px', mb: 2 }} variant="caption">
                  {commonT('phone')}{' '}
                  <Typography variant="caption" color="error">
                    *
                  </Typography>
                </Typography>
                <PhoneNumberInput
                  name="phone"
                  fullWidth
                  margin="normal"
                  placeholder={commonT('phone-number-placeholder')}
                  helperText={authT('invalid-phone-format')}
                  rules={{
                    required: authT('require-phone'),
                    validate: (val: string) => isValidThaiNumber(val) || commonT('invalid-phone-number'),
                  }}
                />
              </Box>
            )}

            {loginStep === LoginStepEnum.RequestOtp && (
              <Fade in={true}>
                <Box sx={{ width: '100%' }}>
                  <OtpInputStep handleRequestOtp={handleRequestOtp} />
                </Box>
              </Fade>
            )}

            <Box
              component="div"
              sx={{ width: '100%', display: 'flex', justifyContent: 'space-between', my: '24px', gap: '12px' }}
            >
              <Button
                fullWidth
                type="submit"
                variant="contained"
                color="primary"
                disabled={isSubmitting || disableLoginOtp}
                loading={isSubmitting || loading}
              >
                {renderButtonTitle()}
              </Button>
            </Box>
          </Box>
        </FormProvider>
      </Box>
      <Modal open={!papdAccept} aria-labelledby="modal-modal-title" aria-describedby="modal-modal-description">
        <Box
          sx={{
            position: 'absolute',
            top: '0',
            left: '0',
            right: '0',
            bottom: '0',
            width: '100vw',
            height: '100dvh',
            bgcolor: 'background.paper',
            border: 'none',
            gap: 2,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              gap: 1,
              alignItems: 'center',
              height: 80,
              width: '100%',
              backgroundColor: 'transparent',
              justifyContent: 'start',
              position: 'relative',
              ml: 4,
              boxSizing: 'content-box',
            }}
          >
            <Image style={{ objectFit: 'contain' }} src={logo} width={logoSize} height={logoSize} alt="logo" />
            <Image style={{ objectFit: 'contain' }} src={textLogo} width={120} height={30} alt="title-text" />
          </Box>
          <Divider/>
          <Pdpa onDeclined={logout} onAccept={onSubmit} />
        </Box>
      </Modal>
      <Modal
        open={isCookieConsentDocShow}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box
          sx={{
            position: 'absolute',
            top: '0',
            left: '0',
            right: '0',
            bottom: '0',
            width: '100vw',
            height: '100h',
            bgcolor: 'background.paper',
            border: 'none',
            gap: 2,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              gap: 1,
              alignItems: 'center',
              height: 64,
              width: '100%',
              background: theme.palette.customColors.gradientAppBgColor,
              transition: theme.transitions.create(['opacity', 'width'], {
                easing: theme.transitions.easing.easeInOut,
                duration: theme.transitions.duration.complex,
              }),
              justifyContent: 'start',
              paddingLeft: 4,
              position: 'relative',
            }}
          >
            <Image internalAsset unoptimized src={logo} width={logoSize} height={logoSize} alt="logo" />
            <Typography
              variant="body1"
              fontWeight={600}
              fontSize={18}
              color="white"
              noWrap
              sx={{
                transition: theme.transitions.create(['opacity', 'width'], {
                  easing: theme.transitions.easing.easeInOut,
                  duration: theme.transitions.duration.complex,
                }),
              }}
            >
              {commonT('app-name')}
            </Typography>
          </Box>
          <Box sx={{ width: '100%', display: 'flex', justifyContent: 'flex-end', p: 4 }}>
            <Close
              onClick={() => {
                setIsCookieConsentDocShow(false);
              }}
            />
          </Box>
          <CookieModal />
        </Box>
      </Modal>
      <Modal
        open={!isAcceptCookieConsent}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
        sx={{ border: 'none', outline: 'none' }}
      >
        <Box
          sx={{
            position: 'absolute',
            left: '0',
            right: '0',
            bottom: '0',
            width: '100vw',
            height: '30vh',
            bgcolor: 'background.paper',
            display: 'flex',
            justifyContent: 'center',
            alignContent: 'center',
            gap: 2,
            border: 'none',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              gap: 4,
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              width: '80vw',
            }}
          >
            <Box
              sx={{ flex: 1, display: 'flex', flexDirection: 'column', alignItems: 'start', justifyContent: 'center' }}
            >
              <Typography sx={{ fontWeight: 'bold' }}>{commonT('cookie-consent')}</Typography>
              <Typography sx={{ fontSize: '12px' }}>{commonT('cookie-consent-desc')}</Typography>
              <Link
                onClick={() => {
                  setIsCookieConsentDocShow(true);
                }}
                href=""
                style={{ fontSize: '12px' }}
              >
                {commonT('cookie-consent')}
              </Link>
            </Box>
            <Box sx={{ flex: 1, display: 'flex', gap: 1 }}>
              <Button onClick={handleDeclined} variant="outlined" sx={{ width: '200px' }}>
                {commonT('decline')}
              </Button>
              <Button onClick={handleAccept} variant="contained" sx={{ width: '200px' }}>
                {commonT('accept-all-cookies')}
              </Button>
            </Box>
          </Box>
        </Box>
      </Modal>
    </>
  );
};

export default LoginPage;
