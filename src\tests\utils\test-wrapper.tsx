import { CssBaseline } from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { FC, ReactNode } from 'react';

import { QueryClientProvider } from '@tanstack/react-query';
import ClientLocalizationProvider from 'configs/client-localization-provider';
import queryClient from 'configs/query-client';
import { customToast } from 'layouts/main/components/toast';
import { SnackbarProvider } from 'notistack';

const theme = createTheme(); // Customize as needed

export const TestWrapper: FC<{ children: ReactNode }> = ({ children }) => {

  return (
    <QueryClientProvider client={queryClient}>
        <ClientLocalizationProvider>
          <ThemeProvider theme={theme}>
            <CssBaseline />
            <SnackbarProvider
              autoHideDuration={3000}
              preventDuplicate
              maxSnack={3}
              anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
              Components={{
                success: customToast.success,
                error: customToast.error,
                warning: customToast.warning,
                info: customToast.info,
              }}
            >
              {children}
            </SnackbarProvider>
          </ThemeProvider>
        </ClientLocalizationProvider>
      </QueryClientProvider>
  );
};
