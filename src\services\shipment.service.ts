import { CreateShipment } from 'types';
import { apiService } from './api/clientApi';
import { AppConfig } from 'configs/app-config';

const externalNextRoute = AppConfig.EXTERNAL_NEXT_ROUTE;

export const createShipmentEventService = (requestBody: CreateShipment): Promise<void> =>
  apiService.post(externalNextRoute + '/v1/durian/events/shipment', requestBody);

export const updateShipmentEventService = (requestBody: CreateShipment, shipmentId: string): Promise<void> =>
  apiService.put(externalNextRoute + `/v1/durian/events/shipment/${shipmentId}`, requestBody);
