import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Breadcrumbs } from './breadcrumbs';
import '@testing-library/jest-dom';
import { useRouter } from 'next/navigation';

jest.mock('components', () => ({
  Dialog: jest.fn(({ isOpen, onConfirm, onCancel, title, content, okButtonText, cancelButtonText }) =>
    isOpen ? (
      <div>
        <h1>{title}</h1>
        <p>{content}</p>
        <button onClick={() => onConfirm && onConfirm()}>
          {okButtonText}
        </button>
        <button onClick={() => {
          if (onCancel) {
            onCancel();
            return false; // Ensure event propagation is stopped
          }
        }}>
          {cancelButtonText}
        </button>
      </div>
    ) : null
  ),
}));

jest.mock('next/link', () => {
  const MockLink = ({ children, href, onClick }: { children: React.ReactNode; href: string; onClick: (e: unknown) => void }) => {
    return (
      <a href={href} onClick={onClick}>
        {children}
      </a>
    );
  };
  MockLink.displayName = 'MockLink';
  return MockLink;
});

const mockUseRouter = useRouter as jest.Mock;

describe('Breadcrumbs', () => {
  const mockPush = jest.fn();

  beforeEach(() => {
    mockUseRouter.mockReturnValue({
      push: mockPush,
    });
    jest.clearAllMocks();
  });

  const items = [
    { label: 'Home', href: '/' },
    { label: 'Products', href: '/products' },
    { label: 'Product A', href: '/products/a' },
  ];

  it('renders breadcrumb items', () => {
    render(<Breadcrumbs items={items} />);
    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Products')).toBeInTheDocument();
    expect(screen.getByText('Product A')).toBeInTheDocument();
  });

  it('renders the last item as text and not a link', () => {
    render(<Breadcrumbs items={items} />);
    const lastItem = screen.getByText('Product A');
    expect(lastItem.tagName).toBe('P');
    expect(lastItem.closest('a')).toBeNull();
  });

  it('renders other items as links', () => {
    render(<Breadcrumbs items={items} />);
    expect(screen.getByText('Home').closest('a')).toHaveAttribute('href', '/');
    expect(screen.getByText('Products').closest('a')).toHaveAttribute('href', '/products');
  });

  it('navigates on link click', () => {
    render(<Breadcrumbs items={items} />);
    fireEvent.click(screen.getByText('Home'));
    expect(mockPush).toHaveBeenCalledWith('/');
  });

  it('calls onClick handler and prevents navigation', () => {
    const mockOnClick = jest.fn();
    const itemsWithOnClick = [
      { label: 'Home', href: '/', onClick: mockOnClick },
      { label: 'Current', href: '/current' },
    ];
    render(<Breadcrumbs items={itemsWithOnClick} />);
    fireEvent.click(screen.getByText('Home'));
    expect(mockOnClick).toHaveBeenCalled();
    expect(mockPush).not.toHaveBeenCalled();
  });

  it('shows confirmation dialog when showConfirmation is true', () => {
    const itemsWithConfirmation = [
      { label: 'Home', href: '/', showConfirmation: true },
      { label: 'Current', href: '/current' },
    ];
    render(<Breadcrumbs items={itemsWithConfirmation} />);
    fireEvent.click(screen.getByText('Home'));

    // Instead of checking the mock directly, check for the rendered dialog content
    expect(screen.getByText('reminder-title')).toBeInTheDocument();
    expect(screen.getByText('ok-modal-btn')).toBeInTheDocument();
    expect(screen.getByText('cancel-modal-btn')).toBeInTheDocument();
    expect(mockPush).not.toHaveBeenCalled();
  });

  it('navigates after confirmation', () => {
    const itemsWithConfirmation = [
      { label: 'Home', href: '/', showConfirmation: true },
      { label: 'Current', href: '/current' },
    ];
    render(<Breadcrumbs items={itemsWithConfirmation} />);
    fireEvent.click(screen.getByText('Home'));

    expect(screen.getByText('ok-modal-btn')).toBeInTheDocument();
    fireEvent.click(screen.getByText('ok-modal-btn'));

    expect(mockPush).toHaveBeenCalledWith('/');
  });

  it('calls onClick after confirmation and prevents navigation', () => {
    const mockOnClick = jest.fn();
    const itemsWithConfirmationAndOnClick = [
      { label: 'Home', href: '/', showConfirmation: true, onClick: mockOnClick },
      { label: 'Current', href: '/current' },
    ];
    render(<Breadcrumbs items={itemsWithConfirmationAndOnClick} />);
    fireEvent.click(screen.getByText('Home'));

    fireEvent.click(screen.getByText('ok-modal-btn'));

    expect(mockOnClick).toHaveBeenCalled();
    expect(mockPush).toHaveBeenCalled();
  });

  it('does not navigate or call onClick on cancel', () => {
    const mockOnClick = jest.fn();
    const itemsWithConfirmation = [
      { label: 'Home', href: '/', showConfirmation: true, onClick: mockOnClick },
      { label: 'Current', href: '/current' },
    ];
    render(<Breadcrumbs items={itemsWithConfirmation} />);
    fireEvent.click(screen.getByText('Home'));

    expect(screen.getByText('cancel-modal-btn')).toBeInTheDocument();
    fireEvent.click(screen.getByText('cancel-modal-btn'));

    expect(mockPush).not.toHaveBeenCalled();
    expect(mockOnClick).toHaveBeenCalled();
    expect(screen.queryByText('reminder-title')).not.toBeInTheDocument();
  });
});
