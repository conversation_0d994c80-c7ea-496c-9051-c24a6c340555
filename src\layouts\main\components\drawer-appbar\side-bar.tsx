'use client';

import MenuIcon from '@mui/icons-material/Menu';
import MenuOpenIcon from '@mui/icons-material/MenuOpen';
import { Box, CssBaseline, DrawerProps, IconButton, Drawer as MuiDrawer, styled, Theme, useTheme } from '@mui/material';
import { FC, PropsWithChildren } from 'react';
import { useGlobalStore } from 'store/useGlobalStore';
import { ListMenuItems } from './list-menu-items';

import { collapsedWidth, drawerWidth, headerHeight } from '../../constant';
import { AnimatePresence, motion } from 'framer-motion';

const openedWidth = drawerWidth;
const closedWidth = collapsedWidth;

// 1. Define your open/closed mixins
const openedMixin = (theme: Theme) => ({
  width: openedWidth,
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  overflowX: 'hidden',
});

const closedMixin = (theme: Theme) => ({
  width: closedWidth,
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  overflowX: 'hidden',
});

// 2. Style the Drawer so it applies the right mixin
const StyledDrawer = styled(MuiDrawer, {
  shouldForwardProp: (prop) => prop !== 'open',
})<{ open: boolean; variant: DrawerProps['variant'] }>(({ theme, open }) => {
  const styles = {
    flexShrink: 0,
    whiteSpace: 'nowrap',
    boxSizing: 'border-box',
    ...(open && {
      ...openedMixin(theme),
      '& .MuiDrawer-paper': openedMixin(theme),
    }),
    ...(!open && {
      ...closedMixin(theme),
      '& .MuiDrawer-paper': closedMixin(theme),
    }),
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return styles as any; // Explicitly cast to avoid type mismatch
});

// 3. Style your main content to animate its width (or margin) the same way
const Main = styled('main', {
  shouldForwardProp: (prop) => prop !== 'open',
})<{ open: boolean }>(({ theme, open }) => ({
  flexGrow: 1,
  boxSizing: 'border-box',
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: open ? theme.transitions.duration.enteringScreen : theme.transitions.duration.leavingScreen,
  }),
  width: open ? `calc(100vw - ${openedWidth}px)` : `calc(100vw - ${closedWidth}px)`,
}));

export const AnimatedMenuIcon = ({ isOpen }: { isOpen: boolean }) => {
  return (
    <AnimatePresence mode="wait" initial={false}>
      <motion.div
        key={isOpen ? 'open' : 'closed'}
        initial={{ x: 10, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        exit={{ x: -10, opacity: 0 }}
        transition={{ duration: 0.2 }}
        style={{ display: 'inline-block' }}
      >
        {isOpen ? <MenuOpenIcon fontSize="inherit" /> : <MenuIcon fontSize="inherit" />}
      </motion.div>
    </AnimatePresence>
  );
};

export const DrawerAppBar: FC<PropsWithChildren & { isPDPA?: boolean }> = ({ children, isPDPA }) => {
  const theme = useTheme();
  const { isOpen, toggle } = useGlobalStore();

  return (
    <Box component="div" id="drawer-app-bar-container" sx={{ display: 'flex', width: '100%', flex: 1 }}>
      {!isPDPA && (
        <>
          <CssBaseline />
          <StyledDrawer
            variant="permanent"
            open={isOpen}
            sx={{
              width: isOpen ? drawerWidth : collapsedWidth,
              flexShrink: 0,
              '& .MuiDrawer-paper': {
                width: isOpen ? drawerWidth : collapsedWidth,
                overflowX: 'hidden',
                color: theme.palette.customColors.white,
                background: theme.palette.customColors.gradientAppBgColorNew,
                mt: `${headerHeight + 1}px`,
                height: `calc(100vh - ${headerHeight + 1}px)`,
                boxShadow: 1,
              },
            }}
          >
            <ListMenuItems isOpen={isOpen} />
            <Box
              component="div"
              sx={{
                display: 'flex',
                gap: '12px',
                alignItems: 'center',
                borderTop: `1px solid ${theme.palette.customColors.divider}`,
                justifyContent: 'flex-start',
                pl: '24px',
              }}
            >
              <IconButton
                onClick={toggle}
                size="small"
                sx={{
                  justifyContent: 'center',
                }}
              >
                <AnimatedMenuIcon isOpen={isOpen} />
              </IconButton>
            </Box>
          </StyledDrawer>
        </>
      )}
      <Main
        open={isOpen}
        style={{
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Box
          sx={{
            height: `calc(100vh - ${headerHeight}px)`,
            overflow: 'auto',
          }}
        >
          {children}
        </Box>
      </Main>
    </Box>
  );
};
