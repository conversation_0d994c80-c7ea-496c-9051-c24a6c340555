import CameraAltOutlinedIcon from '@mui/icons-material/CameraAltOutlined';
import { Avatar, Box, Button, Grow, IconButton, Skeleton, Typography, styled } from '@mui/material';
import { IMAGE_EXTENSIONS } from 'constant/common';
import { useTranslations } from 'next-intl';
import React, { useState } from 'react';
import { uploadFileService } from 'services/internal.service';
import { UploadFile } from 'store/useCreateShipmentStore';
import { theme } from 'styles/theme';
import { getImageUrl, validateFileImage } from 'utils';
import { sendEvent } from 'utils/gtag';
import toastMessages from 'utils/toastMessages';
import { v4 } from 'uuid';

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

export interface ImagePreview {
  file?: File;
  preview: string;
  id?: string;
}

interface UploadImageProps {
  onChange: (imgString: string) => void;
  image?: string;
}

const UploadCutterImage: React.FC<UploadImageProps> = ({ onChange, image }) => {
  const [uploadFileImage, setUploadFileImage] = useState<UploadFile[]>([]);

  const commonT = useTranslations('common');
  const formT = useTranslations('form');
  const receivingTranslation = useTranslations('receive');

  const uploadFile = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    const response = await uploadFileService(formData);
    sendEvent('uploaded_photos');
    const filenameData = response.data.filenameDisk;

    return filenameData;
  };

  const uploadPhoto = async (file: UploadFile) => {
    if (!file.file) return;
    const tempArr = [
      {
        ...file,
        isUploading: true,
      },
    ];
    setUploadFileImage(tempArr);

    const fileResponse = await uploadFile(file.file);

    if (fileResponse) {
      const arrMap = tempArr.map((photo) => {
        if (photo.id === file.id) {
          return {
            ...photo,
            url: fileResponse ?? '',
            isUploading: false,
          };
        }
        return photo;
      });

      setUploadFileImage(arrMap);
      onChange(fileResponse);
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (!file) return;

    const { isValid } = validateFileImage(file);

    if (!isValid) {
      toastMessages.error(formT('invalid-image'));
      e.target.value = '';
      return;
    }

    const imgId = v4();
    const fileUpload = {
      id: imgId,
      name: file.name,
      size: file.size,
      type: file.type,
      url: '',
      file,
    };

    try {
      await uploadPhoto(fileUpload);
    } catch {
      toastMessages.error(commonT('common-error'));
      setUploadFileImage([...uploadFileImage.filter((img) => img.id !== fileUpload.id)]);
    }
    e.target.value = '';
  };

  const renderPhotoItem = (img: UploadFile) => {
    if (img.isUploading) {
      return <Skeleton variant="circular" width={180} height={180} />;
    }
    return (
      <Box sx={{ position: 'relative' }}>
        <Avatar src={getImageUrl(img.url) ?? ''} sx={{ height: 180, width: 180 }} />

        <IconButton
          component="label"
          sx={{ position: 'absolute', right: 0, bottom: 0, p: 1, border: '1px solid' }}
          disabled={img.isUploading}
        >
          <VisuallyHiddenInput
            style={{
              width: '100%',
              height: '100%',
              position: 'absolute',
              right: 0,
              bottom: 0,
              zIndex: 2,
            }}
            accept={IMAGE_EXTENSIONS.join(',')}
            type="file"
            multiple={false}
            onChange={handleFileChange}
          />
          <CameraAltOutlinedIcon fontSize="inherit" color="inherit" />
        </IconButton>
      </Box>
    );
  };

  // Check if we have an existing image or uploaded images to display
  const hasExistingImage = image && image.trim() !== '';
  const hasUploadedImages = uploadFileImage.length > 0;
  const shouldShowUploadButton = !hasExistingImage && !hasUploadedImages;

  return (
    <Box
      sx={{
        width: '100%',
        height: '180px',
        display: 'flex',
        gap: '12px',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      {shouldShowUploadButton && (
        <Button
          component="label"
          role={undefined}
          sx={{
            width: '180px',
            height: '180px',
            borderRadius: '50%',
            border: `1px dashed ${theme.palette.customColors.neutralBorder}`,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            position: 'relative',
          }}
          variant="outlined"
        >
          <VisuallyHiddenInput
            style={{
              width: '100%',
              height: '100%',
              position: 'absolute',
              right: 0,
              bottom: 0,
              zIndex: 2,
            }}
            accept={IMAGE_EXTENSIONS.join(',')}
            type="file"
            multiple={false}
            onChange={handleFileChange}
          />
          <CameraAltOutlinedIcon sx={{ color: theme.palette.customColors.black }} />
          <Typography variant="caption" fontSize="14px" color="text.secondary">
            {receivingTranslation('cutter-photo')}
          </Typography>
          <Typography variant="caption" fontSize="10px" color="text.secondary">
            {receivingTranslation('file-size-limit')}
          </Typography>
        </Button>
      )}

      {/* Display existing image from props */}
      {hasExistingImage && !hasUploadedImages && (
        <Box sx={{ position: 'relative' }}>
          <Avatar src={getImageUrl(image) ?? ''} sx={{ height: 180, width: 180 }} />
          <IconButton
            component="label"
            sx={{ position: 'absolute', right: 0, bottom: 0, p: 1, border: '1px solid' }}
          >
            <VisuallyHiddenInput
              style={{
                width: '100%',
                height: '100%',
                position: 'absolute',
                right: 0,
                bottom: 0,
                zIndex: 2,
              }}
              accept={IMAGE_EXTENSIONS.join(',')}
              type="file"
              multiple={false}
              onChange={handleFileChange}
            />
            <CameraAltOutlinedIcon fontSize="inherit" color="inherit" />
          </IconButton>
        </Box>
      )}

      {/* Display uploaded images */}
      {uploadFileImage.map((img, idx) => (
        <Grow in={true} timeout={100 + idx * 50} key={`${img.id}`}>
          <Box
            sx={{
              width: '120px',
              height: '120px',
              border: `1px dashed solid`,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              borderRadius: '50%',
              position: 'relative',
            }}
          >
            {renderPhotoItem(img)}
          </Box>
        </Grow>
      ))}
    </Box>
  );
};

export default UploadCutterImage;
