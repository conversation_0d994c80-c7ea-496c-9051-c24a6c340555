import { FormControl, TextFieldProps, Typography } from '@mui/material';
import { NumberInput } from 'components/input-form';
import { useTranslations } from 'next-intl';
import { JSX, ReactElement } from 'react';
import { Control, Controller, FieldErrors, FieldValues, Path } from 'react-hook-form';

export interface FormNumberInputProps<TFormValues extends FieldValues> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  errors?: FieldErrors<TFormValues>;
  label: string;
  required?: boolean;
  requiredMessage?: string;
  placeholder?: string;
  variant?: TextFieldProps['variant'];
  max?: number;
  min?: number;
  patternMessage?: string;
  pattern?: RegExp;
  loading?: boolean;
  decimalScale?: number;
  validate?: Record<string, (value: unknown) => boolean | string>;
  startAdornment?: JSX.Element;
  minRequireMessage?: string;
}

export function FormNumberInput<TFormValues extends FieldValues>({
  name,
  control,
  errors,
  label,
  required = false,
  requiredMessage,
  placeholder,
  variant = 'outlined',
  max,
  loading,
  min,
  decimalScale,
  validate,
  startAdornment,
  minRequireMessage,
}: FormNumberInputProps<TFormValues>): ReactElement {
  const formT = useTranslations('form');
  const rules = {
    ...(required && { required: requiredMessage }),
    ...(validate && { validate }),
    ...(min && { min: { value: min, message: minRequireMessage ?? `${label} ${formT('min')} ${min}` } }),
    ...(max && { max: { value: max, message: `${label} ${formT('max')} ${max}` } }),
  };

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field }) => (
        <FormControl fullWidth error={!!errors?.[name]}>
          <Typography sx={{ fontSize: '18px', mb: 1 }} variant="caption">
            {label}{' '}
            <Typography sx={{ visibility: required ? 'visible' : 'hidden' }} variant="caption" color="error">
              *
            </Typography>
          </Typography>
          <NumberInput
            min={min}
            helperText={errors?.[name]?.message as string}
            decimalScale={decimalScale}
            error={!!errors?.[name]}
            required={required}
            name={name}
            placeholder={placeholder}
            variant={variant}
            value={field.value}
            onChange={(value: string) => field.onChange(value)}
            max={max}
            loading={loading}
            startAdornment={startAdornment}
          />
        </FormControl>
      )}
    />
  );
}
