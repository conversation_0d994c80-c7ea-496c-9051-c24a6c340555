'use server';

import { cookies } from 'next/headers';
import { safeJSONParse } from 'utils';

import { basePathProd } from 'configs/app-config';

const maxAge = 60 * 60 * 24 * 90; // 90 days;

export type cookieKeys =
  | 'user_info'
  | 'access_token_pkg_house'
  | 'ph_current_project_token'
  | 'ph_current_project_name'
  | 'ph_current_instance'
  | 'locale_pg_house'
  | 'NEXT_LOCALE';

export const setCookieServer = async (key: cookieKeys, value: string): Promise<void> => {
  const cookieStore = await cookies();
  cookieStore.set(key, value, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: basePathProd,
    maxAge,
  });
};

export const getCookieServer = async <T>(key: cookieKeys) => {
  const cookieStore = await cookies();

  const value = cookieStore.get(key)?.value;

  if (!value) return undefined;

  const valueParsed = safeJSONParse<T>(value);

  if (!valueParsed) return value;

  return valueParsed as T;
};

export const clearAllCookie = async () => {
  const authCookies: cookieKeys[] = ['user_info', 'access_token_pkg_house'];

  authCookies.forEach(async (cookieName) => {
    await clearCookie(cookieName);
  })

  await clearCookie('access_token_pkg_house');
  await clearCookie('user_info');
};

export const clearCookie = async (name: string) => {
  const cookieStore = await cookies();
  cookieStore.set(name, '', {
    maxAge: -1,
    path: basePathProd,
  });
};


export const setCookieWithoutSecure = async (key: cookieKeys, value: string, maxAgeConfig?: number): Promise<void> => {
  const cookieStore = await cookies();
  cookieStore.set(key, value, {
    path: basePathProd,
    maxAge: maxAgeConfig ?? maxAge,
  });
};

export const setCookieLocale = async (key: cookieKeys, value: string, maxAgeConfig?: number): Promise<void> => {
  const cookieStore = await cookies();

  cookieStore.set(key, value, {
    path: '/',
    maxAge: maxAgeConfig ?? maxAge,
  });
};
