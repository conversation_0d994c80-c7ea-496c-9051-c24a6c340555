import React, { FC } from 'react';
import styles from './styles.module.css';
import Image from 'next/image';
import logo from 'assets/icons/logo.svg';

export const LoadingScreen: FC = () => {
  const logoSize = 44;

  return (
    <div className={styles.loadingContainer}>
      <div className={styles.loadingContent}>
        <div className={styles.logoContainer}>
          <div className={styles.animatedLogo}>
            <Image unoptimized src={logo} width={logoSize} height={logoSize} alt="logo" />
          </div>
          <div className={styles.loadingDots}>
            <span className={styles.dot}></span>
            <span className={styles.dot}></span>
            <span className={styles.dot}></span>
          </div>
        </div>
      </div>
    </div>
  );
};
