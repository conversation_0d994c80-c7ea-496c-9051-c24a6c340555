// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`CustomDatePicker should render disabled state 1`] = `
<div>
  <div
    class="MuiFormControl-root MuiPickersTextField-root Mui-disabled css-17hqzlj-MuiFormControl-root-MuiPickersTextField-root"
  >
    <label
      class="MuiFormLabel-root MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-outlined MuiFormLabel-colorPrimary Mui-disabled MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-outlined css-19qnlrw-MuiFormLabel-root-MuiInputLabel-root"
      data-shrink="false"
      for="«rq»"
      id="«rq»-label"
    >
      Test Date Picker
    </label>
    <div
      aria-invalid="false"
      aria-labelledby="«rq»-label"
      class="MuiPickersInputBase-root MuiPickersOutlinedInput-root Mui-disabled MuiPickersInputBase-colorPrimary MuiPickersInputBase-adornedEnd css-vycme6-MuiPickersInputBase-root-MuiPickersOutlinedInput-root"
      role="group"
    >
      <div
        class="MuiPickersSectionList-root MuiPickersInputBase-sectionsContainer css-qru2u2-MuiPickersSectionList-root-MuiPickersInputBase-sectionsContainer-MuiPickersOutlinedInput-sectionsContainer"
        contenteditable="false"
        tabindex="0"
      >
        <span
          class="MuiPickersSectionList-section css-joz0rk-MuiPickersSectionList-section-MuiPickersInputBase-section"
          data-sectionindex="0"
        >
          <span
            class="MuiPickersInputBase-sectionBefore css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
          <span
            aria-disabled="true"
            aria-label="Day"
            aria-labelledby="«rr»-day"
            aria-readonly="false"
            aria-valuemax="31"
            aria-valuemin="1"
            aria-valuetext="Empty"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent css-1jpopwz-MuiPickersSectionList-sectionContent-MuiPickersInputBase-sectionContent"
            contenteditable="false"
            id="«rr»-day"
            inputmode="numeric"
            role="spinbutton"
            tabindex="0"
          >
            DD
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          >
            -
          </span>
        </span>
        <span
          class="MuiPickersSectionList-section css-joz0rk-MuiPickersSectionList-section-MuiPickersInputBase-section"
          data-sectionindex="1"
        >
          <span
            class="MuiPickersInputBase-sectionBefore css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
          <span
            aria-disabled="true"
            aria-label="Month"
            aria-labelledby="«rr»-month"
            aria-readonly="false"
            aria-valuemax="12"
            aria-valuemin="1"
            aria-valuetext="Empty"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent css-1jpopwz-MuiPickersSectionList-sectionContent-MuiPickersInputBase-sectionContent"
            contenteditable="false"
            id="«rr»-month"
            inputmode="text"
            role="spinbutton"
            tabindex="-1"
          >
            MMMM
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          >
            -
          </span>
        </span>
        <span
          class="MuiPickersSectionList-section css-joz0rk-MuiPickersSectionList-section-MuiPickersInputBase-section"
          data-sectionindex="2"
        >
          <span
            class="MuiPickersInputBase-sectionBefore css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
          <span
            aria-disabled="true"
            aria-label="Year"
            aria-labelledby="«rr»-year"
            aria-readonly="false"
            aria-valuemax="9999"
            aria-valuemin="0"
            aria-valuetext="Empty"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent css-1jpopwz-MuiPickersSectionList-sectionContent-MuiPickersInputBase-sectionContent"
            contenteditable="false"
            id="«rr»-year"
            inputmode="numeric"
            role="spinbutton"
            tabindex="-1"
          >
            YYYY
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
        </span>
      </div>
      <div
        class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-elo8k2-MuiInputAdornment-root"
      >
        <button
          aria-label="Choose date"
          class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-edgeEnd MuiIconButton-sizeMedium css-1ysp02-MuiButtonBase-root-MuiIconButton-root"
          disabled=""
          tabindex="-1"
          type="button"
        >
          <svg
            aria-hidden="true"
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-1umw9bq-MuiSvgIcon-root"
            data-testid="CalendarIcon"
            focusable="false"
            viewBox="0 0 24 24"
          >
            <path
              d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
            />
          </svg>
        </button>
      </div>
      <fieldset
        aria-hidden="true"
        class="MuiPickersOutlinedInput-notchedOutline css-lqwr9g-MuiPickersOutlinedInput-notchedOutline"
      >
        <legend
          class="css-1pbc52w"
        >
          <span
            class="css-w48gsk"
          >
            Test Date Picker
          </span>
        </legend>
      </fieldset>
      <input
        aria-hidden="true"
        class="MuiPickersInputBase-input MuiPickersOutlinedInput-input css-31lcvl-MuiPickersInputBase-input"
        disabled=""
        id="«rq»"
        tabindex="-1"
        value=""
      />
    </div>
  </div>
</div>
`;

exports[`CustomDatePicker should render with a value 1`] = `
<div>
  <div
    class="MuiFormControl-root MuiPickersTextField-root css-17hqzlj-MuiFormControl-root-MuiPickersTextField-root"
  >
    <label
      class="MuiFormLabel-root MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-shrink MuiInputLabel-outlined MuiFormLabel-colorPrimary MuiFormLabel-filled MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-shrink MuiInputLabel-outlined css-113d811-MuiFormLabel-root-MuiInputLabel-root"
      data-shrink="true"
      for="«r6»"
      id="«r6»-label"
    >
      Test Date Picker
    </label>
    <div
      aria-invalid="false"
      aria-labelledby="«r6»-label"
      class="MuiPickersInputBase-root MuiPickersOutlinedInput-root MuiPickersInputBase-colorPrimary MuiPickersInputBase-adornedEnd css-vycme6-MuiPickersInputBase-root-MuiPickersOutlinedInput-root"
      role="group"
    >
      <div
        class="MuiPickersSectionList-root MuiPickersInputBase-sectionsContainer css-1fb7els-MuiPickersSectionList-root-MuiPickersInputBase-sectionsContainer-MuiPickersOutlinedInput-sectionsContainer"
        contenteditable="false"
        tabindex="0"
      >
        <span
          class="MuiPickersSectionList-section css-joz0rk-MuiPickersSectionList-section-MuiPickersInputBase-section"
          data-sectionindex="0"
        >
          <span
            class="MuiPickersInputBase-sectionBefore css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
          <span
            aria-disabled="false"
            aria-label="Day"
            aria-labelledby="«r7»-day"
            aria-readonly="false"
            aria-valuemax="31"
            aria-valuemin="1"
            aria-valuenow="25"
            aria-valuetext="25th"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent css-1jpopwz-MuiPickersSectionList-sectionContent-MuiPickersInputBase-sectionContent"
            contenteditable="true"
            id="«r7»-day"
            inputmode="numeric"
            role="spinbutton"
            spellcheck="false"
            tabindex="0"
          >
            25
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          >
            -
          </span>
        </span>
        <span
          class="MuiPickersSectionList-section css-joz0rk-MuiPickersSectionList-section-MuiPickersInputBase-section"
          data-sectionindex="1"
        >
          <span
            class="MuiPickersInputBase-sectionBefore css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
          <span
            aria-disabled="false"
            aria-label="Month"
            aria-labelledby="«r7»-month"
            aria-readonly="false"
            aria-valuemax="12"
            aria-valuemin="1"
            aria-valuenow="12"
            aria-valuetext="December"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent css-1jpopwz-MuiPickersSectionList-sectionContent-MuiPickersInputBase-sectionContent"
            contenteditable="true"
            id="«r7»-month"
            inputmode="text"
            role="spinbutton"
            spellcheck="false"
            tabindex="-1"
          >
            December
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          >
            -
          </span>
        </span>
        <span
          class="MuiPickersSectionList-section css-joz0rk-MuiPickersSectionList-section-MuiPickersInputBase-section"
          data-sectionindex="2"
        >
          <span
            class="MuiPickersInputBase-sectionBefore css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
          <span
            aria-disabled="false"
            aria-label="Year"
            aria-labelledby="«r7»-year"
            aria-readonly="false"
            aria-valuemax="9999"
            aria-valuemin="0"
            aria-valuenow="2566"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent css-1jpopwz-MuiPickersSectionList-sectionContent-MuiPickersInputBase-sectionContent"
            contenteditable="true"
            id="«r7»-year"
            inputmode="numeric"
            role="spinbutton"
            spellcheck="false"
            tabindex="-1"
          >
            2566
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
        </span>
      </div>
      <div
        class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-elo8k2-MuiInputAdornment-root"
      >
        <button
          aria-label="Choose date, selected date is Dec 25, 2566"
          class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium css-1ysp02-MuiButtonBase-root-MuiIconButton-root"
          tabindex="0"
          type="button"
        >
          <svg
            aria-hidden="true"
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-1umw9bq-MuiSvgIcon-root"
            data-testid="CalendarIcon"
            focusable="false"
            viewBox="0 0 24 24"
          >
            <path
              d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
            />
          </svg>
        </button>
      </div>
      <fieldset
        aria-hidden="true"
        class="MuiPickersOutlinedInput-notchedOutline css-lqwr9g-MuiPickersOutlinedInput-notchedOutline"
      >
        <legend
          class="css-kg5swy"
        >
          <span
            class="css-w48gsk"
          >
            Test Date Picker
          </span>
        </legend>
      </fieldset>
      <input
        aria-hidden="true"
        class="MuiPickersInputBase-input MuiPickersOutlinedInput-input css-31lcvl-MuiPickersInputBase-input"
        id="«r6»"
        tabindex="-1"
        value="25-December-2566"
      />
    </div>
  </div>
</div>
`;

exports[`CustomDatePicker should render with custom min and max dates 1`] = `
<div>
  <div
    class="MuiFormControl-root MuiPickersTextField-root css-17hqzlj-MuiFormControl-root-MuiPickersTextField-root"
  >
    <label
      class="MuiFormLabel-root MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-outlined MuiFormLabel-colorPrimary MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-outlined css-19qnlrw-MuiFormLabel-root-MuiInputLabel-root"
      data-shrink="false"
      for="«rg»"
      id="«rg»-label"
    >
      Test Date Picker
    </label>
    <div
      aria-invalid="false"
      aria-labelledby="«rg»-label"
      class="MuiPickersInputBase-root MuiPickersOutlinedInput-root MuiPickersInputBase-colorPrimary MuiPickersInputBase-adornedEnd css-vycme6-MuiPickersInputBase-root-MuiPickersOutlinedInput-root"
      role="group"
    >
      <div
        class="MuiPickersSectionList-root MuiPickersInputBase-sectionsContainer css-qru2u2-MuiPickersSectionList-root-MuiPickersInputBase-sectionsContainer-MuiPickersOutlinedInput-sectionsContainer"
        contenteditable="false"
        tabindex="0"
      >
        <span
          class="MuiPickersSectionList-section css-joz0rk-MuiPickersSectionList-section-MuiPickersInputBase-section"
          data-sectionindex="0"
        >
          <span
            class="MuiPickersInputBase-sectionBefore css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
          <span
            aria-disabled="false"
            aria-label="Day"
            aria-labelledby="«rh»-day"
            aria-readonly="false"
            aria-valuemax="31"
            aria-valuemin="1"
            aria-valuetext="Empty"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent css-1jpopwz-MuiPickersSectionList-sectionContent-MuiPickersInputBase-sectionContent"
            contenteditable="true"
            id="«rh»-day"
            inputmode="numeric"
            role="spinbutton"
            spellcheck="false"
            tabindex="0"
          >
            DD
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          >
            -
          </span>
        </span>
        <span
          class="MuiPickersSectionList-section css-joz0rk-MuiPickersSectionList-section-MuiPickersInputBase-section"
          data-sectionindex="1"
        >
          <span
            class="MuiPickersInputBase-sectionBefore css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
          <span
            aria-disabled="false"
            aria-label="Month"
            aria-labelledby="«rh»-month"
            aria-readonly="false"
            aria-valuemax="12"
            aria-valuemin="1"
            aria-valuetext="Empty"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent css-1jpopwz-MuiPickersSectionList-sectionContent-MuiPickersInputBase-sectionContent"
            contenteditable="true"
            id="«rh»-month"
            inputmode="text"
            role="spinbutton"
            spellcheck="false"
            tabindex="-1"
          >
            MMMM
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          >
            -
          </span>
        </span>
        <span
          class="MuiPickersSectionList-section css-joz0rk-MuiPickersSectionList-section-MuiPickersInputBase-section"
          data-sectionindex="2"
        >
          <span
            class="MuiPickersInputBase-sectionBefore css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
          <span
            aria-disabled="false"
            aria-label="Year"
            aria-labelledby="«rh»-year"
            aria-readonly="false"
            aria-valuemax="9999"
            aria-valuemin="0"
            aria-valuetext="Empty"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent css-1jpopwz-MuiPickersSectionList-sectionContent-MuiPickersInputBase-sectionContent"
            contenteditable="true"
            id="«rh»-year"
            inputmode="numeric"
            role="spinbutton"
            spellcheck="false"
            tabindex="-1"
          >
            YYYY
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
        </span>
      </div>
      <div
        class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-elo8k2-MuiInputAdornment-root"
      >
        <button
          aria-label="Choose date"
          class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium css-1ysp02-MuiButtonBase-root-MuiIconButton-root"
          tabindex="0"
          type="button"
        >
          <svg
            aria-hidden="true"
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-1umw9bq-MuiSvgIcon-root"
            data-testid="CalendarIcon"
            focusable="false"
            viewBox="0 0 24 24"
          >
            <path
              d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
            />
          </svg>
        </button>
      </div>
      <fieldset
        aria-hidden="true"
        class="MuiPickersOutlinedInput-notchedOutline css-lqwr9g-MuiPickersOutlinedInput-notchedOutline"
      >
        <legend
          class="css-1pbc52w"
        >
          <span
            class="css-w48gsk"
          >
            Test Date Picker
          </span>
        </legend>
      </fieldset>
      <input
        aria-hidden="true"
        class="MuiPickersInputBase-input MuiPickersOutlinedInput-input css-31lcvl-MuiPickersInputBase-input"
        id="«rg»"
        tabindex="-1"
        value=""
      />
    </div>
  </div>
</div>
`;

exports[`CustomDatePicker should render with custom textField props 1`] = `
<div>
  <div
    class="MuiFormControl-root MuiPickersTextField-root css-17hqzlj-MuiFormControl-root-MuiPickersTextField-root"
    placeholder="DD-MM-YYYY"
  >
    <label
      class="MuiFormLabel-root MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-outlined MuiFormLabel-colorPrimary MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-outlined css-19qnlrw-MuiFormLabel-root-MuiInputLabel-root"
      data-shrink="false"
      for="«rl»"
      id="«rl»-label"
    >
      Test Date Picker
    </label>
    <div
      aria-describedby="«rl»-helper-text"
      aria-invalid="false"
      aria-labelledby="«rl»-label"
      aria-live="polite"
      class="MuiPickersInputBase-root MuiPickersOutlinedInput-root MuiPickersInputBase-colorPrimary MuiPickersInputBase-adornedEnd css-vycme6-MuiPickersInputBase-root-MuiPickersOutlinedInput-root"
      role="group"
    >
      <div
        class="MuiPickersSectionList-root MuiPickersInputBase-sectionsContainer css-qru2u2-MuiPickersSectionList-root-MuiPickersInputBase-sectionsContainer-MuiPickersOutlinedInput-sectionsContainer"
        contenteditable="false"
        tabindex="0"
      >
        <span
          class="MuiPickersSectionList-section css-joz0rk-MuiPickersSectionList-section-MuiPickersInputBase-section"
          data-sectionindex="0"
        >
          <span
            class="MuiPickersInputBase-sectionBefore css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
          <span
            aria-disabled="false"
            aria-label="Day"
            aria-labelledby="«rm»-day"
            aria-readonly="false"
            aria-valuemax="31"
            aria-valuemin="1"
            aria-valuetext="Empty"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent css-1jpopwz-MuiPickersSectionList-sectionContent-MuiPickersInputBase-sectionContent"
            contenteditable="true"
            id="«rm»-day"
            inputmode="numeric"
            role="spinbutton"
            spellcheck="false"
            tabindex="0"
          >
            DD
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          >
            -
          </span>
        </span>
        <span
          class="MuiPickersSectionList-section css-joz0rk-MuiPickersSectionList-section-MuiPickersInputBase-section"
          data-sectionindex="1"
        >
          <span
            class="MuiPickersInputBase-sectionBefore css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
          <span
            aria-disabled="false"
            aria-label="Month"
            aria-labelledby="«rm»-month"
            aria-readonly="false"
            aria-valuemax="12"
            aria-valuemin="1"
            aria-valuetext="Empty"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent css-1jpopwz-MuiPickersSectionList-sectionContent-MuiPickersInputBase-sectionContent"
            contenteditable="true"
            id="«rm»-month"
            inputmode="text"
            role="spinbutton"
            spellcheck="false"
            tabindex="-1"
          >
            MMMM
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          >
            -
          </span>
        </span>
        <span
          class="MuiPickersSectionList-section css-joz0rk-MuiPickersSectionList-section-MuiPickersInputBase-section"
          data-sectionindex="2"
        >
          <span
            class="MuiPickersInputBase-sectionBefore css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
          <span
            aria-disabled="false"
            aria-label="Year"
            aria-labelledby="«rm»-year"
            aria-readonly="false"
            aria-valuemax="9999"
            aria-valuemin="0"
            aria-valuetext="Empty"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent css-1jpopwz-MuiPickersSectionList-sectionContent-MuiPickersInputBase-sectionContent"
            contenteditable="true"
            id="«rm»-year"
            inputmode="numeric"
            role="spinbutton"
            spellcheck="false"
            tabindex="-1"
          >
            YYYY
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
        </span>
      </div>
      <div
        class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-elo8k2-MuiInputAdornment-root"
      >
        <button
          aria-label="Choose date"
          class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium css-1ysp02-MuiButtonBase-root-MuiIconButton-root"
          tabindex="0"
          type="button"
        >
          <svg
            aria-hidden="true"
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-1umw9bq-MuiSvgIcon-root"
            data-testid="CalendarIcon"
            focusable="false"
            viewBox="0 0 24 24"
          >
            <path
              d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
            />
          </svg>
        </button>
      </div>
      <fieldset
        aria-hidden="true"
        class="MuiPickersOutlinedInput-notchedOutline css-lqwr9g-MuiPickersOutlinedInput-notchedOutline"
      >
        <legend
          class="css-1pbc52w"
        >
          <span
            class="css-w48gsk"
          >
            Test Date Picker
          </span>
        </legend>
      </fieldset>
      <input
        aria-hidden="true"
        class="MuiPickersInputBase-input MuiPickersOutlinedInput-input css-31lcvl-MuiPickersInputBase-input"
        id="«rl»"
        tabindex="-1"
        value=""
      />
    </div>
    <p
      class="MuiFormHelperText-root MuiFormHelperText-sizeMedium MuiFormHelperText-contained css-er619e-MuiFormHelperText-root"
      id="«rl»-helper-text"
    >
      Please select a date
    </p>
  </div>
</div>
`;

exports[`CustomDatePicker should render with error state 1`] = `
<div>
  <div
    class="MuiFormControl-root MuiPickersTextField-root css-1pp2qxa-MuiFormControl-root-MuiPickersTextField-root"
  >
    <label
      class="MuiFormLabel-root MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-outlined MuiFormLabel-colorPrimary Mui-error MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-outlined css-19qnlrw-MuiFormLabel-root-MuiInputLabel-root"
      data-shrink="false"
      for="«rb»"
      id="«rb»-label"
    >
      Test Date Picker
    </label>
    <div
      aria-invalid="true"
      aria-labelledby="«rb»-label"
      class="MuiPickersInputBase-root MuiPickersOutlinedInput-root Mui-error MuiPickersInputBase-colorPrimary MuiPickersInputBase-adornedEnd css-vycme6-MuiPickersInputBase-root-MuiPickersOutlinedInput-root"
      role="group"
    >
      <div
        class="MuiPickersSectionList-root MuiPickersInputBase-sectionsContainer css-qru2u2-MuiPickersSectionList-root-MuiPickersInputBase-sectionsContainer-MuiPickersOutlinedInput-sectionsContainer"
        contenteditable="false"
        tabindex="0"
      >
        <span
          class="MuiPickersSectionList-section css-joz0rk-MuiPickersSectionList-section-MuiPickersInputBase-section"
          data-sectionindex="0"
        >
          <span
            class="MuiPickersInputBase-sectionBefore css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
          <span
            aria-disabled="false"
            aria-label="Day"
            aria-labelledby="«rc»-day"
            aria-readonly="false"
            aria-valuemax="31"
            aria-valuemin="1"
            aria-valuetext="Empty"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent css-1jpopwz-MuiPickersSectionList-sectionContent-MuiPickersInputBase-sectionContent"
            contenteditable="true"
            id="«rc»-day"
            inputmode="numeric"
            role="spinbutton"
            spellcheck="false"
            tabindex="0"
          >
            DD
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          >
            -
          </span>
        </span>
        <span
          class="MuiPickersSectionList-section css-joz0rk-MuiPickersSectionList-section-MuiPickersInputBase-section"
          data-sectionindex="1"
        >
          <span
            class="MuiPickersInputBase-sectionBefore css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
          <span
            aria-disabled="false"
            aria-label="Month"
            aria-labelledby="«rc»-month"
            aria-readonly="false"
            aria-valuemax="12"
            aria-valuemin="1"
            aria-valuetext="Empty"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent css-1jpopwz-MuiPickersSectionList-sectionContent-MuiPickersInputBase-sectionContent"
            contenteditable="true"
            id="«rc»-month"
            inputmode="text"
            role="spinbutton"
            spellcheck="false"
            tabindex="-1"
          >
            MMMM
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          >
            -
          </span>
        </span>
        <span
          class="MuiPickersSectionList-section css-joz0rk-MuiPickersSectionList-section-MuiPickersInputBase-section"
          data-sectionindex="2"
        >
          <span
            class="MuiPickersInputBase-sectionBefore css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
          <span
            aria-disabled="false"
            aria-label="Year"
            aria-labelledby="«rc»-year"
            aria-readonly="false"
            aria-valuemax="9999"
            aria-valuemin="0"
            aria-valuetext="Empty"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent css-1jpopwz-MuiPickersSectionList-sectionContent-MuiPickersInputBase-sectionContent"
            contenteditable="true"
            id="«rc»-year"
            inputmode="numeric"
            role="spinbutton"
            spellcheck="false"
            tabindex="-1"
          >
            YYYY
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
        </span>
      </div>
      <div
        class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-elo8k2-MuiInputAdornment-root"
      >
        <button
          aria-label="Choose date"
          class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium css-1ysp02-MuiButtonBase-root-MuiIconButton-root"
          tabindex="0"
          type="button"
        >
          <svg
            aria-hidden="true"
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-1umw9bq-MuiSvgIcon-root"
            data-testid="CalendarIcon"
            focusable="false"
            viewBox="0 0 24 24"
          >
            <path
              d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
            />
          </svg>
        </button>
      </div>
      <fieldset
        aria-hidden="true"
        class="MuiPickersOutlinedInput-notchedOutline css-lqwr9g-MuiPickersOutlinedInput-notchedOutline"
      >
        <legend
          class="css-1pbc52w"
        >
          <span
            class="css-w48gsk"
          >
            Test Date Picker
          </span>
        </legend>
      </fieldset>
      <input
        aria-hidden="true"
        class="MuiPickersInputBase-input MuiPickersOutlinedInput-input css-31lcvl-MuiPickersInputBase-input"
        id="«rb»"
        tabindex="-1"
        value=""
      />
    </div>
  </div>
</div>
`;

exports[`CustomDatePicker should render with no value 1`] = `
<div>
  <div
    class="MuiFormControl-root MuiPickersTextField-root css-17hqzlj-MuiFormControl-root-MuiPickersTextField-root"
  >
    <label
      class="MuiFormLabel-root MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-outlined MuiFormLabel-colorPrimary MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-outlined css-19qnlrw-MuiFormLabel-root-MuiInputLabel-root"
      data-shrink="false"
      for="«r1»"
      id="«r1»-label"
    >
      Test Date Picker
    </label>
    <div
      aria-invalid="false"
      aria-labelledby="«r1»-label"
      class="MuiPickersInputBase-root MuiPickersOutlinedInput-root MuiPickersInputBase-colorPrimary MuiPickersInputBase-adornedEnd css-vycme6-MuiPickersInputBase-root-MuiPickersOutlinedInput-root"
      role="group"
    >
      <div
        class="MuiPickersSectionList-root MuiPickersInputBase-sectionsContainer css-qru2u2-MuiPickersSectionList-root-MuiPickersInputBase-sectionsContainer-MuiPickersOutlinedInput-sectionsContainer"
        contenteditable="false"
        tabindex="0"
      >
        <span
          class="MuiPickersSectionList-section css-joz0rk-MuiPickersSectionList-section-MuiPickersInputBase-section"
          data-sectionindex="0"
        >
          <span
            class="MuiPickersInputBase-sectionBefore css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
          <span
            aria-disabled="false"
            aria-label="Day"
            aria-labelledby="«r2»-day"
            aria-readonly="false"
            aria-valuemax="31"
            aria-valuemin="1"
            aria-valuetext="Empty"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent css-1jpopwz-MuiPickersSectionList-sectionContent-MuiPickersInputBase-sectionContent"
            contenteditable="true"
            id="«r2»-day"
            inputmode="numeric"
            role="spinbutton"
            spellcheck="false"
            tabindex="0"
          >
            DD
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          >
            -
          </span>
        </span>
        <span
          class="MuiPickersSectionList-section css-joz0rk-MuiPickersSectionList-section-MuiPickersInputBase-section"
          data-sectionindex="1"
        >
          <span
            class="MuiPickersInputBase-sectionBefore css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
          <span
            aria-disabled="false"
            aria-label="Month"
            aria-labelledby="«r2»-month"
            aria-readonly="false"
            aria-valuemax="12"
            aria-valuemin="1"
            aria-valuetext="Empty"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent css-1jpopwz-MuiPickersSectionList-sectionContent-MuiPickersInputBase-sectionContent"
            contenteditable="true"
            id="«r2»-month"
            inputmode="text"
            role="spinbutton"
            spellcheck="false"
            tabindex="-1"
          >
            MMMM
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          >
            -
          </span>
        </span>
        <span
          class="MuiPickersSectionList-section css-joz0rk-MuiPickersSectionList-section-MuiPickersInputBase-section"
          data-sectionindex="2"
        >
          <span
            class="MuiPickersInputBase-sectionBefore css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
          <span
            aria-disabled="false"
            aria-label="Year"
            aria-labelledby="«r2»-year"
            aria-readonly="false"
            aria-valuemax="9999"
            aria-valuemin="0"
            aria-valuetext="Empty"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent css-1jpopwz-MuiPickersSectionList-sectionContent-MuiPickersInputBase-sectionContent"
            contenteditable="true"
            id="«r2»-year"
            inputmode="numeric"
            role="spinbutton"
            spellcheck="false"
            tabindex="-1"
          >
            YYYY
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter css-w1q93y-MuiPickersSectionList-sectionSeparator-MuiPickersInputBase-separator"
          />
        </span>
      </div>
      <div
        class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-elo8k2-MuiInputAdornment-root"
      >
        <button
          aria-label="Choose date"
          class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium css-1ysp02-MuiButtonBase-root-MuiIconButton-root"
          tabindex="0"
          type="button"
        >
          <svg
            aria-hidden="true"
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-1umw9bq-MuiSvgIcon-root"
            data-testid="CalendarIcon"
            focusable="false"
            viewBox="0 0 24 24"
          >
            <path
              d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
            />
          </svg>
        </button>
      </div>
      <fieldset
        aria-hidden="true"
        class="MuiPickersOutlinedInput-notchedOutline css-lqwr9g-MuiPickersOutlinedInput-notchedOutline"
      >
        <legend
          class="css-1pbc52w"
        >
          <span
            class="css-w48gsk"
          >
            Test Date Picker
          </span>
        </legend>
      </fieldset>
      <input
        aria-hidden="true"
        class="MuiPickersInputBase-input MuiPickersOutlinedInput-input css-31lcvl-MuiPickersInputBase-input"
        id="«r1»"
        tabindex="-1"
        value=""
      />
    </div>
  </div>
</div>
`;
