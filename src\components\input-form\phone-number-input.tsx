'use client';

import { FormControl, InputAdornment, TextField, TextFieldProps } from '@mui/material';
import { FC } from 'react';
import { parsePhoneNumberFromString } from 'libphonenumber-js';
import { Controller, useFormContext, RegisterOptions } from 'react-hook-form';

interface PhoneNumberInputProps extends Omit<TextFieldProps, 'onChange' | 'type'> {
  name: string;
  onChange?: (value: string) => void;
  rules?: RegisterOptions;
}

export const PhoneNumberInput: FC<PhoneNumberInputProps> = ({
  name,
  onChange,
  label,
  disabled,
  placeholder,
  rules,
  helperText,
  ...otherProps
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const fieldError = errors[name];
  const hasError = !!fieldError;

  const validateAndSetError = (value: string) => {
    const phone = parsePhoneNumberFromString(value, 'TH');
    const isValid = phone && phone.isValid();
    return isValid;
  };

  const cleanPhoneNumber = (value: string) => {
    // Only allow + at the first position and numbers for the rest
    if (!value) return '';

    // If value starts with +, keep it and only allow numbers after
    if (value.startsWith('+')) {
      return '+' + value.slice(1).replace(/\D/g, '');
    }

    // If no +, only allow numbers
    return value.replace(/\D/g, '');
  };

  const maxNumberLength = 14;

  // Regex: optional + at start, followed by 0 or more digits (allows just "+" while typing)
  const regexTestPhone = /^(?:\+[0-9]*|[0-9]+)$/;

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field }) => (
        <FormControl fullWidth sx={{ position: 'relative' }}>
          <TextField
            {...field}
            {...otherProps}
            type="tel"
            disabled={disabled}
            label={label}
            placeholder={placeholder}
            error={hasError}
            helperText={fieldError ? helperText : ''}
            fullWidth
            onChange={(e) => {
              const rawValue = e.target.value;
              const cleanedValue = cleanPhoneNumber(rawValue);

              if (cleanedValue && regexTestPhone.test(cleanedValue) === false) {
                return;
              }

              if (cleanedValue?.length >= maxNumberLength) {
                validateAndSetError(cleanedValue);
                return;
              }

              field.onChange(cleanedValue);
              validateAndSetError(cleanedValue);
              onChange?.(cleanedValue);
            }}
            onPaste={(e) => {
              e.preventDefault();
              const pastedText = e.clipboardData.getData('text');
              const cleanedValue = cleanPhoneNumber(pastedText);

              // Only paste if it results in a valid format
              if (cleanedValue && regexTestPhone.test(cleanedValue)) {
                field.onChange(cleanedValue);
                validateAndSetError(cleanedValue);
                onChange?.(cleanedValue);
              }
            }}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment
                    position="start"
                    sx={{
                      position: 'absolute',
                      left: '10px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      pointerEvents: 'none',
                    }}
                  />
                ),
              },
            }}
          />
        </FormControl>
      )}
    />
  );
};
