# ---- Base Image for Building ----
FROM node:22.16.0-alpine3.22 AS builder

# Set working directory
WORKDIR /app

# Set production environment
ENV NODE_ENV=production

# Accept build arguments for version and commit SHA
ARG VERSION
# Set environment variables from build args
ENV NEXT_PUBLIC_WEB_VERSION=${VERSION}

# Create user
RUN addgroup -S nodejs && adduser -S nextjs -G nodejs

# Enable Corepack to use the correct Yarn version
RUN corepack enable

# Copy package.json and yarn.lock first
COPY package.json yarn.lock ./
# Copy .yarnrc.yml if it exists
COPY .yarnrc.yml ./

# Set up Yarn properly - create .yarn directory and set Yarn version
RUN mkdir -p .yarn/releases && \
    yarn set version 4.9.1

# Install dependencies using the project's specified Yarn version
RUN yarn install --frozen-lockfile

# Disable <PERSON>sky in CI
ENV HUSKY=0

# Copy only necessary files for building
COPY src ./src
COPY public ./public
COPY next.config.ts ./
COPY tsconfig.json ./
COPY locales ./locales
COPY global.d.ts ./
COPY env.d.ts ./

# Build application
RUN yarn build

# ---- Production Image ----
FROM node:22.16.0-alpine3.22 AS runner
WORKDIR /app
ENV NODE_ENV=production

# Accept build arguments for runtime
ARG VERSION

# Set environment variables for runtime
ENV NEXT_PUBLIC_WEB_VERSION=${VERSION}

# Create a non-root user for security
RUN addgroup -S nodejs && adduser -S nextjs -G nodejs

# Copy necessary build artifacts (no node_modules)
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs --chmod=755 /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs --chmod=755 /app/.next/static ./.next/static

# Fix permission so entrypoint script can write to files
RUN chown -R nextjs:nodejs /app

# Set permissions for the application user
USER nextjs

# Expose port
ENV PORT=3000
EXPOSE $PORT

# Use entrypoint script
CMD ["node", "server.js"]
