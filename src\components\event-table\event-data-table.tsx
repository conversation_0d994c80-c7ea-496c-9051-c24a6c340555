/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { Box } from '@mui/material';
import type {
  DataGridProps,
  GridCallbackDetails,
  GridColDef,
  GridPaginationModel,
  GridRowParams,
  GridRowSelectionModel,
  GridSortModel,
  GridValidRowModel,
} from '@mui/x-data-grid';
import { DataGrid } from '@mui/x-data-grid';
import { useQuery } from '@tanstack/react-query';
import { FilterState, FilterTable } from 'components';
import { PAGE_SIZE_OPTIONS, RECORDED_BY_OPTIONS, STATUS_OPTIONS } from 'constant/common';
import dayjs from 'dayjs';
import { useDeviceHeight } from 'hooks/useDeviceHeight';
import { useDragColumns } from 'hooks/useDragColumn';
import { headerHeight } from 'layouts/main/constant';
import { useTranslations } from 'next-intl';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { getListEvents, GetListEventsParam } from 'services/event.service';
import { theme } from 'styles/theme';
import { EventType, PackingHouse } from 'types/event';
import { CustomNoRowsOverlay } from './custom-no-row-overlay';
import { CustomPagination } from './custom-pagination';
import { CustomSortIconComponent } from './custom-sort-icon';
import styles from './styles.module.css';

const PAGE_SIZE = 10;

const getSelectedCount = (selection: GridRowSelectionModel): number => {
  if (selection && typeof selection === 'object') {
    if ('ids' in selection && selection.ids instanceof Set) {
      return selection.ids.size;
    }
    if (selection instanceof Set) {
      return selection.size;
    }
    if (Array.isArray(selection)) {
      return selection.length;
    }
  }
  return 0;
};

const defaultPaginationModel = {
  pageSize: PAGE_SIZE,
  page: 0,
};

type Props<T extends GridValidRowModel = any> = DataGridProps<T> & {
  tableHeight?: number;
  eventType: EventType;
  queryKey: string;
  hasFilterUserRole?: boolean;
  filterStatusOptions?: { label: string; value: string }[];
  rowSizeOptions?: number[];
  maxRowSelection?: number;
  onRowClick?: (params: GridRowParams) => void;
  defaultSortModel?: GridSortModel;
};

export const EventDataTable = <T extends GridValidRowModel = any>({
  classes,
  slots,
  columns,
  tableHeight,
  onRowSelectionModelChange,
  eventType,
  queryKey,
  filterStatusOptions,
  hasFilterUserRole,
  rowSizeOptions,
  maxRowSelection,
  rowSelectionModel,
  onRowClick,
  defaultSortModel,
  rowSelection = true,
  ...props
}: Props) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const initialPage = Number(searchParams?.get('page')) || 0;
  const initialPageSize = PAGE_SIZE_OPTIONS.find((item) => item === Number(searchParams?.get('pageSize'))) ?? PAGE_SIZE;
  const initialSearch = searchParams?.get('search') ?? '';
  const initialSortField = searchParams?.get('sortBy') ?? '';
  const initialSortOrder = searchParams?.get('order') ?? 'desc';
  const { columns: columnsWithDrag } = useDragColumns<T>(columns as GridColDef<T>[]);
  const isPaginationModelChanged = useRef<boolean>(false);
  const commonTranslation = useTranslations('common');
  const [rows, setRows] = useState<PackingHouse[]>([]);
  const [filters, setFilters] = useState<FilterState>({
    startDate: searchParams?.get('startDate') ? dayjs(Number(searchParams?.get('startDate'))) : null,
    endDate: searchParams?.get('endDate') ? dayjs(Number(searchParams?.get('endDate'))) : null,
    recordedBy: searchParams?.get('recordedBy')
      ? searchParams
          ?.get('recordedBy')!
          .split(',')
          .filter((item) => RECORDED_BY_OPTIONS.includes(item as any))
      : [],
    status: searchParams?.get('status')
      ? searchParams
          ?.get('status')!
          .split(',')
          .filter((item) => STATUS_OPTIONS.includes(item as any))
      : [],
  });
  const [total, setTotal] = useState<number>(0);

  const filterT = useTranslations('filter');
  const [searchKeyword, setSearchKeyword] = useState(initialSearch);
  const [sortModel, setSortModel] = useState<GridSortModel>([]);
  const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
    page: initialPage,
    pageSize: initialPageSize,
  });

  const resetParams = useCallback(() => {
    setSearchKeyword('');
    setFilters({
      status: [],
      recordedBy: [],
      startDate: null,
      endDate: null,
    });
    setPaginationModel({
      page: 0,
      pageSize: PAGE_SIZE,
    });
    setSortModel([]);
  }, []);

  // Only reset params when searchParams is empty, and only once
  useEffect(() => {
    if (searchParams?.toString() === '') {
      resetParams();
    }
  }, [searchParams, resetParams]);

  // Sync sortModel with URL parameters when they change
  useEffect(() => {
    if (initialSortField) {
      const newSortModel: GridSortModel = [
        {
          field: initialSortField,
          sort: initialSortOrder as 'asc' | 'desc',
        },
      ];

      setSortModel(newSortModel);
    }
  }, [initialSortField, initialSortOrder]);

  const defaultSortModelField = defaultSortModel?.[0]?.field ?? 'dateCreated';
  const defaultSortModelOrder = defaultSortModel?.[0]?.sort ?? 'desc';

  const sortModelFieldParam = sortModel[0]?.field || defaultSortModelField;
  const sortModelOrderParam = sortModel[0]?.sort ?? defaultSortModelOrder;
  const pageSizeParam = paginationModel.pageSize || PAGE_SIZE;
  const pageParam = paginationModel.page || 0;
  const filterStatusParam = filters.status as GetListEventsParam['status'];
  const filterStartDateUnix = filters.startDate ? filters.startDate.unix() : undefined;
  const filterToDateUnix = filters.endDate ? filters.endDate.unix() : undefined;
  const recordedByParam = filters.recordedBy as GetListEventsParam['recordedBy'];

  const updateUrlParams = useCallback(
    ({
      page = paginationModel.page,
      pageSize = paginationModel.pageSize,
      search = searchKeyword,
      sortBy = sortModelFieldParam,
      order = sortModelOrderParam,
      status = filters.status,
      startDate = filters.startDate,
      endDate = filters.endDate,
      recordedBy = filters.recordedBy,
    }: {
      page?: number;
      pageSize?: number;
      search?: string;
      sortBy?: string;
      order?: 'asc' | 'desc';
      status?: string[];
      startDate?: any;
      endDate?: any;
      recordedBy?: string[];
    } = {}) => {
      // Always start with a fresh URLSearchParams to avoid carrying over unwanted params
      const params = new URLSearchParams();
      params.set('page', String(page));
      params.set('pageSize', String(pageSize));
      if (search) params.set('search', search);
      if (sortBy) params.set('sortBy', String(sortBy));
      if (order) params.set('order', String(order));
      if (status && status.length > 0) params.set('status', status.join(','));
      if (startDate) params.set('startDate', String(startDate.valueOf()));
      if (endDate) params.set('endDate', String(endDate.valueOf()));
      if (recordedBy && recordedBy.length > 0) params.set('recordedBy', recordedBy.join(','));
      router.replace(`?${params.toString()}`);
    },
    [
      paginationModel.page,
      paginationModel.pageSize,
      searchKeyword,
      sortModelFieldParam,
      sortModelOrderParam,
      filters.status,
      filters.startDate,
      filters.endDate,
      filters.recordedBy,
      router,
    ]
  );

  const handlePageModelChange = (model: GridPaginationModel) => {
    setPaginationModel(model);
    isPaginationModelChanged.current = true;
    updateUrlParams({ page: model.page, pageSize: model.pageSize });
  };

  const handleApplyFilters = (filter: FilterState) => {
    setFilters({ ...filter });
    updateUrlParams({
      status: filter.status,
      startDate: filter.startDate,
      endDate: filter.endDate,
      recordedBy: filter.recordedBy,
    });
  };

  const dataGridClasses = useMemo(
    () => ({
      columnHeader: styles.columnHeader,
      columnHeaderTitleContainer: styles.columnHeaderTitleContainer,
      columnHeaderTitleContainerContent: styles.columnHeaderTitleContainerContent,
      columnHeaderTitle: styles.columnHeaderTitle,
      cell: styles.cell,
      ...classes,
    }),
    [classes]
  );

  const dataGridSx = useMemo(
    () => ({
      border: 0,
      '& .MuiDataGrid-scrollbarFiller--header': {
        backgroundColor: '#dbeafe',
      },
      '& .MuiDataGrid-cell:focus-within': {
        outline: 'none',
      },
      '& .MuiDataGrid-columnHeaderCheckbox .MuiDataGrid-checkboxInput': {
        display: 'none',
      },
      '& .MuiDataGrid-iconButtonContainer': {
        visibility: 'visible',
      },
      '& .MuiDataGrid-sortIcon': {
        opacity: 'inherit !important',
      },
      '& .MuiDataGrid-row': {
        cursor: rowSelection ? 'pointer' : 'default',
      },
    }),
    [rowSelection]
  );

  // Reset pagination model when rows change
  useEffect(() => {
    if (isPaginationModelChanged.current) {
      isPaginationModelChanged.current = false;
    }
  }, [rows]);

  const queryParams = useMemo<GetListEventsParam>(() => {
    return {
      page: pageParam + 1,
      size: pageSizeParam,
      search: searchKeyword,
      sortBy: sortModelFieldParam,
      order: sortModelOrderParam,
      eventType,
      status: filterStatusParam,
      fromDate: filterStartDateUnix,
      toDate: filterToDateUnix,
      recordedBy: recordedByParam,
    };
  }, [
    pageParam,
    pageSizeParam,
    searchKeyword,
    sortModelFieldParam,
    sortModelOrderParam,
    eventType,
    filterStatusParam,
    filterStartDateUnix,
    filterToDateUnix,
    recordedByParam,
  ]);

  const {
    isSuccess,
    data: response,
    isLoading,
    isFetching,
    refetch,
  } = useQuery({
    queryKey: [queryKey, queryParams],
    queryFn: () => {
      return getListEvents({ ...queryParams, eventType });
    },
    refetchOnWindowFocus: false,
    gcTime: 0,
    staleTime: 0,
    refetchInterval: 5 * 60 * 1000, // 5 minutes
    // placeholderData: keepPreviousData,
  });

  const handleSearchKeywordChange = useCallback(
    (keyword: string) => {
      setSearchKeyword(keyword);
      updateUrlParams({
        search: keyword,
      });
    },
    [updateUrlParams]
  );

  const handleSortModelChange = useCallback(
    (model: GridSortModel) => {
      setSortModel(model);

      // Update URL with new sort parameters
      if (model.length > 0 && model[0]) {
        // Reset pagination to first page when sorting changes
        setPaginationModel((prev) => ({ ...prev, page: 0 }));
        updateUrlParams({
          sortBy: model[0].field,
          order: model[0].sort ?? undefined,
          page: 0, // Reset to first page when sorting changes
        });
      } else {
        // Clear sort parameters from URL when no sort is applied
        setPaginationModel((prev) => ({ ...prev, page: 0 }));
        updateUrlParams({
          sortBy: undefined,
          order: undefined,
          page: 0, // Reset to first page when sorting is cleared
        });
      }
    },
    [updateUrlParams]
  );

  useEffect(() => {
    if (isSuccess && response) {
      setTotal(response.total);
      setRows(response.data);
    }
  }, [isSuccess, response]);

  const deviceHeight = useDeviceHeight();

  const minHeight = 600;

  const customTableHeight = useMemo<number>(() => {
    if (tableHeight) {
      return tableHeight;
    }

    if (deviceHeight) {
      const paddingHeight = 250;

      return deviceHeight - headerHeight - paddingHeight;
    }

    return minHeight;
  }, [deviceHeight, tableHeight]);

  // Refactored to be more concise and clear
  const handleRowSelectionModelChange = (
    newRowSelectionModel: GridRowSelectionModel,
    details: GridCallbackDetails<'rows'>
  ) => {
    let newRowSelection = newRowSelectionModel;
    // Skip selection changes during pagination model changes
    if (isPaginationModelChanged.current) return;
    if (rowSelectionModel) {
      const selectionCount = getSelectedCount(rowSelectionModel);
      const newRowSelectionCount = getSelectedCount(newRowSelectionModel);

      // Remove all selection
      if (selectionCount === maxRowSelection && newRowSelectionCount - selectionCount > 1) {
        newRowSelection = { ids: new Set(), type: rowSelectionModel.type };
      } else if (maxRowSelection) {
        const limitedIds = new Set(Array.from(newRowSelectionModel.ids).slice(0, maxRowSelection));
        const limitedSelection: GridRowSelectionModel = {
          type: 'include',
          ids: limitedIds,
        };
        newRowSelection = { ...limitedSelection };
      }
    }
    onRowSelectionModelChange?.(newRowSelection, details);
  };

  const filterDateTitle = useMemo(() => {
    if (eventType === 'shipment' || eventType === 'qr') {
      return filterT('date-created');
    }
    return eventType === 'harvesting' ? filterT('date-incoming') : filterT('date-receiving');
  }, [eventType, filterT]);

  return (
    <Box
      component="div"
      className={styles.container}
      sx={{
        p: 2,
        width: '100%',
        position: 'relative',
        backgroundColor: theme.palette.customColors.white,
        borderRadius: 2,
        boxShadow: 2,
      }}
    >
      <FilterTable
        filterDateTitle={filterDateTitle}
        hasFilterUserRole={hasFilterUserRole}
        filterStatusOptions={filterStatusOptions}
        filterId="filter-product"
        filters={filters}
        setSearchKeyword={handleSearchKeywordChange}
        searchKeyword={searchKeyword}
        searchPlaceHolder={commonTranslation('search-placeholder')}
        handleApplyFilters={handleApplyFilters}
        refetch={refetch}
      />

      <Box
        sx={{
          width: '100%',
          height: customTableHeight,
          position: 'relative',
          '.MuiDataGrid-root': {
            border: 0,
            borderRadius: 0,
            '.MuiDataGrid-columnHeaders': {
              borderTopLeftRadius: 0,
              borderTopRightRadius: 0,
            },
            '.MuiDataGrid-columnHeader': {
              backgroundColor: '#dbeafe',
            },
          },
          '& .MuiDataGrid-columnHeaders': {
            backgroundColor: theme.palette.customColors.gray6,
          },
          '& .MuiDataGrid-cell:focus': {
            outline: 'none',
          },
          border: `1px solid ${theme.palette.customColors.gray5}`,
          borderRadius: '4px',
        }}
      >
        {!!deviceHeight && (
          <DataGrid
            disableColumnResize
            disableRowSelectionOnClick
            disableColumnMenu
            columns={columnsWithDrag}
            hideFooterSelectedRowCount
            rows={rows}
            rowCount={total}
            sortingMode="server"
            onRowClick={(params: GridRowParams) => {
              if (!onRowClick) {
                return;
              }
              onRowClick(params);
            }}
            filterMode="server"
            paginationMode="server"
            loading={isLoading || isFetching}
            rowSelectionModel={rowSelectionModel}
            sx={dataGridSx}
            columnHeaderHeight={40}
            classes={dataGridClasses}
            onSortModelChange={handleSortModelChange}
            sortModel={sortModel}
            paginationModel={paginationModel || defaultPaginationModel}
            onPaginationModelChange={handlePageModelChange}
            slots={{
              pagination: () => (
                <CustomPagination showSelectedRow={props.checkboxSelection} rowSizeOptions={rowSizeOptions} />
              ),
              noRowsOverlay: CustomNoRowsOverlay,
              columnHeaderSortIcon: CustomSortIconComponent,
              ...slots,
            }}
            slotProps={{
              loadingOverlay: {
                variant: 'circular-progress',
                noRowsVariant: 'circular-progress',
                sx: {
                  color: '#fff',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  opacity: 1,
                  background: '#fff',
                },
              },
            }}
            onRowSelectionModelChange={handleRowSelectionModelChange}
            checkboxSelection={props.checkboxSelection}
            rowSelection={rowSelection}
            {...props}
          />
        )}
      </Box>
    </Box>
  );
};
