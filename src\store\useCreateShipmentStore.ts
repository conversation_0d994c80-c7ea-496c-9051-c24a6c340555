import { cloneDeep, flatMap, isEmpty, uniqBy } from 'lodash-es';
import { getQrCodeBatchLotService, uploadOcaDocumentPtpService } from 'services/common.service';
import { uploadFileService } from 'services/internal.service';
import { create } from 'store';
import { DurianVariety, PackingHouse, PackingHouseDetail, QrcodeBatchLot, Receipt, TranslateLabel } from 'types';
import { delayPromise, getImageUrl } from 'utils';
import { mapperOcrResponseToToOcrRecord } from 'utils/mapper';
import _get from 'lodash-es/get';
import dayjs from 'dayjs';
import { capturePosthog } from 'utils/posthog';
import { sendEvent } from 'utils/gtag';
import { transportationModeValues } from 'constant/shipment';

export type VarietyOptionValue = {
  id: string;
  label: TranslateLabel;
  isOther: boolean;
  name?: string;
  originalId: string;
};

export type ShipmentIdentityType = {
  name: string;
};

export enum FormShipmentStepEnum {
  SelectedProductIds,
  ShipmentIdentity,
  DurianInformationStep,
  ReceiptStep,
  UploadDocumentStep,
  QrReviewStep,
}

export type DurianInformationForm = {
  brand: string;
  boxType: string;
  variety: string;
  grade: string;
  netWeight: string;
  totalBoxes: string;
  id?: string;
  packingDate: dayjs.Dayjs | number | null;
  batchlot?: string;
  qrUrl?: string;
  qrId?: string;
};

export type UploadFile = {
  name: string;
  id: string;
  file?: File;
  size: number;
  url?: string;
  type: string;
  hasUploaded?: boolean;
  isUploading?: boolean;
};

export interface ShipmentPhotosLoading {
  [key: string]: boolean;
}

export const defaultState: ShipmentState = {
  formStep: FormShipmentStepEnum.SelectedProductIds,
  informationFormValues: [],
  openModalInformation: false,
  additionalFiles: [] as UploadFile[],
  ocrFile: undefined,
  receivingIds: [],
  isUploadOcrError: false,
  shipmentIdentity: {
    name: '',
  },
  qrCodeBatchlots: [],
  manualInputOrc: false,
  _hasHydrated: false,
  receivingDetails: [],
  shipmentPhotos: [],
  sessionFormData: {
    shipmentName: '',
    orcFile: undefined,
  },
  varietiesList: [],
  isEditForm: false,
  showWarningDialog: false,
};

type ShipmentState = {
  formStep: FormShipmentStepEnum;
  informationFormValues: DurianInformationForm[];
  openModalInformation: boolean;
  additionalFiles: UploadFile[];
  ocrFile?: Receipt;
  receivingIds: string[];
  isUploadOcrError: boolean;
  shipmentIdentity: ShipmentIdentityType;
  qrCodeBatchlots: QrcodeBatchLot[];
  manualInputOrc: boolean;
  _hasHydrated: boolean;
  receivingDetails: PackingHouse[];
  shipmentPhotos: UploadFile[];
  sessionFormData: {
    shipmentName?: string;
    orcFile?: Receipt;
  };
  varietiesList: DurianVariety[];
  isEditForm: boolean;
  showWarningDialog: boolean;
};

type ShipmentActionState = {
  updateStep: (formStep: FormShipmentStepEnum) => void;
  updateOpenPackagingModal: (value: boolean) => void;

  // Form
  updateInformationForm: (informationFormValues: DurianInformationForm) => void;
  deleteInformationForm: (id: string) => void;
  updateShipmentIdentity: (name: string) => void;

  updateUploadFile: (fileUploaded: UploadFile) => Promise<void>;
  deleteUploadFile: (id: string) => void;

  updateOcrFile: (file: File) => Promise<void>;
  updateOrcForm: (formValues?: Receipt) => void;
  deleteOcrFile: () => void;

  setReceivingIds: (ids: string[]) => void;
  setIsUploadOcrError: (value: boolean) => void;

  generateQrCodeBatchLot: () => Promise<void>;
  resetStore: () => void;
  initEditForm: (values: PackingHouseDetail, success?: () => void) => Promise<void>;

  setManualInput: (value: boolean) => void;
  uploadFile: (file: File) => Promise<string>;
  setReceivingDetails: (receivingDetail: PackingHouse[]) => void;

  uploadShipmentPhoto: (file: UploadFile) => Promise<void>;
  deleteShipmentPhoto: (id: string) => void;

  setSessionFormData: (data: { shipmentName?: string; orcFile?: Receipt }) => void;
  setVarietiesList: (varieties: DurianVariety[]) => void;

  setShowWarningDialog: (value: boolean) => void;
};

export const useCreateShipmentStore = create<ShipmentState & ShipmentActionState>(
  (set, get) => ({
    ...defaultState,
    updateStep: (formStep) => {
      set({
        formStep,
      });
    },
    updateInformationForm: (infoForm) => {
      const { informationFormValues } = get();

      set({
        informationFormValues: informationFormValues.concat(infoForm),
      });
    },

    deleteInformationForm: (id) => {
      const { informationFormValues } = get();
      set({
        informationFormValues: informationFormValues.filter((it) => it.id !== id),
      });
    },

    updateOpenPackagingModal: (value) => {
      set({
        openModalInformation: value,
      });
    },
    updateUploadFile: async (fileUploaded) => {
      const { uploadFile } = get();

      if (!fileUploaded.file) {
        return;
      }

      set((state) => ({
        additionalFiles: [
          ...state.additionalFiles,
          {
            ...fileUploaded,
            isUploading: true,
          },
        ],
      }));

      try {
        const fileUploadedResponse = await uploadFile(fileUploaded.file);

        set((state) => ({
          additionalFiles: state.additionalFiles.map((it) => {
            if (it.id === fileUploaded.id) {
              return {
                ...it,
                isUploading: false,
                url: fileUploadedResponse,
              };
            }
            return it;
          }),
        }));
      } catch (error) {
        set((state) => ({
          additionalFiles: state.additionalFiles.filter((it) => it.id !== fileUploaded.id),
        }));
        throw error;
      }
    },
    deleteUploadFile: (id) => {
      const { additionalFiles: uploadFiles } = get();
      set({
        additionalFiles: uploadFiles.filter((it) => it.id !== id),
      });
    },

    uploadFile: async (file) => {
      const formData = new FormData();
      formData.append('file', file);
      const response = await uploadFileService(formData);
      sendEvent('uploaded_photos');
      const filenameData = response.data.filenameDisk;

      return filenameData;
    },

    updateOcrFile: async (file) => {
      const formData = new FormData();
      formData.append('file', file);

      const response = await uploadFileService(formData);
      const filenameData = response.data.filenameDisk;

      if (!filenameData) throw new Error('uploadFile');

      const imageUrl = getImageUrl(filenameData);

      if (!imageUrl) throw new Error('image url not found');

      const { data: ocrUploadResponse } = await uploadOcaDocumentPtpService(imageUrl);

      const formattedData: Receipt = mapperOcrResponseToToOcrRecord(
        ocrUploadResponse.suggest,
        ocrUploadResponse.source,
        filenameData
      );

      capturePosthog('get_ephyto_success');

      set({
        ocrFile: { ...formattedData },
      });

      const { setSessionFormData } = get();

      setSessionFormData({
        orcFile: { ...formattedData },
      });
    },

    deleteOcrFile: () => {
      set({ ocrFile: undefined });
    },

    updateOrcForm: (values) => {
      set({ ocrFile: values });
    },

    setReceivingIds: (ids) => {
      set({
        receivingIds: [...ids],
      });
    },

    setIsUploadOcrError: (isUploadOcrError) => {
      set({ isUploadOcrError });
    },

    updateShipmentIdentity: (name) => {
      set({
        shipmentIdentity: {
          name,
        },
      });
    },
    generateQrCodeBatchLot: async () => {
      const { informationFormValues, ocrFile } = get();

      const newPackingAmount = informationFormValues.filter((it) => !it.qrId).length;

      if(newPackingAmount === 0) {
        return Promise.resolve();
      }

      const qrcodeBatchlotsResponse = await getQrCodeBatchLotService(newPackingAmount, ocrFile?.nameOfExportingCompany);

      const qrCodeBatchlots = qrcodeBatchlotsResponse.data.map((it) => ({
        qrId: it.qrId,
        batchlot: it.batchlot,
        qrUrl: it.qrUrl,
        id: it.id,
      }));

      let indexOfQr = 0;

      const informationFormValuesUpdated = informationFormValues.map((it) => {
        if (it.qrId) {
          return it;
        }

        const qrCode = qrCodeBatchlots[indexOfQr];
        indexOfQr += 1;

        return {
          ...it,
          batchlot: qrCode ? qrCode.batchlot : '',
          qrUrl: qrCode ? qrCode.qrUrl : '',
          qrId: qrCode ? qrCode.qrId : '',
        };
      });

      set({
        qrCodeBatchlots,
        informationFormValues: informationFormValuesUpdated,
      });
    },

    resetStore: () => {
      set(cloneDeep(defaultState));
    },

    initEditForm: async (values: PackingHouseDetail, success) => {
      const isDraft = values?.status === 'draft';

      const { sourceMaterials, name, files, packing, pq7ReceiptDetails } = values;

      const receivingIds = sourceMaterials?.map((it) => it.id) ?? [];

      const formStep = isDraft ? FormShipmentStepEnum.ShipmentIdentity : FormShipmentStepEnum.QrReviewStep;

      const shipmentPhotos: UploadFile[] = [];

      const fileProductsLength = files?.product.length ?? 0;

      for (let i = 0; i < fileProductsLength; i += 1) {
        const shipmentPhoto = files?.product[i];

        if (!shipmentPhoto) continue;

        shipmentPhotos.push({
          id: shipmentPhoto.id,
          name: shipmentPhoto.filenameDownload,
          file: undefined,
          size: shipmentPhoto.filesize,
          type: shipmentPhoto.mimeType,
          url: shipmentPhoto.filenameDisk ?? '',
        });
      }

      const packagingForm: DurianInformationForm[] = [];

      if (packing && !isEmpty(packing)) {
        packing.forEach((pack) => {
          const variety = pack.varieties[0];
          const varietyName = pack.varietyName;

          const varietyFormatted = {
            ...variety,
            label: varietyName
              ? {
                  th: varietyName,
                  en: varietyName,
                }
              : variety.label,
            originalId: variety.id,
            name: varietyName ?? '',
          };

          packagingForm.push({
            brand: pack.brandNameInfo.id,
            boxType: pack.productTypeInfo.id,
            variety: variety ? JSON.stringify(varietyFormatted) : '',
            grade: variety?.grades[0]?.id ?? '',
            netWeight: pack.weightKg.toString(),
            totalBoxes: pack.numberOfBoxes.toString(),
            id: pack.id,
            packingDate: pack.packingDate ? dayjs(pack.packingDate * 1000) : null,
            batchlot: pack.batchlot ?? '',
            qrUrl: pack.qrCodes?.[0]?.qrUrl ?? '',
            qrId: pack.qrCodes?.[0]?.id ?? '',
          });
        });
      }

      let ocrFile: Receipt = {} as Receipt;
      if (pq7ReceiptDetails?.receiptNumber) {
        ocrFile = {
          numberOfBoxes: pq7ReceiptDetails.numberOfBoxes,
          destinationCountry: pq7ReceiptDetails.destinationCountry,
          exportDate: pq7ReceiptDetails.exportDate,
          totalWeightKg: (pq7ReceiptDetails.totalWeightKg ? pq7ReceiptDetails.totalWeightKg.toString() : '') ?? '',
          receiptNumber: (pq7ReceiptDetails.receiptNumber ? pq7ReceiptDetails.receiptNumber.toString() : '') ?? '',
          transportationMode: transportationModeValues.includes(pq7ReceiptDetails.transportationMode)
            ? pq7ReceiptDetails.transportationMode
            : '',
          id: pq7ReceiptDetails.id,
          truckNumber: pq7ReceiptDetails.truckRegistrationNumber ?? '',
          trailerNumber: pq7ReceiptDetails.trailerRegistrationNumber ?? '',
          orchardNo: pq7ReceiptDetails.orchardRegisterNumber ?? '',
          sourceFrom: pq7ReceiptDetails?.ephytoResponse ? 'ephyto' : 'ocr',
          containerNumber: pq7ReceiptDetails.containerNumber ?? '',
          borderCheckpointName: pq7ReceiptDetails.borderCheckpointName ?? '',
          ephytoResponse: pq7ReceiptDetails.ephytoResponse,
          nameOfExportingCompany: pq7ReceiptDetails.nameOfExportingCompany ?? '',
          truckProvinceRegistrationNumber: pq7ReceiptDetails.truckProvinceRegistrationNumber ?? '',
          trailerProvinceRegistrationNumber: pq7ReceiptDetails.trailerProvinceRegistrationNumber ?? '',
        };
      }

      const additionalDocuments = files?.shipment;

      const additionalDocumentsFormatted: UploadFile[] = [];

      if (additionalDocuments?.length) {
        additionalDocuments?.forEach((additionFile) => {
          const imageUrl = additionFile.filenameDisk;

          additionalDocumentsFormatted.push({
            id: additionFile.id,
            name: additionFile.filenameDownload,
            file: undefined,
            size: additionFile.filesize,
            type: additionFile.mimeType,
            url: imageUrl ?? '',
          });
        });
      }

      const qrcodeData = packing?.map((it) => it.qrCodes[0]).filter((it) => it);

      set({
        formStep,
        receivingIds,
        shipmentIdentity: {
          name,
        },
        informationFormValues: [...packagingForm],
        ocrFile: { ...ocrFile },
        additionalFiles: [...additionalDocumentsFormatted],
        qrCodeBatchlots: qrcodeData ?? [],
        shipmentPhotos,
        isEditForm: true,
      });

      await delayPromise(100);
      success?.();
    },
    setManualInput: (manualInputOrc) => {
      set({ manualInputOrc });
    },

    setReceivingDetails: (receivingDetails) => {
      if (receivingDetails && receivingDetails.length > 0) {
        const { setVarietiesList } = get();

        const varietiesReceived =
          flatMap(receivingDetails, (it) => it.varieties).map((it, idx) => {
            return {
              ...it,
              // id: `${it.id}-${it.name ?? ''}-${idx}`,
              originalId: it.id,
              label: it.name
                ? {
                    th: it.name ?? '',
                    en: it.name ?? '',
                  }
                : it.label,
              index: idx,
              isOther: !!it.name,
              name: it.name ?? '',
              idx: idx,
            };
          }) ?? [];

        const varietiesList: DurianVariety[] = [];
        varietiesReceived.forEach((variety) => {
          const existingVariety = varietiesList.find((v) =>
            v.isOther ? v.id === variety.id && v.name === variety.name : v.id === variety.id
          );
          if (existingVariety) {
            if (_get(variety, 'isOther', false)) {
              existingVariety.grades.push(...variety.grades);
            } else {
              const existingGradeIds = existingVariety.grades.map((g) => g.id);

              variety.grades.forEach((grade) => {
                if (!existingGradeIds.includes(grade.id)) {
                  existingVariety.grades.push(grade);
                }
              });
            }
          } else {
            const grades = uniqBy(variety.grades, (grade) => grade.id);
            const newVariety = { ...variety, grades };
            varietiesList.push(newVariety);
          }
        });

        setVarietiesList(varietiesList);
      }

      set({ receivingDetails: [...receivingDetails] });
    },
    uploadShipmentPhoto: async (fileUpload) => {
      const { uploadFile } = get();

      if (!fileUpload.file) {
        return;
      }

      set((state) => ({
        shipmentPhotos: [
          ...state.shipmentPhotos,
          {
            ...fileUpload,
            isUploading: true,
          },
        ],
      }));

      try {
        const fileUploadedResponse = await uploadFile(fileUpload.file);

        set((state) => ({
          shipmentPhotos: state.shipmentPhotos.map((it) => {
            if (it.id === fileUpload.id) {
              return {
                ...it,
                isUploading: false,
                url: fileUploadedResponse,
              };
            }
            return it;
          }),
        }));
      } catch (error) {
        set((state) => ({
          shipmentPhotos: state.shipmentPhotos.filter((it) => it.id !== fileUpload.id),
        }));
        throw error;
      }
    },

    deleteShipmentPhoto: (id) => {
      const { shipmentPhotos } = get();

      set({ shipmentPhotos: shipmentPhotos.filter((photo) => photo.id !== id) });
    },
    setSessionFormData: (data) => {
      const { sessionFormData } = get();

      const newSessionFormData = cloneDeep(sessionFormData);

      if (data.shipmentName) {
        newSessionFormData.shipmentName = data.shipmentName;
      }
      if (data.orcFile) {
        newSessionFormData.orcFile = data.orcFile;
      }

      set({
        sessionFormData: newSessionFormData,
      });
    },
    setVarietiesList: (varieties) => {
      set({ varietiesList: [...varieties] });
    },
    setShowWarningDialog: (value) => {
      set({ showWarningDialog: value });
    },
  }),
  {
    persistKey: 'create-shipment-store',
    useSession: true,
    partialize: (state) =>
      state.isEditForm
        ? {}
        : {
            formStep: state.formStep,
            informationFormValues: state.informationFormValues,
            additionalFiles: state.additionalFiles,
            receivingIds: state.receivingIds,
            ocrFile: state.ocrFile,
            shipmentIdentity: state.shipmentIdentity,
            qrCodeBatchlots: state.qrCodeBatchlots,
            shipmentPhotos: state.shipmentPhotos,
            sessionFormData: state.sessionFormData,
            manualInputOrc: state.manualInputOrc,
          },
    merge: (persistedState, currentState) => {
      if (persistedState.isEditForm) {
        return {
          ...currentState,
        };
      }

      return {
        ...currentState,
        ...persistedState,
      };
    },
  }
);
