'use client';

import { Box } from '@mui/material';

import { useEffect } from 'react';
import { DrawerAppBar, PageTransition } from './components';
import { User } from 'types/user';
import { useUserStore } from 'store/useUserStore';

interface LayoutProps {
  children: React.ReactNode;
  loading?: boolean;
  user?: User;
}

export const PDPALayout = ({ children, user }: Readonly<LayoutProps>) => {
  const { setUserInfo } = useUserStore();

  useEffect(() => {
    if (user) {
      setUserInfo(user);
    }
  }, [setUserInfo, user]);

  return (
    <Box
      component="div"
      id="main-layout-wrapper"
      sx={{
        display: 'flex',
        flex: 1,
        width: '100%',
        flexDirection: 'column',
      }}
    >
      <DrawerAppBar isPDPA>
        <PageTransition>{children}</PageTransition>
      </DrawerAppBar>
    </Box>
  );
};
