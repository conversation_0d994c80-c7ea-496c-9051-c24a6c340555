import { CircularProgress, FormControl, InputAdornment, TextField, TextFieldProps } from '@mui/material';
import { FC, JSX, useRef } from 'react';
import { NumericFormat } from 'react-number-format';

export const NumberInput: FC<
  Omit<TextFieldProps, 'onChange'> & {
    onChange: (value: string) => void;
    max?: number;
    loading?: boolean;
    min?: number;
    decimalScale?: number;
    startAdornment?: JSX.Element;
  }
> = ({
  error,
  decimalScale,
  loading,
  name,
  label,
  value,
  variant,
  placeholder,
  onChange,
  defaultValue,
  max = Number.MAX_SAFE_INTEGER,
  min,
  startAdornment,
  helperText,
}) => {

  const previousRef = useRef<string>('');
  previousRef.current = value as string;

  return (
    <FormControl fullWidth sx={{ position: 'relative' }} error={error}>
      <NumericFormat
        name={name}
        customInput={TextField}
        thousandSeparator=","
        decimalScale={decimalScale}
        allowNegative={false}
        fullWidth
        label={label}
        error={error}
        variant={variant}
        placeholder={placeholder}
        defaultValue={defaultValue as string}
        helperText={helperText}
        onValueChange={({ value: curValue }) => {
          let newValue = curValue;
          if (min) {
            newValue = Number(curValue) >= min ? curValue : '';
          }
          onChange(newValue);
        }}
        value={value as string}
        isAllowed={(values) => {
          if (previousRef.current === '' && values.value.length === 1) {
            return Number(values.value) >= (min ?? 0);
          }
          return Number(values.value) <= max;
        }}
        min={min}
        allowLeadingZeros={false}
        slotProps={{
          input: {
            startAdornment,
            endAdornment: loading ? (
              <InputAdornment position="end">
                <CircularProgress size={20} />
              </InputAdornment>
            ) : null,
          },
        }}
      />
    </FormControl>
  );
};
