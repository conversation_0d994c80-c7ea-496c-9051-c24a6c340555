import toastMessages from 'utils/toastMessages';
import { create } from 'zustand';

export type ToastType = keyof typeof toastMessages;

export interface ToastNotification {
  message: string;
  type: ToastType;
}

interface ToastStore {
  toast: ToastNotification | null;
  setToast: (toast: ToastNotification) => void;
  resetToast: () => void;
}

export const useToastStore = create<ToastStore>((set) => ({
  toast: null,
  setToast: (toast) => set({ toast }),
  resetToast: () => set({ toast: null }),
}));
