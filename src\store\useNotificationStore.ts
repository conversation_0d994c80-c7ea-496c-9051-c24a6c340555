import { NotificationType } from 'types/notification';
import { create } from 'store';
import {
  getNotifications,
  getNewNotiCount as getNewNotiCountService,
  updateSeen as updateSeenService,
  markAsAllNotificationService,
} from 'services/notification.service';
import { logger } from 'utils/logger';

const PER_PAGE = 30;

type Store = {
  data: NotificationType[];
  loadingMore: boolean;
  firstTimeLoading: boolean;
  hasMore: boolean;
  newNotiCount: number;
  isOpenNoti: boolean;
  openedNotiFirstTime: boolean;
  openNoti: () => void;
  closeNoti: () => void;
  getNewNotiCount: () => Promise<boolean>;
  getMore: () => Promise<void>;
  getFirstTime: () => Promise<boolean>;
  setLoadedFirstTime: () => void;
  updateSeen: (id: string) => void;
  addOne: (notification: NotificationType) => void;
  offset: number;
  markAsAllRead: () => Promise<void>;
  _hasHydrated?: boolean;
};

const initial = {
  data: [],
  newNotiCount: 0,
  isOpenNoti: false,
  openedNotiFirstTime: false,
  loadingMore: false,
  firstTimeLoading: false,
  hasMore: false,
  offset: 0,
};

const useNotificationsStore = create<Store>(
  (set, get) => ({
    ...initial,
    getNewNotiCount: async () => {
      if (get().openedNotiFirstTime) {
        return false;
      }

      const res = await getNewNotiCountService();
      if (res.data && !get().openedNotiFirstTime) {
        set({ newNotiCount: res.data?.unread });
      }
      return true;
    },
    openNoti: () => {
      set({ isOpenNoti: true, openedNotiFirstTime: true });
    },

    markAsAllRead: async () => {
      const { data: previousData } = get();

      await markAsAllNotificationService();

      const updatedData = previousData.map((it) => ({ ...it, unread: false }));

      set({ data: updatedData, newNotiCount: 0 });
    },

    closeNoti: () => {
      set({ isOpenNoti: false });
    },
    getFirstTime: async () => {
      const { offset } = get();

      const res = await getNotifications({ limit: PER_PAGE, offset });
      const { data } = res;
      set({ data: data, hasMore: data.length >= PER_PAGE });

      return true;
    },
    setLoadedFirstTime: () => {
      set({ firstTimeLoading: false });
    },
    getMore: async () => {
      const { hasMore } = get();

      if (!hasMore) {
        return;
      }

      try {
        set({ loadingMore: true });

        const data = get().data;
        const res = await getNotifications({
          limit: PER_PAGE,
          nextId: data[data.length - 1].id,
        });

        const newData = res?.data || [];

        set(({ data: curData }) => {
          return {
            loading: false,
            data: [...curData, ...newData],
            hasMore: res?.data.length >= PER_PAGE,
          };
        });
      } catch (error) {
        logger.error('Failed to load more notifications', { meta: error });
        set({ loadingMore: false });
      } finally {
        set({ loadingMore: false });
      }
    },
    updateSeen: (id: string) => {
      const record = get().data.find((item) => item.id === id);

      if (record) {
        set((state) => ({
          data: state.data.map((item) => (item.id === id ? { ...item, unread: false } : item)),
          newNotiCount: state.newNotiCount - 1 > 0 ? state.newNotiCount - 1 : 0,
        }));
        updateSeenService(id);
      }
    },
    addOne: (notification: NotificationType) => {
      set(({ data, newNotiCount: prevNewNotiCount }) => {
        return { data: [notification, ...data], newNotiCount: prevNewNotiCount + 1 };
      });
    },
  }),
  {
    persistKey: 'notification-store',
    useSession: true,
    partialize: (state) => ({
      data: state.data,
      newNotiCount: state.newNotiCount,
    }),
    merge: (persistedState, currentState) => {
      return {
        ...currentState,
        ...persistedState,
      };
    },
  }
);

export default useNotificationsStore;
