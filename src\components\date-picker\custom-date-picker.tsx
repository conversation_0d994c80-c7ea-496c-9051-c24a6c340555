import { DatePicker, DatePickerProps } from '@mui/x-date-pickers/DatePicker';
import { TextFieldProps, useTheme } from '@mui/material';
import { DD_MMMM_YYYY_WITH_DASH } from 'constant/common';
import { getCookieLocale } from 'utils/cookie-client';
import dayjs, { Dayjs } from 'dayjs';
import { PickersDay, PickersDayProps } from '@mui/x-date-pickers';

const MIN_DATE = '1900-01-01';
const MAX_DATE = '2100-12-31';
export const DIFFERENCE_THAI_YEAR = 543;

interface CustomDatePickerProps extends Omit<DatePickerProps, 'value' | 'onChange'> {
  value: Dayjs | null | undefined;
  onChange: (date: Dayjs | null) => void;
  textFieldProps?: TextFieldProps;
  minDate?: Dayjs | undefined;
  maxDate?: Dayjs | undefined;
  format?: string;
  error?: boolean;
}

export const CustomDatePicker = ({
  value,
  onChange,
  textFieldProps,
  minDate,
  maxDate,
  error,
  // format = DD_MMMM_YYYY_WITH_DASH,
  label,
  ...rest
}: CustomDatePickerProps) => {
  const locale = getCookieLocale() ?? 'th';
  const isThai = locale === 'th';
  const theme = useTheme();

  // Use format depending on locale
  const dateFormat = DD_MMMM_YYYY_WITH_DASH;
  const handleChange = (date: Dayjs | null) => {
    if (date && isThai) {
      date = date.subtract(DIFFERENCE_THAI_YEAR, 'year');
    }
    onChange(date);
  };

  const diffYearByLocale = isThai ? DIFFERENCE_THAI_YEAR : 0;
  const minCustomDate = dayjs(minDate ?? MIN_DATE).add(diffYearByLocale, 'year');
  const maxCustomDate = dayjs(maxDate ?? MAX_DATE).add(diffYearByLocale, 'year');

  const convertDate = (date: Dayjs | null) => {
    if (date && isThai) {
      return dayjs(date).add(DIFFERENCE_THAI_YEAR, 'year').startOf('day');
    }
    return date ? dayjs(date).startOf('day') : null;
  };

  const referenceDate = isThai ? dayjs().add(diffYearByLocale, 'year') : dayjs();

  return (
    <DatePicker
      {...rest}
      format={dateFormat}
      value={value && dayjs(value).isValid() ? convertDate(value) : null}
      onChange={handleChange}
      referenceDate={referenceDate}
      minDate={minCustomDate}
      maxDate={maxCustomDate}
      label={label}
      slots={{
        day: (props: PickersDayProps) => {
          const { day, selected, outsideCurrentMonth } = props;
          const isSelected = selected && !outsideCurrentMonth;
          const isToday = day.isSame(dayjs(), 'day');
          const isReference = day.isSame(referenceDate, 'day');
          return (
            <PickersDay
              {...props}
              sx={{
                ...(isSelected && {
                  backgroundColor: 'primary.main',
                  color: 'white',
                  '&:hover': {
                    backgroundColor: 'primary.main',
                    color: 'white',
                  },
                }),
                ...(isToday && {
                  '&:not(.Mui-selected)': {
                    borderColor: 'primary.main',
                    backgroundColor: 'rgba(25, 118, 210, 0.1)',
                  },
                  '&:hover': {
                    backgroundColor: 'primary.main',
                    borderColor: 'primary.main',
                  },
                }),
                ...(isReference && {
                  border: '1px solid',
                  borderColor: 'primary.main',
                  borderRadius: '50%',
                  backgroundColor: 'rgba(25, 118, 210, 0.1)',
                  '&:hover': {
                    backgroundColor: 'primary.main',
                    borderColor: 'primary.main',
                  },
                }),
                borderColor: 'red',
              }}
            />
          );
        },
      }}
      slotProps={{
        textField: {
          size: 'medium',
          error: error,
          sx: {
            '& .MuiOutlinedInput-root': {
              '& fieldset': {
                borderColor: error ? theme.palette.error.main : 'inherit',
              },
              '&:hover fieldset': {
                borderColor: error ? theme.palette.error.main : 'inherit',
              },
              '&.Mui-focused fieldset': {
                borderColor: error ? theme.palette.error.main : theme.palette.primary.main,
              },
            },
          },
          ...textFieldProps,
        },
        ...rest.slotProps,
      }}
    />
  );
};
