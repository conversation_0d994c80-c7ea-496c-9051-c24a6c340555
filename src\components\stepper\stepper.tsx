import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>per<PERSON><PERSON>,
  StepConnector,
  styled,
  stepConnectorClasses
} from '@mui/material';
import { theme } from 'styles/theme';

interface LabelOnlyStepperProps {
  activeStep: number;
  steps: string[];
  stepperProps?: Omit<StepperProps, 'activeStep' | 'children' | 'connector'>;
}

const CustomConnector = styled(StepConnector)(() => ({
  top: 16,
  [`& .${stepConnectorClasses.line}`]: {
    borderTopWidth: 2,
    borderColor: theme.palette.grey[300],
  },
  [`&.${stepConnectorClasses.active} .${stepConnectorClasses.line}`]: {
    borderColor: theme.palette.primary.main,
  },
  [`&.${stepConnectorClasses.completed} .${stepConnectorClasses.line}`]: {
    borderColor: theme.palette.primary.main,
  },
}));

export const Stepper = ({
  activeStep,
  steps,
  stepperProps
}: LabelOnlyStepperProps) => {
  return (
    <Box
      sx={{
        width: '100%',
        bgcolor: theme.palette.customColors.white,
        p: '16px 40px',
        boxShadow: 1,
        borderRadius: 2,
        // Increase the step icon size
        '& .MuiStepIcon-root': {
          width: 30,
          height: 30,
          '& .MuiSvgIcon-root': {
            fontSize: '1.25rem',
          },
        }
      }}
    >
      <MuiStepper
        nonLinear
        activeStep={activeStep}
        connector={<CustomConnector />}
        {...stepperProps}
      >
        {steps.map(label => (
          <Step key={label} completed={activeStep > steps.indexOf(label)}>
            <StepLabel color="inherit">
              {label}
            </StepLabel>
          </Step>
        ))}
      </MuiStepper>
    </Box>
  );
}
