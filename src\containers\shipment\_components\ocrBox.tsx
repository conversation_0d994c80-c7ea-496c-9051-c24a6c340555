import { Box, IconButton, Typography } from '@mui/material';
import Stack from '@mui/material/Stack';
import { FC, useState } from 'react';
import { Receipt } from 'types';
import OpenInFullIcon from '@mui/icons-material/OpenInFull';
import { useTranslations } from 'next-intl';
import { formatNumberWithCommas } from 'utils';
import { parseFormattedNumber } from 'utils/convert';
import { PQ7ReceiptDetailDrawer } from './pq7-receipt-detail-drawer';

interface OcrBoxProps {
  data: Receipt;
}

export const OcrBox: FC<OcrBoxProps> = ({ data }) => {
  const ocrT = useTranslations('ocr');
  const commonT = useTranslations('common');
  const [isOcrDetailOpen, setIsOcrDetailOpen] = useState(false);

  const totalWeight = parseFormattedNumber(data.totalWeightKg.toString());

  return (
    <Box
      component="div"
      sx={(theme) => ({
        width: '100%',
        padding: '20px',
        borderRadius: '8px',
        backgroundColor: theme.palette.customColors.primary50,
        display: 'flex',
        flexDirection: 'column',
        gap: '8px',
        justifyContent: 'space-between',
        height: 'auto',
      })}
    >
      <Stack direction="row" justifyContent="space-between" mb="8px">
        <Typography variant="body2" fontWeight="bold" sx={{ lineHeight: '24px' }}>
          {data.receiptNumber}{' '}
          <Typography
            component="span"
            variant="caption"
            color="text.secondary"
          >{`(${data.sourceFrom === 'ocr' ? ocrT('source-from-ocr') : ocrT('source-from-ephyto')})`}</Typography>
        </Typography>
        <Stack direction="row" gap={'4px'} alignItems="center">
          <IconButton sx={{ fontSize: '24px' }} onClick={() => setIsOcrDetailOpen(true)}>
            <OpenInFullIcon fontSize="inherit" />
          </IconButton>
        </Stack>
      </Stack>

      <Stack
        direction="row"
        justifyContent="space-between"
        sx={(theme) => ({
          paddingBottom: '8px',
          borderBottom: `1px solid ${theme.palette.customColors.neutralBorder}`,
        })}
      >
        <Typography variant="caption" color="text.secondary">
          {ocrT('destination-country')}
        </Typography>
        <Typography variant="caption" fontWeight="bold">
          {data.destinationCountry}
        </Typography>
      </Stack>
      <Stack
        direction="row"
        justifyContent="space-between"
        sx={(theme) => ({
          paddingBottom: '8px',
          borderBottom: `1px solid ${theme.palette.customColors.neutralBorder}`,
        })}
      >
        <Typography variant="caption" color="text.secondary">
          {ocrT('total-weight')}
        </Typography>
        <Typography
          variant="caption"
          fontWeight="bold"
        >{`${formatNumberWithCommas(totalWeight)} ${commonT('kg')}`}</Typography>
      </Stack>

      <Stack direction="row" justifyContent="space-between">
        <Typography variant="caption" color="text.secondary">
          {ocrT('number-of-boxes')}
        </Typography>
        <Typography variant="caption" fontWeight="bold">
          {formatNumberWithCommas(data.numberOfBoxes ?? 0)}
        </Typography>
      </Stack>
      {data && (
        <PQ7ReceiptDetailDrawer
          data={data}
          open={isOcrDetailOpen}
          toggle={(value) => {
            setIsOcrDetailOpen(value);
          }}
        />
      )}
    </Box>
  );
};
