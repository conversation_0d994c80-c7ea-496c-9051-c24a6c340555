import { Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC } from 'react';
import { colors } from 'styles/colors';
import { PackingHouseDetail } from 'types';
import { getCookieLocale } from 'utils/cookie-client';

interface AdjustWeightInformationProps {
  data: PackingHouseDetail;
}

const flattenData = (data: PackingHouseDetail['meta']['diffVarieties'], locale: 'en' | 'th') => {
  if (!data) return [];
  return data.flatMap((item, varietyIndex) => {
    return item.grades.map((grade, gradeIndex) => ({
      index: `${varietyIndex} ${gradeIndex}`,
      name: `${item.label[locale]} ${grade.label[locale]}`,
      origin: grade.diff.origin || 0,
      current: grade.diff.current || 0,
    }));
  });
};

export const AdjustWeightInformation: FC<AdjustWeightInformationProps> = ({ data }) => {
  const receiveTranslation = useTranslations('receive');
  const locale = getCookieLocale() ?? 'th';
  const tableData = flattenData(data.meta.diffVarieties, locale);

  return (
    <div style={{ width: '100%', margin: '0 auto' }}>
      <Typography sx={{ color: colors.neutral500, fontSize: 14 }}>
        {receiveTranslation('weight-adjusted-help-text')}
      </Typography>
      <TableContainer
        component={Paper}
        sx={{ backgroundColor: 'transparent', outline: 'none', border: 'none', boxShadow: 'none' }}
      >
        <Table aria-label="data table" size="small">
          <TableHead>
            <TableRow sx={{ backgroundColor: 'transparent' }}>
              <TableCell sx={{ fontWeight: 'bold', fontSize: '16px', borderBottom: 'none', paddingX: 0 }}>
                {receiveTranslation('durian-varieties')}
              </TableCell>
              <TableCell align="left" sx={{ fontWeight: 'bold', fontSize: '16px', borderBottom: 'none', paddingX: 0 }}>
                {receiveTranslation('origin-weight')}
              </TableCell>
              <TableCell align="left" sx={{ fontWeight: 'bold', fontSize: '16px', borderBottom: 'none', paddingX: 0 }}>
                {receiveTranslation('received-weight')}
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {tableData.map((row) => (
              <TableRow key={row.index}>
                <TableCell
                  component="th"
                  scope="row"
                  sx={{ fontSize: '16px', borderBottom: 'none', color: colors.neutral500, paddingX: 0 }}
                >
                  {row.name}
                </TableCell>
                <TableCell
                  align="left"
                  sx={{ fontSize: '16px', borderBottom: 'none', color: colors.neutral500, paddingX: 0 }}
                >
                  {row.origin.toLocaleString()}
                </TableCell>
                <TableCell
                  align="left"
                  sx={{ fontSize: '16px', borderBottom: 'none', color: colors.neutral500, paddingX: 0 }}
                >
                  {row.current.toLocaleString()}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
};
