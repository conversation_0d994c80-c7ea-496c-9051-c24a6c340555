import { NextResponse } from 'next/server';

export async function GET(
  request: Request,
  ctx: { params: Promise<{ path: string[] }> }
) {
  const params = await ctx.params;
  const path = params.path;
  if (!path || path.length === 0) {
    return NextResponse.json({ error: 'No asset path provided' }, { status: 400 });
  }

  // Reconstruct the external URL
  const externalBase = process.env.NEXT_PUBLIC_ASSET_DOMAIN;
  const assetPath = path.join('/');
  const targetUrl = `${externalBase}/${assetPath}`;

  let upstream: Response;
  try {
    upstream = await fetch(targetUrl);
  } catch (err) {
    return NextResponse.json(
      { error: 'Failed to fetch external asset', details: String(err) },
      { status: 502 }
    );
  }

  if (!upstream.ok) {
    return NextResponse.json(
      { error: `Upstream responded with ${upstream.status}` },
      { status: upstream.status }
    );
  }

  // Stream the image back, preserving content‐type
  const contentType = upstream.headers.get('content-type') || 'application/octet-stream';
  return new NextResponse(upstream.body, {
    status: 200,
    headers: {
      'Content-Type': contentType,
      'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400',
    },
  });
}
