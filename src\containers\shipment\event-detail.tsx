'use client';

import { <PERSON>, Button, Divider, Grid, Icon<PERSON>utton, Typography } from '@mui/material';
import { PackagingDurianBox } from 'app/(main)/shipment/_components';
import { Breadcrumbs, EventBox } from 'components';
import PackingQrCode from 'components/qr-code-review/qr-code-review';
import UploadProgressUI from 'components/upload-file-progress/upload-file-progress';

import { formatDateWithLocale, renderStatus } from 'containers/event/_components';
import { ArrowBackOutlined, BorderColorOutlined, UpdateOutlined } from '@mui/icons-material';
import { useGetEventStatus } from 'hooks/useGetEventStatus';
import { get } from 'lodash-es';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { FC, useMemo, useState } from 'react';
import { clientRoutes } from 'routes/client-routes';
import { theme } from 'styles/theme';
import { AcceptFileTypes, PackingHouseDetail } from 'types';
import { formatDate, formatNumberWithCommas, getImageUrl, getTotalWeight } from 'utils';
import { getCookieLocale, getLabelByLocale, RecordLabel } from 'utils/cookie-client';
import { SealDrawer } from './_components';
import { ShipmentAccordion } from './receipt-accordian';
import { basePathProd } from 'configs/app-config';
import { useUserStore } from 'store/useUserStore';
import { formatOrchardNo } from 'utils/format';
import { useFeatureFlag } from 'hooks/useFeatureFlag';

const showDeleteBtn = false;

type EventDetailProps = {
  shipmentDetail: PackingHouseDetail;
};

export const EventDetail: FC<EventDetailProps> = ({ shipmentDetail: data }) => {
  const shipmentTranslation = useTranslations('shipment');
  const commonTranslation = useTranslations('common');
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const { getStatusLabel } = useGetEventStatus();
  const { ENABLED_SHIPMENT_HISTORY } = useFeatureFlag();

  const user = useUserStore((state) => state.user);

  const supplier = get(user, 'profile.supplierId', {});

  const packingHouse = get(supplier, 'packingHouse');

  const doaNumber = get(packingHouse, 'doaNumber.number', '');

  const locale = getCookieLocale();

  const { eventStatus, eventStatusLabel } = getStatusLabel(data.status, data.type);
  const router = useRouter();

  const isDisabledEdit = useMemo(() => {
    const isWaiting = eventStatus === 'waiting';

    const isDraft = eventStatus === 'draft';

    if (isWaiting || isDraft) {
      return false;
    }

    return true;
  }, [eventStatus]);

  const isDisabledSeal = useMemo(() => {
    return eventStatus === 'draft';
  }, [eventStatus]);

  return (
    <>
      <Box sx={{ padding: '20px', backgroundColor: theme.palette.customColors.lightGray }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Breadcrumbs
              items={[
                {
                  label: shipmentTranslation('shipments'),
                  href: clientRoutes.shipment,
                },
                {
                  label: data?.batchlot || '',
                  href: clientRoutes.event,
                },
              ]}
            />
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              <IconButton
                onClick={() => {
                  router.back();
                }}
              >
                <ArrowBackOutlined fontSize="small" />
              </IconButton>
              <Typography variant="h3">{data?.name} </Typography>
              <Typography sx={{ fontSize: '20px', color: theme.palette.customColors.gray }}>
                - {data?.batchlot || '--'}
              </Typography>
              {renderStatus(eventStatus, eventStatusLabel)}
            </Box>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            {ENABLED_SHIPMENT_HISTORY && eventStatus !== 'draft' && (
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', justifyContent: 'end' }}>
                <Button
                  variant="outlined"
                  sx={{ height: '40px' }}
                  onClick={() => {
                    router.push(`${clientRoutes.shipment}/${data?.id}/history`);
                  }}
                >
                  <UpdateOutlined />
                </Button>
              </Box>
            )}
            {(eventStatus === 'waiting' || eventStatus === 'draft') && (
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', justifyContent: 'end' }}>
                {showDeleteBtn && (
                  <Button
                    variant="text"
                    sx={{ height: '40px', width: '200px', color: theme.palette.error.main }}
                    onClick={() => {}}
                  >
                    {shipmentTranslation('delete-record')}
                  </Button>
                )}

                {!isDisabledEdit && ENABLED_SHIPMENT_HISTORY && (
                  <Button
                    variant="outlined"
                    sx={{ height: '40px' }}
                    onClick={() => {
                      router.push(`${clientRoutes.shipment}/${data?.id}/edit`);
                    }}
                  >
                    <BorderColorOutlined />
                  </Button>
                )}

                {!isDisabledEdit && !ENABLED_SHIPMENT_HISTORY && (
                  <Button
                    variant="outlined"
                    sx={{ height: '40px', padding: '0 20px' }}
                    onClick={() => {
                      router.push(`${clientRoutes.shipment}/${data?.id}/edit`);
                    }}
                  >
                    {shipmentTranslation('edit-record')}
                  </Button>
                )}

                <Button
                  variant="contained"
                  sx={{ height: '40px', width: '200px' }}
                  onClick={() => {
                    setIsOpen(true);
                  }}
                  disabled={isDisabledSeal}
                >
                  {shipmentTranslation('seal-record')}
                </Button>
              </Box>
            )}
          </Box>
        </Box>
        <Grid container spacing={2} sx={{ width: '100%', marginTop: 2 }}>
          {/* Left */}
          <Grid
            size={{
              xs: 12,
              md: 12,
              lg: 6,
              xl: 6,
            }}
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {/* Packing */}
              <Box
                sx={{
                  flex: 1,
                  borderRadius: '8px',
                  border: '1px solid #0000001A',
                  background: 'white',
                  height: 'auto',
                  p: 2,
                }}
                component={'div'}
              >
                <Typography mb={1} variant="body1">
                  {shipmentTranslation('packaging-information')}
                </Typography>
                <Box
                  sx={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1,
                    height: 'auto',
                    overflowY: 'auto',
                    flexWrap: 'wrap',
                  }}
                >
                  {data?.packing && (
                    <Grid container spacing={2}>
                      {data.packing.map((it) => {
                        const variety = get(it, 'varieties[0]', {}) as RecordLabel;
                        return (
                          <PackagingDurianBox
                            key={it.id}
                            brandName={get(it, `brandNameInfo.label[${locale}]`) ?? ''}
                            variety={
                              getLabelByLocale({
                                ...variety,
                                label: it.varietyName
                                  ? {
                                      th: it.varietyName,
                                      en: it.varietyName,
                                    }
                                  : variety.label,
                              }) as string
                            }
                            grade={it.varieties?.[0]?.grades?.[0]?.label?.[locale] || ''}
                            netWeight={formatNumberWithCommas(it.weightKg)}
                            totalBox={formatNumberWithCommas(it.numberOfBoxes)}
                            typeOfBox={it.productTypeInfo?.label[locale] ?? ''}
                            packingDate={it.packingDate ? formatDateWithLocale(it.packingDate * 1000) : ''}
                            isDelete={false}
                          />
                        );
                      })}
                    </Grid>
                  )}
                </Box>
              </Box>
              {/* Receipt */}
              <Box
                sx={{
                  flex: 1,
                  borderRadius: '8px',
                  border: '1px solid #0000001A',
                  background: 'white',
                  height: 'auto',
                  padding: 2,
                }}
              >
                <Typography variant="body1" mb={1}>
                  {shipmentTranslation('pq7-receipts')}
                </Typography>
                <Box
                  sx={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1,
                    overflowY: 'auto',
                    height: 'auto',
                    flexWrap: 'wrap',
                  }}
                >
                  <ShipmentAccordion data={data?.pq7ReceiptDetails} />
                </Box>
              </Box>
              {/* Source Material */}
              <Box
                sx={{
                  flex: 1,
                  borderRadius: '8px',
                  border: '1px solid #0000001A',
                  background: 'white',
                  height: 'auto',
                  padding: 2,
                }}
              >
                <Typography mb={1} variant="body1">
                  {shipmentTranslation('source-materials')}
                </Typography>
                <Box
                  sx={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1,
                    overflowY: 'auto',
                    height: 'auto',
                  }}
                >
                  {data?.sourceMaterials?.map((it) => (
                    <EventBox
                      padding="16px 0"
                      hasBorderBottom
                      key={it.id}
                      imgUrl={getImageUrl(get(it, 'images[0].filenameDisk'))}
                      title={it.batchlot}
                      description={it.name ?? ''}
                      customDescription={
                        <Box component="div" sx={{ display: 'flex', gap: 1 }}>
                          <Typography
                            variant="caption"
                            color={theme.palette.customColors.blueHighlight}
                            onClick={() => {
                              window.open(
                                `${basePathProd}/${clientRoutes.eventIncoming}/${it.originBatchlotId}`,
                                '_blank'
                              );
                            }}
                            sx={{ cursor: 'pointer' }}
                          >
                            {it.originBatchlot}
                          </Typography>
                          <Divider component="p" orientation="vertical" flexItem />
                          <Typography variant="caption" color="text.secondary">
                            {formatNumberWithCommas(getTotalWeight(it.varieties))} {commonTranslation('kg')}
                          </Typography>
                        </Box>
                      }
                    />
                  ))}
                </Box>
              </Box>
            </Box>
          </Grid>
          {/* Right */}
          <Grid
            size={{
              xs: 12,
              md: 12,
              lg: 6,
              xl: 6,
            }}
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {/* QR */}
              <Box
                sx={{
                  flex: 1,
                  borderRadius: '8px',
                  border: '1px solid #0000001A',
                  background: 'white',
                  height: 'auto',
                  p: 2,
                }}
                component={'div'}
              >
                <Typography variant="body1">{shipmentTranslation('qr-codes')}</Typography>
                <Box
                  sx={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1,
                    height: 'auto',
                    overflowY: 'auto',
                    flexWrap: 'wrap',
                  }}
                >
                  {data?.packing && (
                    <Grid container spacing={2} mt="16px">
                      {data.packing.map((it) => {
                        const variety = get(it, 'varieties[0]', {}) as RecordLabel;
                        return (
                          <Grid
                            key={it.id}
                            size={{
                              md: 12,
                              lg: 12,
                            }}
                          >
                            <PackingQrCode
                              data={{
                                batchNumber: get(it, 'batchlot'),
                                variety: getLabelByLocale({
                                  ...variety,
                                  label: it.varietyName
                                    ? {
                                        th: it.varietyName,
                                        en: it.varietyName,
                                      }
                                    : variety.label,
                                }) as string,
                                grade: getLabelByLocale(get(it, 'varieties[0].grades[0]') as unknown as RecordLabel),
                                totalBoxes: it.numberOfBoxes.toString(),
                                netWeight: it.weightKg.toString(),
                                boxType: getLabelByLocale(get(it, 'productTypeInfo') as unknown as RecordLabel),
                                qrUrl: get(it, 'qrCodes[0].qrUrl', ''),
                                productOf: 'Thailand',
                                brand: 'Fresh Durian',
                                exportCompany: data.pq7ReceiptDetails?.nameOfExportingCompany,
                                packingDate: it?.packingDate ? formatDate(it.packingDate * 1000) : '',
                                orchardRegisterNumber: data.pq7ReceiptDetails?.orchardRegisterNumber
                                  ? `AC ${formatOrchardNo(data.pq7ReceiptDetails?.orchardRegisterNumber)}`
                                  : undefined,
                                packingHouseRegisterNumber: doaNumber,
                                exportTo: data.pq7ReceiptDetails?.destinationCountry,
                              }}
                            />
                          </Grid>
                        );
                      })}
                    </Grid>
                  )}
                </Box>
              </Box>

              {/* Document */}
              <Box
                sx={{
                  flex: 1,
                  borderRadius: '8px',
                  border: '1px solid #0000001A',
                  background: 'white',
                  height: 'auto',
                  padding: 2,
                }}
              >
                <Typography mb={1} variant="body1">
                  {shipmentTranslation('documents')}
                </Typography>
                <Box
                  sx={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1,
                    overflowY: 'auto',
                    height: 'auto',
                    flexWrap: 'wrap',
                  }}
                >
                  {data?.files?.product.length || data?.files?.shipment.length ? (
                    [...get(data, 'files.product', []), ...get(data, 'files.shipment', [])]?.map((it) => {
                      return (
                        <UploadProgressUI
                          key={it.id}
                          fileId={it.id}
                          fileName={it.filenameDownload}
                          fileSize={it.filesize}
                          fileUrl={it.filenameDisk}
                          uploading={false}
                          fileType={it.mimeType as AcceptFileTypes}
                        />
                      );
                    })
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      {shipmentTranslation('no-document')}
                    </Typography>
                  )}
                </Box>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Box>
      {data && <SealDrawer data={data} open={isOpen} toggle={(value) => setIsOpen(value)} />}
    </>
  );
};
