import { useTranslations } from 'next-intl';
import { useCallback } from 'react';
import { EventStatusEnum } from 'types';

export type ProductType = 'harvesting' | 'receiving' | 'shipment';

export const useGetEventStatus = () => {
  const filterT = useTranslations('filter');

  const getStatusLabel = useCallback(
    (status: string, productType: string) => {
      let eventStatus: EventStatusEnum = EventStatusEnum.DRAFT;
      let eventStatusLabel = '';

      if (productType === 'receiving') {
        eventStatus = EventStatusEnum.RECEIVED;
        eventStatusLabel = filterT('status-received');
      } else if (productType === 'qr') {
        if (status === 'available') {
          eventStatus = EventStatusEnum.AVAILABLE;
          eventStatusLabel = filterT('status-available');
        } else if (status === 'assigned') {
          eventStatus = EventStatusEnum.ASSIGNED;
          eventStatusLabel = filterT('status-assigned');
        }
      } else if (productType === 'harvesting' || productType === 'shipment') {
        if (status === 'published') {
          eventStatus = EventStatusEnum.WAITING;
          eventStatusLabel =
            productType === 'harvesting' ? filterT('status-incoming-waiting') : filterT('status-shipment-waiting');
        } else if (status === 'draft') {
          eventStatus = EventStatusEnum.DRAFT;
          eventStatusLabel = filterT('status-draft');
        } else if (status === 'consumed') {
          eventStatus = EventStatusEnum.RECEIVED;
          eventStatusLabel = filterT('status-received');
        } else if (status === 'sealed') {
          eventStatus = EventStatusEnum.SEALED;
          eventStatusLabel = filterT('status-sealed');
        } else if (status === 'rejected') {
          eventStatus = EventStatusEnum.REJECTED;
          eventStatusLabel = filterT('status-rejected');
        }
      }

      return {
        eventStatus,
        eventStatusLabel,
      };
    },
    [filterT]
  );

  return { getStatusLabel };
};
