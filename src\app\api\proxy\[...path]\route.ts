import { NextRequest } from 'next/server';

import { handler } from 'utils/handler';

export async function GET(
  req: NextRequest,
  ctx: { params: Promise<{ path: string[] }> }
) {
  const params = await ctx.params;
  const path = params.path;
  return handler(req, 'GET', path);
}

export async function POST(
  req: NextRequest,
  ctx: { params: Promise<{ path: string[] }> }
) {
  const path = (await ctx.params).path;
  return handler(req, 'POST', path);
}

export async function PATCH(
  req: NextRequest,
  ctx: { params: Promise<{ path: string[] }> }
) {
  const path = (await ctx.params).path;
  return handler(req, 'PATCH', path);
}

export async function PUT(
  req: NextRequest,
  ctx: { params: Promise<{ path: string[] }> }
) {
  const path = (await ctx.params).path;
  return handler(req, 'PUT', path);
}

export async function DELETE(
  req: NextRequest,
  ctx: { params: Promise<{ path: string[] }> }
) {
  const path = (await ctx.params).path;
  return handler(req, 'DELETE', path);
}
