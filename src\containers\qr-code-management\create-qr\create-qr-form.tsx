/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { Box, InputAdornment } from '@mui/material';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';
import { CreateQrFormSchema } from './schema';
import { FormCheckbox, FormDatePickerInput, FormSelect, FormTextInput } from 'components';
import { useMasterDataStore } from 'store/useMaterDataStore';
import { FormNumberFormatInput } from 'components/hook-form/form-format-number-input';

const maxlengthOrchardNo = 17;

export const CreateQrForm = () => {
  const qrT = useTranslations('qr');
  const ocrInformationT = useTranslations('ocr-information');

  const { productTypes, getProductTypeLabel } = useMasterDataStore();

  const productTypeOptions = productTypes.map((it) => ({ value: it.id, label: getProductTypeLabel(it.id) }));

  const {
    control,
    formState: { errors },
  } = useFormContext<CreateQrFormSchema>();

  return (
    <Box component="form" sx={{ display: 'flex', flexDirection: 'column', gap: 2, height: '100%' }}>
      <FormTextInput<CreateQrFormSchema>
        label={qrT('name-of-the-exporting-company')}
        placeholder={qrT('name-of-exporting-company-placeholder')}
        name="company"
        control={control}
        errors={errors}
        required
      />
      <FormNumberFormatInput<CreateQrFormSchema>
        label={qrT('orchard-register-number')}
        placeholder={qrT('orchard-register-number-placeholder')}
        name="orchardNumber"
        control={control}
        errors={errors}
        required
        startAdornment={<InputAdornment position="start">{ocrInformationT('prefix-orchard-no')}</InputAdornment>}
        minLength={maxlengthOrchardNo}
        maxLength={maxlengthOrchardNo}
      />
      <FormDatePickerInput<CreateQrFormSchema>
        label={qrT('packing-date')}
        placeholder={qrT('packing-date-placeholder')}
        name="packingDate"
        control={control}
        errors={errors}
        required
      />
      <FormSelect<CreateQrFormSchema>
        label={qrT('product-type')}
        placeholder={qrT('product-type-placeholder')}
        name="productType"
        control={control}
        errors={errors}
        options={productTypeOptions}
        required
      />
      <FormTextInput<CreateQrFormSchema>
        label={qrT('export-to')}
        placeholder={qrT('export-to-placeholder')}
        name="exportTo"
        control={control}
        errors={errors}
        multiline
        rows={4}
        maxLength={1000}
        sx={{
          '& .MuiInputBase-root': {},
        }}
      />
      <FormTextInput<CreateQrFormSchema>
        label={qrT('description')}
        placeholder={qrT('description-placeholder')}
        name="description"
        control={control}
        errors={errors}
        multiline
        rows={4}
        maxLength={500}
        sx={{
          '& .MuiInputBase-root': {},
        }}
      />

      <Box />
      <FormCheckbox<CreateQrFormSchema>
        label={qrT('create-another')}
        name="createAnother"
        control={control}
        errors={errors}
      />
    </Box>
  );
};
