'use client';

import { useRouter } from 'next/navigation';

const useUpdateQueryParam = () => {
  const router = useRouter();

  return (paramsObject: { [key: string]: string | null }, initialPath?: string) => {
    const currentPath = initialPath ?? window.location.pathname;
    const params = new URLSearchParams();

    // Update or delete query params based on the paramsObject
    Object.entries(paramsObject).forEach(([key, value]) => {
      if (value) {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });

    router.push(`${currentPath}?${params.toString()}`);
  };
};

export default useUpdateQueryParam;
