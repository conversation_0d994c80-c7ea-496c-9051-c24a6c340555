/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { render } from '@testing-library/react';
import { Drawer } from '../drawer';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';

describe('Drawer Snapshot', () => {
  it('matches snapshot when open with action buttons', () => {
    const theme = createTheme({
      palette: {
        customColors: {
          gradientAppBgColor: '#f5f5f5',
          black: '#000000',
          divider: '#e0e0e0',
          primary: '#1976d2',
          primary10: '#e3f2fd',
          primary50: '#90caf9',
          primary100: '#42a5f5',
          warning100: '#fff8e1',
          warning400: '#ffb300',
          warning700: '#ff6f00',
          success100: '#e8f5e9',
          success400: '#66bb6a',
          success700: '#388e3c',
        },
      } as any,
    });

    const { container } = render(
      <ThemeProvider theme={theme}>
        <Drawer
          open={true}
          drawerTitle="Test Drawer"
          onClose={() => {}}
          onConfirm={() => {}}
          hasActionBtn={true}
        >
          <div>Drawer Content</div>
        </Drawer>
      </ThemeProvider>
    );

    expect(container).toMatchSnapshot();
  });

  it('matches snapshot when closed', () => {
    const theme = createTheme({
      palette: {
        customColors: {
          gradientAppBgColor: '#f5f5f5',
          black: '#000000',
          divider: '#e0e0e0',
          primary: '#1976d2',
          primary10: '#e3f2fd',
          primary50: '#90caf9',
          primary100: '#42a5f5',
          warning100: '#fff8e1',
          warning400: '#ffb300',
          warning700: '#ff6f00',
          success100: '#e8f5e9',
          success400: '#66bb6a',
          success700: '#388e3c',
        } as any,
      },
    });

    const { container } = render(
      <ThemeProvider theme={theme}>
        <Drawer open={false} drawerTitle="Closed Drawer" />
      </ThemeProvider>
    );

    expect(container).toMatchSnapshot();
  });
});
