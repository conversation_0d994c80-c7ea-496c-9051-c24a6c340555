import { Button, Grid } from '@mui/material';
import { FC } from 'react';
import { activeButtonSelectStyle, normalButtonSelectStyle } from './filter-table.styles';
  import Image from 'next/image';

interface FilterOption {
  label: string;
  value: string;
  icon?: string;
}

interface FilterButtonSelectionProps {
  options: FilterOption[];
  selectedValue: string;
  onSelectionChange: (value: string) => void;
  totalOptionsLength?: number;
}

export const FilterButtonSelection: FC<FilterButtonSelectionProps> = ({
  options,
  selectedValue,
  onSelectionChange,
  totalOptionsLength,
}) => {
  const itemsLength = totalOptionsLength ?? options.length;

  const size = itemsLength > 3 ? 6 : 4;

  return (
    <Grid container spacing={2}>
      {options.map((option) => (
        <Grid key={option.value} size={size}>
          <Button
            fullWidth
            sx={selectedValue === option.value ? activeButtonSelectStyle : normalButtonSelectStyle}
            onClick={() => onSelectionChange(option.value)}
            startIcon={
              option.icon ? (
                <Image src={option.icon} alt={option.label} width={16} height={16} unoptimized />
              ) : undefined
            }
          >
            {option.label}
          </Button>
        </Grid>
      ))}
    </Grid>
  );
};
