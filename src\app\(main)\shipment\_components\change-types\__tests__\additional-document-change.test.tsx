import { render, screen } from '@testing-library/react';
import { AdditionalDocumentChange } from '../additional-document-change';

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: jest.fn(() => (key: string) => {
    const translations: Record<string, string> = {
      'history-field-additional-documents': 'Additional documents',
      'history-value-removed': 'Removed',
      'history-value-added': 'Added',
    };
    return translations[key] || key;
  }),
}));

describe('AdditionalDocumentChange', () => {
  const mockChanges = [
    {
      field: 'additionalDocuments',
      fieldLabel: 'Additional documents',
      oldValue: 'Commercial_Invoice.png',
      changeType: 'removed',
    },
    {
      field: 'additionalDocuments',
      fieldLabel: 'Additional documents',
      oldValue: 'Export_Declaration_Form.pdf',
      changeType: 'removed',
    },
    {
      field: 'additionalDocuments',
      fieldLabel: 'Additional documents',
      newValue: 'shipment_photo124.jpg',
      changeType: 'added',
    },
    {
      field: 'additionalDocuments',
      fieldLabel: 'Additional documents',
      newValue: 'New_Export_Declaration_Form.pdf',
      changeType: 'added',
    },
  ];

  it('renders additional documents field title', () => {
    render(<AdditionalDocumentChange changes={mockChanges} />);
    
    expect(screen.getByText('Additional documents')).toBeInTheDocument();
  });

  it('renders removed documents section', () => {
    render(<AdditionalDocumentChange changes={mockChanges} />);
    
    expect(screen.getByText('Removed')).toBeInTheDocument();
    expect(screen.getByText('Commercial_Invoice.png')).toBeInTheDocument();
    expect(screen.getByText('Export_Declaration_Form.pdf')).toBeInTheDocument();
  });

  it('renders added documents section', () => {
    render(<AdditionalDocumentChange changes={mockChanges} />);
    
    expect(screen.getByText('Added')).toBeInTheDocument();
    expect(screen.getByText('shipment_photo124.jpg')).toBeInTheDocument();
    expect(screen.getByText('New_Export_Declaration_Form.pdf')).toBeInTheDocument();
  });

  it('renders appropriate icons for different file types', () => {
    render(<AdditionalDocumentChange changes={mockChanges} />);
    
    // Check for PDF icons
    const pdfIcons = screen.getAllByTestId('PictureAsPdfIcon');
    expect(pdfIcons.length).toBeGreaterThan(0);
    
    // Check for image icons
    const imageIcons = screen.getAllByTestId('ImageIcon');
    expect(imageIcons.length).toBeGreaterThan(0);
  });

  it('handles only removed documents', () => {
    const removedOnlyChanges = [
      {
        field: 'additionalDocuments',
        oldValue: 'removed_document.pdf',
        changeType: 'removed',
      },
    ];

    render(<AdditionalDocumentChange changes={removedOnlyChanges} />);
    
    expect(screen.getByText('Removed')).toBeInTheDocument();
    expect(screen.getByText('removed_document.pdf')).toBeInTheDocument();
    expect(screen.queryByText('Added')).not.toBeInTheDocument();
  });

  it('handles only added documents', () => {
    const addedOnlyChanges = [
      {
        field: 'additionalDocuments',
        newValue: 'added_document.pdf',
        changeType: 'added',
      },
    ];

    render(<AdditionalDocumentChange changes={addedOnlyChanges} />);
    
    expect(screen.getByText('Added')).toBeInTheDocument();
    expect(screen.getByText('added_document.pdf')).toBeInTheDocument();
    expect(screen.queryByText('Removed')).not.toBeInTheDocument();
  });

  it('handles empty filename gracefully', () => {
    const emptyFilenameChanges = [
      {
        field: 'additionalDocuments',
        newValue: '',
        changeType: 'added',
      },
    ];

    render(<AdditionalDocumentChange changes={emptyFilenameChanges} />);
    
    expect(screen.getByText('Added')).toBeInTheDocument();
    // Should render empty string without crashing
  });

  it('renders removed documents without special styling', () => {
    const removedChanges = [
      {
        field: 'additionalDocuments',
        oldValue: 'removed_document.pdf',
        changeType: 'removed',
      },
    ];

    render(<AdditionalDocumentChange changes={removedChanges} />);

    const removedDocument = screen.getByText('removed_document.pdf');
    // Removed documents no longer have line-through styling
    expect(removedDocument).not.toHaveStyle({ textDecoration: 'line-through' });
  });

  it('uses correct icon colors for different file types', () => {
    const pdfChange = [
      {
        field: 'additionalDocuments',
        newValue: 'test.pdf',
        changeType: 'added',
      },
    ];

    render(<AdditionalDocumentChange changes={pdfChange} />);
    
    const pdfIcon = screen.getByTestId('PictureAsPdfIcon');
    expect(pdfIcon).toHaveStyle({ color: '#f44336' }); // Red for PDF
  });
});
