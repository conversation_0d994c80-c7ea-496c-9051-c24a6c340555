'use client';
import { usePathname } from 'next/navigation';
import { AnimatePresence, motion } from 'framer-motion';
import { FC, PropsWithChildren } from 'react';

export const PageTransition: FC<PropsWithChildren> = ({ children }) => {
  const pathname = usePathname();
  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={pathname}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0 }}
        id="page-transition"
        style={{ flex: 1, display: 'flex', width: '100%', flexDirection: 'column', position: 'relative', minHeight: '100%' }}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
};
