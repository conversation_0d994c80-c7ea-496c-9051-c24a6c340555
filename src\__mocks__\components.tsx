import React from 'react';

export const DetailRow = ({ title, content }: { title: string; content?: React.ReactNode }) => (
  <div data-testid="detail-row">
    <span data-testid="detail-row-title">{title}</span>
    <span data-testid="detail-row-content">{content}</span>
  </div>
);

export const ImageReviewModal = ({ imageUrl }: { imageUrl: string | null }) => (
  <div data-testid="image-review-modal" data-image-url={imageUrl}>
    Image Modal
  </div>
);

export const PlotOptionBox = ({
  plotNumber,
  plotId,
  plotGap,
  plotArea,
  plotAreaUnit,
  renderActionButtons,
  isError = false
}: {
  plotNumber: string;
  plotId: string;
  plotGap: string;
  plotArea: number;
  plotAreaUnit: string;
  renderActionButtons?: () => React.ReactNode;
  isError?: boolean;
}) => (
  <div
    data-testid="plot-option-box"
    data-plot-number={plotNumber}
    data-plot-id={plotId}
    data-plot-gap={plotGap}
    data-plot-area={plotArea}
    data-plot-area-unit={plotAreaUnit}
    data-is-error={isError}
  >
    <span>{plotNumber}</span>
    <span>{plotId}</span>
    <span>{plotGap}</span>
    <span>{plotArea} {plotAreaUnit}</span>
    {renderActionButtons && renderActionButtons()}
  </div>
);
