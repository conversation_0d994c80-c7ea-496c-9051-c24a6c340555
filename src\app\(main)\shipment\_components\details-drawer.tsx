import { Box } from '@mui/material';
import { get } from 'lodash-es';
import { useTranslations } from 'next-intl';
import { FC } from 'react';

import { Drawer, EventBox } from 'components';
import { useCreateShipmentStore } from 'store/useCreateShipmentStore';
import { clientRoutes } from 'routes/client-routes';
import { getImageUrl } from 'utils';
import { basePathProd } from 'configs/app-config';

interface DetailsDrawerProps {
  open: boolean;
  toggle: (open: boolean) => void;
}

export const DetailsDrawer: FC<DetailsDrawerProps> = ({ open, toggle }) => {
  const shipmentT = useTranslations('shipment');
  const { receivingDetails: receivingData } = useCreateShipmentStore();

  const onClose = () => {
    toggle(false);
  };

  const drawerTitle = shipmentT('batches_detail_drawer_title');

  const onClick = (productId: string) => {
    const url = `${basePathProd}/${clientRoutes.eventReceiving}/${productId}?type=view`;

    const newWindow = window.open(url, '_blank');
    if (newWindow) {
      newWindow.focus();
    }
  };

  const length = receivingData?.length || 0;

  return (
    <Drawer anchor="right" disableScrollLock drawerTitle={drawerTitle} open={open} onClose={onClose} hasActionBtn={false}>
      <Box
        id="detail-drawer"
        component="div"
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: `calc(100vh - 70px)`,
          overflow: 'auto',
          py: '12px',
        }}
      >
        {receivingData?.map((it, index) => {
          const imageUrl = get(it, 'images[0].filenameDisk', null);

          const imageUrlLink = imageUrl ? getImageUrl(imageUrl) : basePathProd + '/assets/images/no-image.png';

          const isLastItem = length - 1 === index

          return (
            <EventBox
              inDrawer={true}
              hasBorderBottom={!isLastItem}
              onClick={() => onClick(it.productId)}
              key={it.id}
              imgUrl={imageUrlLink}
              title={it.batchlot}
              description={get(it, 'farm.name')}
              customDescription={get(it, 'name')}
            />
          );
        })}
      </Box>
    </Drawer>
  );
};
