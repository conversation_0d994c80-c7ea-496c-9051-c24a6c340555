'use client';

import { createContext, FC, ReactNode, RefObject, useContext, useEffect, useRef } from 'react';
import { capturePosthog } from 'utils/posthog';

export type QrExportData = {
  batchNumber: string;
  variety: string;
  grade: string;
  totalBoxes: string;
  brand: string;
  netWeight: string;
  boxType: string;
  qrUrl: string;
  exportCompany?: string;
  orchardRegisterNumber?: string;
  packingHouseRegisterNumber?: string;
  packingDate?: string;
  productOf?: string;
  exportTo?: string;
};

type QrCodeReviewContextState = {
  qrExportData: QrExportData;
  contentRef: RefObject<HTMLDivElement | null>;
  fullContentRef: RefObject<HTMLDivElement | null>;
  textContentRef: RefObject<HTMLDivElement | null>;
};

const QrCodeReviewContext = createContext<QrCodeReviewContextState | undefined>(undefined);

export const useQrCodeReviewContext = () => {
  const context = useContext(QrCodeReviewContext);
  if (!context) {
    throw new Error('useQrCodeReviewContext must be used within a QrCodeReviewProvider');
  }
  return context;
};

export type QrCodeReviewProviderProps = {
  children: ReactNode;
  data: QrExportData;
  capturedEventName?: string;
};

export const QrCodeReviewProvider: FC<QrCodeReviewProviderProps> = (props) => {
  const { children, data, capturedEventName = 'shipment_qr_code_review' } = props;

  const contentRef = useRef<HTMLDivElement>(null);
  const fullContentRef = useRef<HTMLDivElement>(null);
  const textContentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!capturedEventName) return;
    capturePosthog(capturedEventName);
  }, [capturedEventName]);

  return (
    <QrCodeReviewContext.Provider
      value={{
        qrExportData: data,
        contentRef,
        fullContentRef,
        textContentRef,
      }}
    >
      {children}
    </QrCodeReviewContext.Provider>
  );
};
