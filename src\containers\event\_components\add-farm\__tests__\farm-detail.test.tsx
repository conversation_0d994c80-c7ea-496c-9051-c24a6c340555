import React from 'react';
import { screen, fireEvent } from '@testing-library/react';
import { Farm, Plot } from 'types';
import { render } from 'configs/test-util';

// Use automatic mock for components
jest.mock('components');

// Mock the UploadPlotImage component
jest.mock('../upload-plot-image', () => {
  return function MockUploadPlotImage({ onChange }: { onChange: (imageString: string) => void }) {
    return (
      <button
        data-testid="upload-plot-image"
        onClick={() => onChange('mock-image-string')}
      >
        Upload Image
      </button>
    );
  };
});

// Mock the utils
jest.mock('utils', () => ({
  getImageUrl: jest.fn((image) => image ? `https://example.com/${image}` : null),
}));

// Mock the theme
jest.mock('styles/theme', () => ({
  theme: {
    palette: {
      customColors: {
        gray: '#666666',
      },
    },
  },
}));

// Mock Next.js Image component
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: { src: string; alt: string; [key: string]: unknown }) {
    // eslint-disable-next-line @next/next/no-img-element
    return <img src={src} alt={alt} {...props} data-testid="next-image" />;
  };
});

// Mock assets
jest.mock('assets/icons/empty-state.svg', () => 'empty-state-icon.svg');

// Import the component after mocks
import { FarmDetail } from '../farm-detail';

describe('FarmDetail', () => {
  const mockFarm: Farm = {
    id: 'farm-1',
    name: 'Test Farm',
    address: '123 Test Street',
    gln: 'GLN123',
    gap: 'GAP123',
  };

  const mockPlots: Plot[] = [
    {
      id: 'plot-1',
      name: 'Plot 1',
      plotId: '1234567890123456',
      area: 1000,
      areaUnit: 'rai',
      gap: 'GAP001',
      image: 'image1.jpg',
    },
    {
      id: 'plot-2',
      name: 'Plot 2',
      plotId: '2345678901234567',
      area: 1500,
      areaUnit: 'rai',
      gap: 'GAP002',
    },
  ];

  const mockProps = {
    farm: mockFarm,
    plots: mockPlots,
    addPlot: jest.fn(),
    addImageToPlot: jest.fn(),
    removeImageFromPlot: jest.fn(),
    removePlot: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Farm Information Display', () => {
    it('should render farm name and address correctly', () => {
      render(<FarmDetail {...mockProps} />);

      const detailRows = screen.getAllByTestId('detail-row');
      expect(detailRows).toHaveLength(2);

      const titles = screen.getAllByTestId('detail-row-title');
      const contents = screen.getAllByTestId('detail-row-content');

      expect(titles[0]).toHaveTextContent('farm-name');
      expect(contents[0]).toHaveTextContent('Test Farm');
      expect(titles[1]).toHaveTextContent('farm-address');
      expect(contents[1]).toHaveTextContent('123 Test Street');
    });

    it('should handle missing farm data gracefully', () => {
      const farmWithMissingData: Farm = {
        id: 'farm-2',
        name: null as unknown as string,
        address: null as unknown as string,
      };

      render(<FarmDetail {...mockProps} farm={farmWithMissingData} />);

      const contents = screen.getAllByTestId('detail-row-content');
      // The component uses `farm.name ?? '--'` so null values should show as '--'
      expect(contents[0]).toHaveTextContent('--');
      expect(contents[1]).toHaveTextContent('--');
    });
  });

  describe('Plot Display', () => {
    it('should render plots correctly when plots exist', () => {
      render(<FarmDetail {...mockProps} />);

      const plotBoxes = screen.getAllByTestId('plot-option-box');
      expect(plotBoxes).toHaveLength(2);

      expect(plotBoxes[0]).toHaveAttribute('data-plot-number', 'Plot 1');
      expect(plotBoxes[0]).toHaveAttribute('data-plot-id', '1234567890123456');
      expect(plotBoxes[0]).toHaveAttribute('data-plot-gap', 'GAP001');
      expect(plotBoxes[0]).toHaveAttribute('data-plot-area', '1000');
      expect(plotBoxes[0]).toHaveAttribute('data-plot-area-unit', 'rai');

      expect(plotBoxes[1]).toHaveAttribute('data-plot-number', 'Plot 2');
      expect(plotBoxes[1]).toHaveAttribute('data-plot-id', '2345678901234567');
      expect(plotBoxes[1]).toHaveAttribute('data-plot-gap', 'GAP002');
      expect(plotBoxes[1]).toHaveAttribute('data-plot-area', '1500');
      expect(plotBoxes[1]).toHaveAttribute('data-plot-area-unit', 'rai');
    });

    it('should show empty state when no plots exist', () => {
      render(<FarmDetail {...mockProps} plots={[]} />);

      expect(screen.getByTestId('next-image')).toBeInTheDocument();
      expect(screen.getByText('no-plot-selected')).toBeInTheDocument();
      expect(screen.queryByTestId('plot-option-box')).not.toBeInTheDocument();
    });

    it('should handle plots with missing data gracefully', () => {
      const plotsWithMissingData: Plot[] = [
        {
          id: 'plot-3',
          name: undefined,
          plotId: undefined,
          gap: undefined,
          area: undefined,
          areaUnit: undefined,
        },
      ];

      render(<FarmDetail {...mockProps} plots={plotsWithMissingData} />);

      const plotBox = screen.getByTestId('plot-option-box');
      expect(plotBox).toHaveAttribute('data-plot-number', '');
      expect(plotBox).toHaveAttribute('data-plot-id', '');
      expect(plotBox).toHaveAttribute('data-plot-gap', '');
      expect(plotBox).toHaveAttribute('data-plot-area', '0');
      expect(plotBox).toHaveAttribute('data-plot-area-unit', '');
    });
  });

  describe('Plot Actions', () => {
    it('should render image review modal for plots with images', () => {
      render(<FarmDetail {...mockProps} />);

      const imageModals = screen.getAllByTestId('image-review-modal');
      expect(imageModals).toHaveLength(1); // Only plot-1 has an image

      expect(imageModals[0]).toHaveAttribute('data-image-url', 'https://example.com/image1.jpg');
    });

    it('should render upload image button for plots without images', () => {
      render(<FarmDetail {...mockProps} />);

      const uploadButtons = screen.getAllByTestId('upload-plot-image');
      expect(uploadButtons).toHaveLength(1); // Only plot-2 doesn't have an image
    });

    it('should call addImageToPlot when upload image is clicked', () => {
      render(<FarmDetail {...mockProps} />);

      const uploadButton = screen.getByTestId('upload-plot-image');
      fireEvent.click(uploadButton);

      expect(mockProps.addImageToPlot).toHaveBeenCalledWith('plot-2', 'mock-image-string');
    });

    it('should call removeImageFromPlot when remove image button is clicked', () => {
      render(<FarmDetail {...mockProps} />);

      const removeImageButton = screen.getByTestId('ClearRoundedIcon');
      fireEvent.click(removeImageButton);

      expect(mockProps.removeImageFromPlot).toHaveBeenCalledWith('plot-1');
    });

    it('should call removePlot when delete plot button is clicked', () => {
      render(<FarmDetail {...mockProps} />);

      const deletePlotButtons = screen.getAllByTestId('DeleteOutlineOutlinedIcon');
      fireEvent.click(deletePlotButtons[0]);

      expect(mockProps.removePlot).toHaveBeenCalledWith('plot-1');
    });
  });

  describe('Add Plot Button', () => {
    it('should render add plot button', () => {
      render(<FarmDetail {...mockProps} />);

      const addButton = screen.getByRole('button', { name: /select-plot/i });
      expect(addButton).toBeInTheDocument();
    });

    it('should call addPlot when add plot button is clicked', () => {
      render(<FarmDetail {...mockProps} />);

      const addButton = screen.getByRole('button', { name: /select-plot/i });
      fireEvent.click(addButton);

      expect(mockProps.addPlot).toHaveBeenCalled();
    });
  });



  describe('Translations', () => {
    it('should display translated text', () => {
      render(<FarmDetail {...mockProps} />);

      // Check for specific translation keys that should be present
      expect(screen.getByText((_, element) => {
        const text = element?.textContent || '';
        return text.includes('farm-plot') && text.includes('required') && !text.includes('support');
      })).toBeInTheDocument();

      expect(screen.getByText('farm-plot-img-support')).toBeInTheDocument();
      expect(screen.getByText('select-plot')).toBeInTheDocument();
    });
  });

  describe('Component Integration', () => {
    it('should pass correct props to PlotOptionBox', () => {
      render(<FarmDetail {...mockProps} />);

      const plotBoxes = screen.getAllByTestId('plot-option-box');

      // Check first plot
      expect(plotBoxes[0]).toHaveAttribute('data-plot-number', 'Plot 1');
      expect(plotBoxes[0]).toHaveAttribute('data-plot-id', '1234567890123456');
      expect(plotBoxes[0]).toHaveAttribute('data-plot-gap', 'GAP001');
      expect(plotBoxes[0]).toHaveAttribute('data-plot-area', '1000');
      expect(plotBoxes[0]).toHaveAttribute('data-plot-area-unit', 'rai');

      // Check second plot
      expect(plotBoxes[1]).toHaveAttribute('data-plot-number', 'Plot 2');
      expect(plotBoxes[1]).toHaveAttribute('data-plot-id', '2345678901234567');
      expect(plotBoxes[1]).toHaveAttribute('data-plot-gap', 'GAP002');
      expect(plotBoxes[1]).toHaveAttribute('data-plot-area', '1500');
      expect(plotBoxes[1]).toHaveAttribute('data-plot-area-unit', 'rai');
    });

    it('should pass correct props to DetailRow', () => {
      render(<FarmDetail {...mockProps} />);

      const titles = screen.getAllByTestId('detail-row-title');
      const contents = screen.getAllByTestId('detail-row-content');

      expect(titles[0]).toHaveTextContent('farm-name');
      expect(contents[0]).toHaveTextContent('Test Farm');
      expect(titles[1]).toHaveTextContent('farm-address');
      expect(contents[1]).toHaveTextContent('123 Test Street');
    });

    it('should pass correct imageUrl to ImageReviewModal', () => {
      render(<FarmDetail {...mockProps} />);

      const imageModal = screen.getByTestId('image-review-modal');
      expect(imageModal).toHaveAttribute('data-image-url', 'https://example.com/image1.jpg');
    });
  });

  describe('Callback Functions', () => {
    it('should maintain callback references with useCallback', () => {
      const { rerender } = render(<FarmDetail {...mockProps} />);

      // Get initial callback references by triggering them
      const uploadButton = screen.getByTestId('upload-plot-image');
      fireEvent.click(uploadButton);
      expect(mockProps.addImageToPlot).toHaveBeenCalledTimes(1);

      // Re-render with same props
      rerender(<FarmDetail {...mockProps} />);

      // Callbacks should still work (indicating they're memoized)
      const uploadButtonAfterRerender = screen.getByTestId('upload-plot-image');
      fireEvent.click(uploadButtonAfterRerender);
      expect(mockProps.addImageToPlot).toHaveBeenCalledTimes(2);
    });

    it('should handle multiple plot actions correctly', () => {
      render(<FarmDetail {...mockProps} />);

      // Test remove image
      const removeImageButton = screen.getByTestId('ClearRoundedIcon');
      fireEvent.click(removeImageButton);
      expect(mockProps.removeImageFromPlot).toHaveBeenCalledWith('plot-1');

      // Test remove plot
      const deletePlotButtons = screen.getAllByTestId('DeleteOutlineOutlinedIcon');
      fireEvent.click(deletePlotButtons[0]);
      expect(mockProps.removePlot).toHaveBeenCalledWith('plot-1');

      fireEvent.click(deletePlotButtons[1]);
      expect(mockProps.removePlot).toHaveBeenCalledWith('plot-2');

      // Test add image
      const uploadButton = screen.getByTestId('upload-plot-image');
      fireEvent.click(uploadButton);
      expect(mockProps.addImageToPlot).toHaveBeenCalledWith('plot-2', 'mock-image-string');
    });
  });

  describe('Edge Cases and Boundary Conditions', () => {
    it('should handle empty plot array', () => {
      render(<FarmDetail {...mockProps} plots={[]} />);

      expect(screen.queryByTestId('plot-option-box')).not.toBeInTheDocument();
      expect(screen.getByTestId('next-image')).toBeInTheDocument();
      expect(screen.getByText('no-plot-selected')).toBeInTheDocument();
    });

    it('should handle null/undefined farm properties', () => {
      const farmWithNullProps: Farm = {
        id: 'farm-null',
        name: null as unknown as string,
        address: null as unknown as string,
      };

      render(<FarmDetail {...mockProps} farm={farmWithNullProps} />);

      const contents = screen.getAllByTestId('detail-row-content');
      expect(contents[0]).toHaveTextContent('--');
      expect(contents[1]).toHaveTextContent('--');
    });

    it('should handle plots with all optional properties undefined', () => {
      const minimalPlots: Plot[] = [
        {
          id: 'minimal-plot',
        },
      ];

      render(<FarmDetail {...mockProps} plots={minimalPlots} />);

      const plotBox = screen.getByTestId('plot-option-box');
      expect(plotBox).toHaveAttribute('data-plot-number', '');
      expect(plotBox).toHaveAttribute('data-plot-id', '');
      expect(plotBox).toHaveAttribute('data-plot-gap', '');
      expect(plotBox).toHaveAttribute('data-plot-area', '0');
      expect(plotBox).toHaveAttribute('data-plot-area-unit', '');
    });

    it('should handle large number of plots', () => {
      const manyPlots: Plot[] = Array.from({ length: 10 }, (_, index) => ({
        id: `plot-${index}`,
        name: `Plot ${index + 1}`,
        plotId: `${index}`.padStart(16, '0'),
        area: (index + 1) * 100,
        areaUnit: 'rai',
        gap: `GAP${index.toString().padStart(3, '0')}`,
      }));

      render(<FarmDetail {...mockProps} plots={manyPlots} />);

      const plotBoxes = screen.getAllByTestId('plot-option-box');
      expect(plotBoxes).toHaveLength(10);

      // Check first and last plots
      expect(plotBoxes[0]).toHaveAttribute('data-plot-number', 'Plot 1');
      expect(plotBoxes[9]).toHaveAttribute('data-plot-number', 'Plot 10');
    });
  });

  describe('Accessibility', () => {
    it('should have proper button roles and labels', () => {
      render(<FarmDetail {...mockProps} />);

      const addPlotButton = screen.getByRole('button', { name: /select-plot/i });
      expect(addPlotButton).toBeInTheDocument();
      // Check that it's a MUI outlined button by checking for the CSS class
      expect(addPlotButton.className).toContain('MuiButton-outlined');
    });

    it('should have proper image alt text', () => {
      render(<FarmDetail {...mockProps} plots={[]} />);

      const emptyStateImage = screen.getByTestId('next-image');
      expect(emptyStateImage).toHaveAttribute('alt', 'empty-state-icon');
    });
  });
});
