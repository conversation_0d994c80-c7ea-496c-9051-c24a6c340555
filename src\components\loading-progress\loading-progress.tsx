import * as React from 'react';
import CircularProgress, { circularProgressClasses, CircularProgressProps } from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import { FC } from 'react';

function FacebookCircularProgress(props: CircularProgressProps) {
  return (
    <Box sx={{ position: 'relative' }}>
      <CircularProgress
        variant="determinate"
        sx={(theme) => ({
          color: theme.palette.grey[200],
          ...theme.applyStyles('dark', {
            color: theme.palette.grey[800],
          }),
        })}
        size={40}
        thickness={4}
        {...props}
        value={100}
      />
      <CircularProgress
        variant="indeterminate"
        disableShrink
        sx={(theme) => ({
          color: theme.palette.customColors.primary,
          animationDuration: '550ms',
          position: 'absolute',
          left: 0,
          [`& .${circularProgressClasses.circle}`]: {
            strokeLinecap: 'round',
          },
          ...theme.applyStyles('dark', {
            color: theme.palette.customColors.primary,
          }),
        })}
        size={40}
        thickness={4}
        {...props}
      />
    </Box>
  );
}
interface CircularWithValueLabelProps {
  title?: string;
}

export const CircularWithValueLabel: FC<CircularWithValueLabelProps> = ({ title }) => {
  return (
    <Box sx={{ display: 'flex', width: '100%', height: '100%', justifyContent: 'center', alignItems: 'center', flexDirection: 'column', gap: '12px' }}>
      <FacebookCircularProgress />
      <Typography variant="caption" component="div" sx={{ color: 'text.secondary' }}>
        {title}
      </Typography>
    </Box>
  );
};
