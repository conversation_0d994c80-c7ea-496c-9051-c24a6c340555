'use client';

import { Box, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC } from 'react';
import { ShipmentHistory, SortOrder } from 'types/shipment-history';
import { HistoryEntry } from './history-entry';
import { HistoryError } from './history-error';
import { theme } from 'styles/theme';

interface HistoryDisplayProps {
  entries: ShipmentHistory['entries'];
  sortOrder: SortOrder;
  error?: string;
  onRetry?: () => void;
}

export const HistoryDisplay: FC<HistoryDisplayProps> = ({
  entries,
  sortOrder,
  error,
  onRetry,
}) => {
  const shipmentT = useTranslations('shipment');
  const commonT = useTranslations('common');

  const sortedEntries = [...entries].sort((a, b) => {
    const dateA = new Date(a.timestamp).getTime();
    const dateB = new Date(b.timestamp).getTime();

    return sortOrder === 'newest' ? dateB - dateA : dateA - dateB;
  });


  if (error) {
    return <HistoryError error={error} onRetry={onRetry} />;
  }

  if (entries.length === 0) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '200px',
          flexDirection: 'column',
          gap: 2,
        }}
      >
        <Typography variant="h6" color="text.secondary">
          {commonT('data-not-found')}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {shipmentT('history-no-entries')}
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        backgroundColor: theme.palette.customColors.white,
        borderRadius: '8px',
        border: `1px solid ${theme.palette.customColors.gray5}`,
        p: 3,
        position: 'relative',
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          left: '44px',
          top: '60px',
          bottom: '100px',
          width: '2px',
          backgroundColor: theme.palette.customColors.gray5,
          zIndex: 0,
        }}
      />

      {sortedEntries.map((entry, index) => (
        <Box key={entry.id} sx={{ position: 'relative', zIndex: 1 }}>
          <HistoryEntry entry={entry} />
          {index < sortedEntries.length - 1 && <Box sx={{ height: '16px' }} />}
        </Box>
      ))}
    </Box>
  );
};
