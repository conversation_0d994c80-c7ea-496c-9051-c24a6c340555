/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AddPlotDrawer } from '../add-plot-drawer';
import { Farm, Plot } from 'types';

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => key,
}));

// Mock components
jest.mock('components', () => ({
  Drawer: ({ children, open, onClose, drawerTitle, footerElement, anchor, hasActionBtn }: any) => (
    <div data-testid="drawer" data-open={open} data-anchor={anchor} data-has-action-btn={hasActionBtn}>
      <div data-testid="drawer-title">{drawerTitle}</div>
      <div data-testid="drawer-content">{children}</div>
      <div data-testid="drawer-footer">{footerElement}</div>
      <button data-testid="drawer-close" onClick={onClose}>Close</button>
    </div>
  ),
}));

// Mock AddPlot component
jest.mock('../add-plot', () => ({
  AddPlot: ({ farm, selectedPlots, setSelectedPlots }: any) => (
    <div data-testid="add-plot">
      <div data-testid="add-plot-farm-id">{farm.id}</div>
      <div data-testid="add-plot-farm-name">{farm.name}</div>
      <div data-testid="selected-plots-count">{selectedPlots.length}</div>
      <button
        data-testid="select-plot-btn"
        onClick={() => setSelectedPlots([...selectedPlots, { id: 'new-plot', name: 'New Plot' }])}
      >
        Select Plot
      </button>
      <button
        data-testid="deselect-plot-btn"
        onClick={() => setSelectedPlots(selectedPlots.slice(0, -1))}
      >
        Deselect Plot
      </button>
      <button
        data-testid="clear-plots-btn"
        onClick={() => setSelectedPlots([])}
      >
        Clear All Plots
      </button>
    </div>
  ),
}));

describe('AddPlotDrawer Component', () => {
  const mockToggle = jest.fn();
  const mockOnAddPlot = jest.fn();

  const mockFarm: Farm = {
    id: 'farm-1',
    name: 'Test Farm',
    address: '123 Test Street',
    gln: 'GLN123',
    gap: 'GAP123',
  };

  const mockPlots: Plot[] = [
    {
      id: 'plot-1',
      name: 'Plot 1',
      area: 1000,
      areaUnit: 'rai',
      gap: 'GAP001',
      image: 'image1.jpg',
    },
    {
      id: 'plot-2',
      name: 'Plot 2',
      area: 1500,
      areaUnit: 'rai',
      gap: 'GAP002',
    },
  ];

  const defaultProps = {
    open: true,
    toggle: mockToggle,
    farm: mockFarm,
    plot: mockPlots,
    onAddPlot: mockOnAddPlot,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const renderComponent = (props = {}) => {
    return render(<AddPlotDrawer {...defaultProps} {...props} />);
  };

  describe('Initial Render', () => {
    it('should render drawer with correct title and initial state', () => {
      renderComponent();

      expect(screen.getByTestId('drawer')).toBeInTheDocument();
      expect(screen.getByTestId('drawer-title')).toHaveTextContent('select-plot');
      expect(screen.getByTestId('drawer')).toHaveAttribute('data-open', 'true');
      expect(screen.getByTestId('drawer')).toHaveAttribute('data-anchor', 'right');
      expect(screen.getByTestId('drawer')).toHaveAttribute('data-has-action-btn', 'false');
    });

    it('should render AddPlot component with correct props', () => {
      renderComponent();

      expect(screen.getByTestId('add-plot')).toBeInTheDocument();
      expect(screen.getByTestId('add-plot-farm-id')).toHaveTextContent('farm-1');
      expect(screen.getByTestId('add-plot-farm-name')).toHaveTextContent('Test Farm');
    });

    it('should render footer with cancel and confirm buttons', () => {
      renderComponent();

      expect(screen.getByText('cancel-modal-btn')).toBeInTheDocument();
      expect(screen.getByText('confirm-modal-btn')).toBeInTheDocument();
    });
  });

  describe('Initial Plot Selection', () => {
    it('should initialize with provided plots when drawer opens', () => {
      renderComponent();

      // Should show the count of initially provided plots
      expect(screen.getByTestId('selected-plots-count')).toHaveTextContent('2');
    });

    it('should not initialize plots when drawer is closed', () => {
      renderComponent({ open: false });

      // Should show 0 plots when drawer is closed
      expect(screen.getByTestId('selected-plots-count')).toHaveTextContent('0');
    });

    it('should not initialize plots when no plots are provided', () => {
      renderComponent({ plot: [] });

      // Should show 0 plots when no plots provided
      expect(screen.getByTestId('selected-plots-count')).toHaveTextContent('0');
    });

    it('should reinitialize plots when drawer reopens with new plots', () => {
      const { rerender } = renderComponent({ open: false });

      // Initially closed, should have 0 plots
      expect(screen.getByTestId('selected-plots-count')).toHaveTextContent('0');

      // Reopen with plots
      rerender(<AddPlotDrawer {...defaultProps} open={true} />);

      // Should now show the provided plots
      expect(screen.getByTestId('selected-plots-count')).toHaveTextContent('2');
    });
  });

  describe('Plot Selection Management', () => {
    it('should allow selecting additional plots', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Initially has 2 plots
      expect(screen.getByTestId('selected-plots-count')).toHaveTextContent('2');

      // Select additional plot
      const selectButton = screen.getByTestId('select-plot-btn');
      await user.click(selectButton);

      // Should now have 3 plots
      expect(screen.getByTestId('selected-plots-count')).toHaveTextContent('3');
    });

    it('should allow deselecting plots', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Initially has 2 plots
      expect(screen.getByTestId('selected-plots-count')).toHaveTextContent('2');

      // Deselect a plot
      const deselectButton = screen.getByTestId('deselect-plot-btn');
      await user.click(deselectButton);

      // Should now have 1 plot
      expect(screen.getByTestId('selected-plots-count')).toHaveTextContent('1');
    });

    it('should allow clearing all plots', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Initially has 2 plots
      expect(screen.getByTestId('selected-plots-count')).toHaveTextContent('2');

      // Clear all plots
      const clearButton = screen.getByTestId('clear-plots-btn');
      await user.click(clearButton);

      // Should now have 0 plots
      expect(screen.getByTestId('selected-plots-count')).toHaveTextContent('0');
    });
  });

  describe('Save Button Logic', () => {
    it('should enable confirm button when plots are selected', () => {
      renderComponent();

      const confirmButton = screen.getByText('confirm-modal-btn');
      expect(confirmButton).not.toBeDisabled();
    });

    it('should disable confirm button when no plots are selected', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Clear all plots
      const clearButton = screen.getByTestId('clear-plots-btn');
      await user.click(clearButton);

      // Confirm button should be disabled
      const confirmButton = screen.getByText('confirm-modal-btn');
      expect(confirmButton).toBeDisabled();
    });

    it('should call onAddPlot and close drawer when confirm button is clicked with valid data', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Click confirm
      const confirmButton = screen.getByText('confirm-modal-btn');
      await user.click(confirmButton);

      expect(mockOnAddPlot).toHaveBeenCalledWith(mockPlots);
      expect(mockToggle).toHaveBeenCalled();
    });

    it('should not call onAddPlot when confirm button is clicked with no plots', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Clear all plots first
      const clearButton = screen.getByTestId('clear-plots-btn');
      await user.click(clearButton);

      // Try to click confirm (should be disabled)
      const confirmButton = screen.getByText('confirm-modal-btn');
      expect(confirmButton).toBeDisabled();

      // onAddPlot should not be called
      expect(mockOnAddPlot).not.toHaveBeenCalled();
    });
  });

  describe('Cancel and Close Functionality', () => {
    it('should call toggle when cancel button is clicked', async () => {
      const user = userEvent.setup();
      renderComponent();

      const cancelButton = screen.getByText('cancel-modal-btn');
      await user.click(cancelButton);

      expect(mockToggle).toHaveBeenCalled();
    });

    it('should call toggle when drawer close button is clicked', async () => {
      const user = userEvent.setup();
      renderComponent();

      const closeButton = screen.getByTestId('drawer-close');
      await user.click(closeButton);

      expect(mockToggle).toHaveBeenCalled();
    });

    it('should reset selected plots when drawer is closed', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Add an additional plot
      const selectButton = screen.getByTestId('select-plot-btn');
      await user.click(selectButton);

      // Should have 3 plots
      expect(screen.getByTestId('selected-plots-count')).toHaveTextContent('3');

      // Close drawer
      const cancelButton = screen.getByText('cancel-modal-btn');
      await user.click(cancelButton);

      expect(mockToggle).toHaveBeenCalled();
    });
  });

  describe('Component Props Handling', () => {
    it('should handle farm prop correctly', () => {
      const customFarm: Farm = {
        id: 'custom-farm',
        name: 'Custom Farm Name',
        address: 'Custom Address',
        gln: 'CUSTOM_GLN',
        gap: 'CUSTOM_GAP',
      };

      renderComponent({ farm: customFarm });

      expect(screen.getByTestId('add-plot-farm-id')).toHaveTextContent('custom-farm');
      expect(screen.getByTestId('add-plot-farm-name')).toHaveTextContent('Custom Farm Name');
    });

    it('should handle empty plot array', () => {
      renderComponent({ plot: [] });

      expect(screen.getByTestId('selected-plots-count')).toHaveTextContent('0');

      const confirmButton = screen.getByText('confirm-modal-btn');
      expect(confirmButton).toBeDisabled();
    });

    it('should handle single plot', () => {
      const singlePlot = [mockPlots[0]];
      renderComponent({ plot: singlePlot });

      expect(screen.getByTestId('selected-plots-count')).toHaveTextContent('1');

      const confirmButton = screen.getByText('confirm-modal-btn');
      expect(confirmButton).not.toBeDisabled();
    });
  });

  describe('State Management', () => {
    it('should maintain independent state from props after user interactions', async () => {
      const user = userEvent.setup();
      renderComponent();

      // Initially has 2 plots from props
      expect(screen.getByTestId('selected-plots-count')).toHaveTextContent('2');

      // User adds a plot
      const selectButton = screen.getByTestId('select-plot-btn');
      await user.click(selectButton);

      // Should have 3 plots (2 from props + 1 added)
      expect(screen.getByTestId('selected-plots-count')).toHaveTextContent('3');

      // User removes a plot
      const deselectButton = screen.getByTestId('deselect-plot-btn');
      await user.click(deselectButton);

      // Should have 2 plots again
      expect(screen.getByTestId('selected-plots-count')).toHaveTextContent('2');
    });

    it('should reset to initial state when drawer reopens', () => {
      const { rerender } = renderComponent();

      // Close drawer
      rerender(<AddPlotDrawer {...defaultProps} open={false} />);

      // Reopen drawer
      rerender(<AddPlotDrawer {...defaultProps} open={true} />);

      // Should be back to initial state with 2 plots
      expect(screen.getByTestId('selected-plots-count')).toHaveTextContent('2');
    });
  });
});
