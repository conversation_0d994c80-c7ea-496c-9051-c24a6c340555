import {
  calculateTotalWeight,
  convertResponseToWeights,
  compareVarieties,
  convertModalDataToVarieties,
  transformDurianDataEvent,
  reorderToOriginalOrder,
  transformArray,
} from 'utils/event';
import {  DurianVariety, PackingHouse, ReceiveUpdatePayload } from 'types';

const mockVarieties: DurianVariety[] = [
  {
    id: 'variety1',
    value: 'monthong',
    label: { th: 'มันทอง', en: 'Monthong' },
    flowerBloomingDay: 1751513154,
    name: 'Monthong',
    grades: [
      { id: 'grade1', value: 'A', label: { th: 'เกรด A', en: 'Grade A' }, weight: 10 },
      { id: 'grade2', value: 'B', label: { th: 'เกรด B', en: 'Grade B' }, weight: 15 },
    ],
  },
  {
    id: 'variety2',
    value: 'kanyao',
    label: { th: 'ก้านยาว', en: 'Kanyao' },
    flowerBloomingDay: 1751513154,
    name: 'Kanya<PERSON>',
    grades: [
      { id: 'grade1', value: 'A', label: { th: 'เกรด A', en: 'Grade A' }, weight: 5 },
    ],
  },
];

const mockPackingHouse  = {
  id: 'ph1',
  varieties: mockVarieties,
  name: 'Test Packing House',
  status: 'active',
  eventStatus: 'pending',
  eventType: 'receive',
  description: 'Mock packing house for testing',
  dateCreated: new Date().toISOString(),
  dateUpdated: new Date().toISOString(),
  address: '123 Test Street',
} as PackingHouse;

const mockReceiveUpdatePayload: ReceiveUpdatePayload = {
  update: {
    varieties: [
      {
        id: 'variety1',
        flowerBloomingDay: 1751513154,
        grades: [
          { id: 'grade1', weight: 12 },
          { id: 'grade2', weight: 18 },
        ],
      },
    ],
  },
};

describe('Event Utility Functions', () => {
  describe('calculateTotalWeight', () => {
    it('should calculate total weight correctly', () => {
      const result = calculateTotalWeight(mockPackingHouse);
      // 10 + 15 + 5 = 30
      expect(result).toBe(30);
    });

    it('should return 0 for null input', () => {
      expect(calculateTotalWeight(null)).toBe(0);
    });

    it('should return 0 for empty varieties', () => {
      const emptyPackingHouse = { ...mockPackingHouse, varieties: [] };
      expect(calculateTotalWeight(emptyPackingHouse)).toBe(0);
    });
  });

  describe('convertResponseToWeights', () => {
    it('should convert variety data to weights object', () => {
      const result = convertResponseToWeights(mockVarieties);
      expect(result).toEqual({
        'variety1-grade1': '10',
        'variety1-grade2': '15',
        'variety2-grade1': '5',
      });
    });

    it('should return empty object for null input', () => {
      expect(convertResponseToWeights(undefined)).toEqual({});
    });

    it('should handle empty array input', () => {
      expect(convertResponseToWeights([])).toEqual({});
    });
  });

  describe('compareVarieties', () => {
    it('should detect no changes when varieties are identical', () => {
      const result = compareVarieties(mockVarieties, mockVarieties);
      expect(result).toEqual({
        hasChanges: false,
        changedVarietyIds: [],
      });
    });

    it('should detect changes when weights differ', () => {
      const updatedVarieties = JSON.parse(JSON.stringify(mockVarieties));
      updatedVarieties[0].grades[0].weight = 20;

      const result = compareVarieties(mockVarieties, updatedVarieties);
      expect(result).toEqual({
        hasChanges: true,
        changedVarietyIds: ['variety1'],
      });
    });

    it('should detect changes when array lengths differ', () => {
      const result = compareVarieties(mockVarieties, [mockVarieties[0]]);
      expect(result).toEqual({
        hasChanges: true,
        changedVarietyIds: ['variety1'],
      });
    });
  });

  describe('convertModalDataToVarieties', () => {
    it('should convert modal data to varieties', () => {
      const result = convertModalDataToVarieties(mockReceiveUpdatePayload, mockVarieties);
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('variety1');
      expect(result[0].grades).toHaveLength(2);
      expect(result[0].grades[0].weight).toBe(12);
    });

    it('should return empty array for null input', () => {
      expect(convertModalDataToVarieties({} as ReceiveUpdatePayload, mockVarieties)).toEqual([]);
    });
  });

  describe('transformDurianDataEvent', () => {
    it('should transform durian varieties to display format', () => {
      const result = transformDurianDataEvent(mockVarieties);
      expect(result).toHaveLength(2);
      expect(result[0].displayText).toBe('มันทอง');
      expect(result[0].grades).toHaveLength(2);
      expect(result[0].grades[0].displayText).toBe('เกรด A');
    });

    it('should return empty array for null input', () => {
      expect(transformDurianDataEvent(null as unknown as DurianVariety[])).toEqual([]);
    });
  });

  describe('reorderToOriginalOrder', () => {
    it('should reorder transformed data to match original order', () => {
      const transformed = [
        { id: 'variety2', value: 'transformed2' },
        { id: 'variety1', value: 'transformed1' },
      ];

      const result = reorderToOriginalOrder(mockVarieties, transformed);
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('variety1');
      expect(result[1].id).toBe('variety2');
    });
  });

  describe('transformArray', () => {
    it('should transform array to simplified format', () => {
      const result = transformArray(mockVarieties);
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('variety1');
      expect(result[0].grades).toHaveLength(2);
      expect(result[0].grades[0]).toEqual({
        id: 'grade1',
        weight: 10,
      });
    });

    it('should include name property when available', () => {
      const mockWithNamedGrade = JSON.parse(JSON.stringify(mockVarieties));
      mockWithNamedGrade[0].grades[0].name = 'Named Grade';

      const result = transformArray(mockWithNamedGrade);
      expect(result[0].grades[0].name).toBe('Named Grade');
    });
  });
});
