'use client';

import { Box, Button, styled, Typography, useTheme } from '@mui/material';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { FC, useState } from 'react';
import { v4 } from 'uuid';

import emptyStateIcon from 'assets/icons/empty-state.svg';
import uploadIcon from 'assets/icons/upload.svg';
import UploadProgressUI from 'components/upload-file-progress/upload-file-progress';
import { ACCEPT_UPLOAD_FILES } from 'constant/common';
import { useCreateShipmentStore } from 'store/useCreateShipmentStore';
import { AcceptFileTypes } from 'types';
import { validateFile } from 'utils';
import toastMessages from 'utils/toastMessages';
import { logger } from 'utils/logger';

const MAX_UPLOAD_FILES = 20;

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

interface UploadDocumentsContentProps {
  minHeight?: number;
}

export const UploadDocumentsContent: FC<UploadDocumentsContentProps> = ({ minHeight }) => {
  const shipmentT = useTranslations('shipment');
  const { additionalFiles: uploadFiles, updateUploadFile, deleteUploadFile } = useCreateShipmentStore();
  const [uploading, setUploading] = useState(false);

  const listExpectDocumentT = useTranslations('list-expect-document');
  const formT = useTranslations('form');

  const theme = useTheme();

  const descriptionUpload = [
    listExpectDocumentT('declaration'),
    listExpectDocumentT('commercial-invoice'),
    listExpectDocumentT('bill-of-lading'),
    listExpectDocumentT('packing-list'),
    listExpectDocumentT('certificate-of-origin'),
    listExpectDocumentT('export-declaration-form'),
    listExpectDocumentT('audit-record'),
    listExpectDocumentT('laboratory-report'),
    listExpectDocumentT('export-compliance-documents'),
  ];

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const remainingSlots = MAX_UPLOAD_FILES - uploadFiles.length;

    if (remainingSlots <= 0) {
      toastMessages.error(formT('invalid-file'));
      event.target.value = '';
      return;
    }

    const allFiles = Array.from(files);
    const validFiles: File[] = [];
    let processedCount = 0;

    for (const file of allFiles) {
      if (validFiles.length >= remainingSlots) break;

      const { isValid } = validateFile(file);
      processedCount++;

      if (isValid) {
        validFiles.push(file);
      }
    }

    const validCount = validFiles.length;
    const invalidCount = processedCount - validCount;

    if (invalidCount > 0) {
      toastMessages.error(formT('invalid-file'));
    }

    // If no valid files, clear input and return
    if (validCount === 0) {
      event.target.value = '';
      return;
    }

    setUploading(true);

    const uploadPromises = validFiles.map(async (file) => {
      const fileId = v4();

      try {
        await updateUploadFile({
          id: fileId,
          name: file.name,
          size: file.size,
          type: file.type,
          url: '',
          file,
        });
      } catch (error) {
        logger.error(`Error uploading file "${file.name}":`, { error });
        deleteUploadFile(fileId);
        throw error;
      }
    });

    await Promise.allSettled(uploadPromises);
    setUploading(false);
    event.target.value = '';
  };

  return (
    <Box
      component="div"
      data-testid="upload-documents-container"
      sx={{
        width: '100%',
        display: 'flex',
        gap: '16px',
        minHeight: minHeight ? `${minHeight - 20}px` : '100%',
      }}
    >
      <Box
        sx={{
          height: '100%',
          alignItems: 'center',
          justifyContent: 'center',
          display: 'flex',
          p: '20px',
          flex: 1,
          border: `2px dashed ${theme.palette.customColors.neutralBorder}`,
          flexDirection: 'column',
          mt: '20px',
        }}
      >
        <Box
          component="div"
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: '12px',
            alignItems: 'center',
          }}
        >
          <Image src={uploadIcon} width={80} height={80} alt="upload-icon" />
          <Typography variant="body1">{shipmentT('upload-description')}</Typography>
          <Typography variant="body2" color="text.secondary">
            {shipmentT('upload-placeholder')}
          </Typography>

          <Button
            component="label"
            role={undefined}
            variant="outlined"
            tabIndex={-1}
            disabled={uploading}
            sx={{ width: '150px', mt: '12px' }}
          >
            {shipmentT('upload-btn-title')}
            <VisuallyHiddenInput
              accept={ACCEPT_UPLOAD_FILES.join(',')}
              type="file"
              multiple
              onChange={handleFileUpload}
            />
          </Button>
        </Box>

        <Box
          component="ul"
          mt="24px"
          sx={{
            fontWeight: 600,
            width: '100%',
            display: 'flex',
            alignItems: 'left',
            flexDirection: 'column',
          }}
        >
          {listExpectDocumentT('title')}

          <Box sx={{ pl: '16px', mt: '8px' }}>
            {descriptionUpload.map((item) => (
              <Box component="li" key={item} sx={{ fontWeight: 400, color: 'text.secondary' }}>
                {item}
              </Box>
            ))}
          </Box>
        </Box>
      </Box>

      <Box
        sx={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: uploadFiles.length > 0 ? 'flex-start' : 'center',
          mt: '20px',
        }}
      >
        {uploadFiles.length === 0 && (
          <Box
            component="div"
            sx={{
              width: '100%',
              display: 'flex',
              alignItems: 'center',
              flexDirection: 'column',
            }}
          >
            <Image src={emptyStateIcon} width={160} height={160} unoptimized alt="empty-state-icon" />
            <Typography my="12px" fontSize="16px" color="text.secondary">
              {shipmentT('no-file-uploaded')}
            </Typography>
          </Box>
        )}

        {uploadFiles.length > 0 && (
          <Box
            sx={{
              width: '100%',
              display: 'flex',
              flexDirection: 'column',
              gap: '12px',
              overflowY: 'auto',
            }}
          >
            {uploadFiles?.map((it) => (
              <UploadProgressUI
                key={it.id}
                fileId={it.id}
                onDelete={deleteUploadFile}
                fileName={it.name}
                fileSize={it.size}
                fileUrl={it.url ?? ''}
                uploading={it.isUploading}
                fileType={it.type as AcceptFileTypes}
              />
            ))}
          </Box>
        )}
      </Box>
    </Box>
  );
};
