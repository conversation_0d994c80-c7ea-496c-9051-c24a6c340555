import React from 'react';
import { useTranslations } from 'next-intl';
import {
  Box,
  Button,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Dialog as MuiDialog,
} from '@mui/material';
import { theme } from 'styles/theme';
import { Close } from '@mui/icons-material';
import { WarningIcon } from './warning-icon';

interface DialogContentProps {
  isOpen: boolean;
  title?: string;
  content?: string;
  okButtonText?: string;
  cancelButtonText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}

export const WarningDialog: React.FC<DialogContentProps> = ({
  isOpen,
  title,
  content,
  okButtonText,
  cancelButtonText,
  onConfirm,
  onCancel,
}) => {
  const commonTranslations = useTranslations('common');

  const handleConfirmNavigation = () => {
    if (!onConfirm) return;
    onConfirm();
  };

  const handleCancelNavigation = () => {
    if (!onCancel) return;
    onCancel();
  };

  return (
    <>
      <MuiDialog
        open={isOpen}
        onClose={handleCancelNavigation}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        disableScrollLock
      >
        <Box sx={{ display: 'flex', alignItems: 'start', padding: 4, gap: 2, pb: 0 }}>
          <Box>
            <WarningIcon />
          </Box>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <DialogTitle id="alert-dialog-title" sx={{ padding: 0 }}>
                {title || commonTranslations('warning-title')}
              </DialogTitle>
              <Close
                sx={{ color: theme.palette.customColors.black, fontSize: '28px' }}
                onClick={handleCancelNavigation}
              />
            </Box>
            <DialogContent sx={{ padding: 0 }}>
              <DialogContentText
                id="alert-dialog-description"
                sx={{ color: theme.palette.customColors.black, fontWeight: 400 }}
              >
                {content || commonTranslations('warning-content')}
              </DialogContentText>
            </DialogContent>
          </Box>
        </Box>
        <DialogActions sx={{ padding: 4, pt: 2 }}>
          <Button variant="outlined" onClick={handleCancelNavigation} autoFocus sx={{ width: '120px' }}>
            {cancelButtonText || commonTranslations('cancel-modal-btn')}
          </Button>
          <Button
            variant="contained"
            onClick={handleConfirmNavigation}
            sx={{ width: '120px', background: theme.palette.customColors.toastError }}
          >
            {okButtonText || commonTranslations('decline')}
          </Button>
        </DialogActions>
      </MuiDialog>
    </>
  );
};
