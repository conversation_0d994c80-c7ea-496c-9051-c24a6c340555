'use client';

import { useQuery } from '@tanstack/react-query';
import { queryKeys } from './_key';
import { getEventDetail } from 'services/event.service';
import { cloneDeep } from 'lodash-es';

export const useEventDetailQuery = (id: string, isShipment: boolean = false) => {
  return useQuery({
    queryKey: [queryKeys.EVENT, id],
    queryFn: () => getEventDetail(id, isShipment),
    select: (response) => {
      return cloneDeep(response.data);
    },
    enabled: <PERSON><PERSON>an(id),
  });
};
