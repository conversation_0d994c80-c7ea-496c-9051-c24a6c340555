'use client';

import Dashboard from './dashboard';
import { useEffect, useState } from 'react';

let loaded = false;

const DashboardContainer = () => {
  const [isLoading, setIsLoading] = useState(!loaded);

  useEffect(() => {
    if (loaded) return;
    setIsLoading(true);
    const loadingTimer = setTimeout(() => {
      setIsLoading(false);
      loaded = true;
    }, 500);

    return () => clearTimeout(loadingTimer);
  }, []);

  return <Dashboard isLoading={isLoading} />;
};

export default DashboardContainer;
