import { Box, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { FC } from 'react';
import { DetailRow, Drawer } from 'components';
import { Receipt } from 'types';
import { formatDateWithLocale } from 'containers/event/_components';

interface PQ7ReceiptDetailDrawerProps {
  data: Receipt;
  open: boolean;
  toggle: (open: boolean) => void;
}

export const PQ7ReceiptDetailDrawer: FC<PQ7ReceiptDetailDrawerProps> = ({ data, open, toggle }) => {
  const ocrTranslation = useTranslations('ocr');

  const onClose = () => {
    toggle(false);
  };

  const drawerTitle = `${ocrTranslation('receipt-number-input')}`;

  return (
    <Drawer
      anchor="right"
      drawerTitle={drawerTitle}
      open={open}
      onClose={onClose}
      hasActionBtn={true}
      onConfirm={onClose}
    >
      <Box
        component="div"
        sx={{ width: '100%', p: '24px 16px', display: 'flex', flexDirection: 'column', gap: '16px' }}
      >
        <Typography variant="h6">{data.receiptNumber}</Typography>
        <DetailRow
          sx={{ flexDirection: 'row' }}
          title={ocrTranslation('destination-country')}
          content={data.destinationCountry}
        />
        <DetailRow
          sx={{ flexDirection: 'row' }}
          title={ocrTranslation('transport-mode')}
          content={data.transportationMode}
        />
        <DetailRow sx={{ flexDirection: 'row' }} title={ocrTranslation('total-weight')} content={data.totalWeightKg} />
        <DetailRow
          sx={{ flexDirection: 'row' }}
          title={ocrTranslation('number-of-boxes')}
          content={data.numberOfBoxes}
        />
        <DetailRow
          sx={{ flexDirection: 'row' }}
          title={ocrTranslation('export-date')}
          content={data.exportDate ? formatDateWithLocale(data.exportDate) : ''}
        />
        <DetailRow
          sx={{ flexDirection: 'row' }}
          title={ocrTranslation('container-number')}
          content={data.containerNumber}
        />
        <DetailRow
          sx={{ flexDirection: 'row' }}
          title={ocrTranslation('truck-number')}
          content={data.truckRegistrationNumber}
        />
        <DetailRow
          sx={{ flexDirection: 'row' }}
          title={ocrTranslation('trailer-number')}
          content={data.trailerRegistrationNumber}
        />
        <DetailRow
          sx={{ flexDirection: 'row' }}
          title={ocrTranslation('orchard-no')}
          content={data.orchardRegisterNumber}
        />
      </Box>
    </Drawer>
  );
};
