import { render, screen, fireEvent } from '@testing-library/react';
import { useTranslations } from 'next-intl';
import { HistorySortButtons } from '../history-sort-buttons';

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: jest.fn(() => (key: string) => {
    const translations: Record<string, string> = {
      'history-newest-first': 'Newest First',
      'history-oldest-first': 'Oldest First',
    };
    return translations[key] || key;
  }),
}));

const mockUseTranslations = useTranslations as jest.MockedFunction<typeof useTranslations>;

describe('HistorySortButtons', () => {
  const mockOnSortChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the correct sort button based on current order', () => {
    // When sortOrder is "newest", it should show "Newest First" button
    const { rerender } = render(
      <HistorySortButtons
        sortOrder="newest"
        onSortChange={mockOnSortChange}
      />
    );

    expect(screen.getByText('Newest First')).toBeInTheDocument();
    expect(screen.queryByText('Oldest First')).not.toBeInTheDocument();

    // When sortOrder is "oldest", it should show "Oldest First" button
    rerender(
      <HistorySortButtons
        sortOrder="oldest"
        onSortChange={mockOnSortChange}
      />
    );

    expect(screen.getByText('Oldest First')).toBeInTheDocument();
    expect(screen.queryByText('Newest First')).not.toBeInTheDocument();
  });

  it('highlights the active sort button', () => {
    render(
      <HistorySortButtons
        sortOrder="newest"
        onSortChange={mockOnSortChange}
      />
    );

    const newestButton = screen.getByText('Newest First');

    // Check if the button has active styling
    expect(newestButton.closest('button')).toBeInTheDocument();
  });

  it('calls onSortChange when clicking oldest first button (when current is oldest)', () => {
    render(
      <HistorySortButtons
        sortOrder="oldest"
        onSortChange={mockOnSortChange}
      />
    );

    const oldestButton = screen.getByText('Oldest First');
    fireEvent.click(oldestButton);

    expect(mockOnSortChange).toHaveBeenCalledWith('newest');
  });

  it('calls onSortChange when clicking newest first button (when current is newest)', () => {
    render(
      <HistorySortButtons
        sortOrder="newest"
        onSortChange={mockOnSortChange}
      />
    );

    const newestButton = screen.getByText('Newest First');
    fireEvent.click(newestButton);

    expect(mockOnSortChange).toHaveBeenCalledWith('oldest');
  });

  it('uses correct translation keys', () => {
    render(
      <HistorySortButtons
        sortOrder="newest"
        onSortChange={mockOnSortChange}
      />
    );

    expect(mockUseTranslations).toHaveBeenCalledWith('shipment');
  });

  it('renders with proper button styling', () => {
    render(
      <HistorySortButtons
        sortOrder="newest"
        onSortChange={mockOnSortChange}
      />
    );

    const buttons = screen.getAllByRole('button');
    expect(buttons).toHaveLength(1);

    buttons.forEach(button => {
      expect(button).toHaveStyle({
        borderRadius: '4px',
        fontWeight: '600',
        textTransform: 'none',
        minWidth: '120px',
        height: '40px',
      });
    });
  });
});
