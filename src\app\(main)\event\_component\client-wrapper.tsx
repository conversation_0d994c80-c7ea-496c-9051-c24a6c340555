'use client';

import { PackingHouseDetail } from 'types';
import dynamic from 'next/dynamic';

const EventDetail = dynamic(() => import('containers/event').then((mod) => mod.EventDetail), {
  ssr: false, // This is now allowed because we're in a client component
});

const CreateExistenceDraft = dynamic(() => import('containers/event/create').then((mod) => mod.CreateEvent), {
  ssr: false, // This is now allowed because we're in a client component
});

type ClientWrapperProps = {
  eventDetail: PackingHouseDetail;
  id: string;
  isReceiving?: boolean; // Optional prop to indicate if it's a receiving event
  isCreate?: boolean;
};

export default function ClientWrapper({ eventDetail, id, isReceiving = false, isCreate = false }: ClientWrapperProps) {
  if (isCreate) return <CreateExistenceDraft key={id} eventDetail={eventDetail} />;
  return <EventDetail key={id} isReceiving={isReceiving} eventDetail={eventDetail} />;
}
