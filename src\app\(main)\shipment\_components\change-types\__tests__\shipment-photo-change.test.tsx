import { render, screen } from '@testing-library/react';
import { ShipmentPhotoChange } from '../shipment-photo-change';

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: jest.fn(() => (key: string) => {
    const translations: Record<string, string> = {
      'history-field-shipment-photos': 'Shipment photos',
      'history-value-removed': 'Removed',
      'history-value-added': 'Added',
    };
    return translations[key] || key;
  }),
}));

describe('ShipmentPhotoChange', () => {
  const mockChanges = [
    {
      field: 'shipmentPhotos',
      fieldLabel: 'Shipment photos',
      oldValue: 'shipment_photo17844.jpg',
      changeType: 'removed',
    },
    {
      field: 'shipmentPhotos',
      fieldLabel: 'Shipment photos',
      oldValue: 'shipment_photo12356.jpg',
      changeType: 'removed',
    },
    {
      field: 'shipmentPhotos',
      fieldLabel: 'Shipment photos',
      newValue: 'shipment_photo123jjgryu56.jpg',
      changeType: 'added',
    },
  ];

  it('renders shipment photos field title', () => {
    render(<ShipmentPhotoChange changes={mockChanges} />);
    
    expect(screen.getByText('Shipment photos')).toBeInTheDocument();
  });

  it('renders removed photos section', () => {
    render(<ShipmentPhotoChange changes={mockChanges} />);
    
    expect(screen.getByText('Removed')).toBeInTheDocument();
    expect(screen.getByText('shipment_photo17844.jpg')).toBeInTheDocument();
    expect(screen.getByText('shipment_photo12356.jpg')).toBeInTheDocument();
  });

  it('renders added photos section', () => {
    render(<ShipmentPhotoChange changes={mockChanges} />);
    
    expect(screen.getByText('Added')).toBeInTheDocument();
    expect(screen.getByText('shipment_photo123jjgryu56.jpg')).toBeInTheDocument();
  });

  it('renders photo thumbnails with proper styling', () => {
    render(<ShipmentPhotoChange changes={mockChanges} />);
    
    // Check for image icons (thumbnails)
    const imageIcons = screen.getAllByTestId('ImageIcon');
    expect(imageIcons.length).toBeGreaterThan(0);
  });

  it('handles only removed photos', () => {
    const removedOnlyChanges = [
      {
        field: 'shipmentPhotos',
        oldValue: 'removed_photo.jpg',
        changeType: 'removed',
      },
    ];

    render(<ShipmentPhotoChange changes={removedOnlyChanges} />);
    
    expect(screen.getByText('Removed')).toBeInTheDocument();
    expect(screen.getByText('removed_photo.jpg')).toBeInTheDocument();
    expect(screen.queryByText('Added')).not.toBeInTheDocument();
  });

  it('handles only added photos', () => {
    const addedOnlyChanges = [
      {
        field: 'shipmentPhotos',
        newValue: 'added_photo.jpg',
        changeType: 'added',
      },
    ];

    render(<ShipmentPhotoChange changes={addedOnlyChanges} />);
    
    expect(screen.getByText('Added')).toBeInTheDocument();
    expect(screen.getByText('added_photo.jpg')).toBeInTheDocument();
    expect(screen.queryByText('Removed')).not.toBeInTheDocument();
  });

  it('handles empty filename gracefully', () => {
    const emptyFilenameChanges = [
      {
        field: 'shipmentPhotos',
        newValue: '',
        changeType: 'added',
      },
    ];

    render(<ShipmentPhotoChange changes={emptyFilenameChanges} />);
    
    expect(screen.getByText('Added')).toBeInTheDocument();
    // Should render empty string without crashing
  });

  it('renders removed photos without special styling', () => {
    const removedChanges = [
      {
        field: 'shipmentPhotos',
        oldValue: 'removed_photo.jpg',
        changeType: 'removed',
      },
    ];

    render(<ShipmentPhotoChange changes={removedChanges} />);

    const removedPhoto = screen.getByText('removed_photo.jpg');
    expect(removedPhoto).toBeInTheDocument();
    // No special styling applied to removed photos
  });
});
