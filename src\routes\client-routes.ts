const basePath = '';

export const authRoutes = {
  login: basePath + '/login',
};

export const clientRoutes = {
  home: basePath + '/',

  // Event
  event: basePath + '/event',
  eventIncoming: basePath + '/event/incoming',
  eventCreate: basePath + '/event/incoming/create',
  eventReceiving: basePath + '/event/receiving',
  // Event
  shipment: basePath + '/shipment',
  createShipment: basePath + '/shipment/create',
  setting: basePath + '/settings',
  packingHouseInformation: basePath + '/settings/info',
  profile: basePath + '/profile',
  contactUs: basePath + '/contact-us',
  qr: basePath + '/qr-code',
};

export const protectedPaths = [
  {
    path: clientRoutes.home,
    exact: true,
  },
  {
    path: clientRoutes.event,
    exact: false,
  },
  {
    path: clientRoutes.shipment,
    exact: false,
  },
  {
    path: clientRoutes.setting,
    exact: false,
  },
  {
    path: clientRoutes.profile,
    exact: false,
  },
];

export const errorRoute = {
  notFound: '/not-found',
  error: '/error',
};

export const imageGetInternalUrl = '/api/directus/image';
