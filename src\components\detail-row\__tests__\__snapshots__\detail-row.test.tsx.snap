// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`DetailRow matches snapshot with ReactNode title and content 1`] = `
<div
  class="MuiStack-root css-12g3902-MuiStack-root"
>
  <p
    class="MuiTypography-root MuiTypography-body1 MuiTypography-alignCenter css-1lfeow5-MuiTypography-root"
    style="--Typography-textAlign: center;"
  >
    <span>
      Custom Title
    </span>
  </p>
  <div
    class="MuiTypography-root MuiTypography-body1 css-1kftr9u-MuiTypography-root"
  >
    <div>
      Custom Content
    </div>
  </div>
</div>
`;

exports[`DetailRow matches snapshot with all props enabled 1`] = `
<div
  class="MuiStack-root css-1yu4zg1-MuiStack-root"
>
  <p
    class="MuiTypography-root MuiTypography-body1 MuiTypography-alignCenter css-1lfeow5-MuiTypography-root"
    style="--Typography-textAlign: center;"
  >
    Title
  </p>
  <div
    class="MuiTypography-root MuiTypography-body1 css-1jz848c-MuiTypography-root"
  >
    Content
  </div>
</div>
`;

exports[`DetailRow matches snapshot with basic props 1`] = `
<div
  class="MuiStack-root css-12g3902-MuiStack-root"
>
  <p
    class="MuiTypography-root MuiTypography-body1 MuiTypography-alignCenter css-1lfeow5-MuiTypography-root"
    style="--Typography-textAlign: center;"
  >
    Test Title
  </p>
  <div
    class="MuiTypography-root MuiTypography-body1 css-1kftr9u-MuiTypography-root"
  >
    Test Content
  </div>
</div>
`;

exports[`DetailRow matches snapshot with default content 1`] = `
<div
  class="MuiStack-root css-12g3902-MuiStack-root"
>
  <p
    class="MuiTypography-root MuiTypography-body1 MuiTypography-alignCenter css-1lfeow5-MuiTypography-root"
    style="--Typography-textAlign: center;"
  >
    Test Title
  </p>
  <div
    class="MuiTypography-root MuiTypography-body1 css-1kftr9u-MuiTypography-root"
  >
    --
  </div>
</div>
`;
