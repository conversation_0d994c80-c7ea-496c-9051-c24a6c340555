'use client';

import { Box, Typography, Button, Paper } from '@mui/material';
import ReportProblemIcon from '@mui/icons-material/ReportProblem';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';

const COLORS = {
  primaryMain: '#4285F4',
  lightGray: '#f5f5f5',
  blueHighlight: '#2266D9',
  blue500: '#2196f3',
  blueTint: '#F5F9FF',
  blue50: '#E3F2FD',
};

export default function NotFound() {
  const notFoundT = useTranslations('not-found');
  const commonT = useTranslations('common');

  return (
    <Box
      sx={{
        height: 'calc(100vh - 70px)',
        p: 2,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: `linear-gradient(135deg, ${COLORS.blue50} 0%, ${COLORS.blueTint} 100%)`,
      }}
    >
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.4 }}
      >
        <Paper
          elevation={6}
          sx={{
            textAlign: 'center',
            p: 4,
            borderRadius: 2,
            backgroundColor: COLORS.lightGray,
          }}
        >
          <ReportProblemIcon sx={{ fontSize: 64, color: COLORS.blue500, mb: 2 }} />

          <Typography variant="h6" gutterBottom>
            {notFoundT('title')}
          </Typography>

          <Typography variant="body2" sx={{ color: COLORS.blue500, mb: 3 }}>
            {notFoundT('description')}
          </Typography>

          <Button
            component={Link}
            href="/"
            variant="contained"
            size="large"
            sx={{
              backgroundColor: COLORS.primaryMain,
              '&:hover': { backgroundColor: COLORS.blueHighlight },
            }}
          >
            {commonT('go-homepage')}
          </Button>
        </Paper>
      </motion.div>
    </Box>
  );
}
