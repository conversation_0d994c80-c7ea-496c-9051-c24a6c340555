/* eslint-disable @typescript-eslint/no-require-imports */
// generate log with prod tags only
// v1.x.x-prod format

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const outputFile = path.resolve('CHANGELOG.md');
const TAG_SUFFIX = '-prod';
const isCategorized = process.argv.includes('--categorized');

function extractTaskInfo(message) {
  // Extract branch name from merge commit message
  // Format: "Merge pull request #123 from owner/feat/MCDT-3564-description"
  const branchMatch = message.match(/from\s+[\w-]+\/([\w/-]+)/);
  if (branchMatch) {
    const branchName = branchMatch[1];

    // Extract type (feat, fix, etc.) and MCDT task ID
    const typeMatch = branchName.match(/^(feat|fix|chore|docs|style|refactor|test)\/MCDT-(\d+)/i);
    if (typeMatch) {
      const type = typeMatch[1].toLowerCase();
      // Map chore to refactor category
      const mappedType = type === 'chore' ? 'refactor' : type;
      const taskId = `MCDT-${typeMatch[2]}`;
      return {
        type: mappedType,
        taskId,
        branchName,
        isValid: true
      };
    }
  }
  return { isValid: false };
}

function getCommits(from, to) {
  const range = from ? `${from}..${to}` : to;
  console.log(`📋 Getting commits for range: ${range}`);

  const log = execSync(
    `git log ${range} --merges --pretty=format:"%s|%ad|%H" --date=short`
  ).toString();

  const allMerges = log
    .split('\n')
    .filter(line => line.includes('Merge pull request'))
    .map((line) => {
      const [message, date, hash] = line.split('|');
      return { message, date, hash };
    });

  console.log(`🔀 All merge commits found: ${allMerges.length}`);

  const mcdtMerges = allMerges
    .map(({ message, date, hash }) => {
      const taskInfo = extractTaskInfo(message);
      if (taskInfo.isValid) {
        // Get the actual commit message from the merged branch
        try {
          const actualMessage = execSync(
            `git log ${hash}^2 --pretty=format:"%s" -1`
          ).toString().trim();

          return {
            message: actualMessage || message,
            date,
            hash,
            ...taskInfo
          };
        } catch {
          // Fallback to merge message if can't get actual commit
          return {
            message,
            date,
            hash,
            ...taskInfo
          };
        }
      }
      return null;
    })
    .filter(commit => commit !== null);

  // Group by taskId and take the latest commit for each task
  const groupedByTask = mcdtMerges.reduce((acc, commit) => {
    const { taskId } = commit;
    if (!acc[taskId] || new Date(commit.date) > new Date(acc[taskId].date)) {
      acc[taskId] = commit;
    }
    return acc;
  }, {});

  const uniqueMcdtMerges = Object.values(groupedByTask);

  console.log(`🎯 MCDT merge commits found: ${mcdtMerges.length}`);
  console.log(`🔗 Unique MCDT tasks found: ${uniqueMcdtMerges.length}`);
  return uniqueMcdtMerges;
}

function getTagDate(tag) {
  return execSync(
    `git log -1 --pretty=format:"%ad" ${tag} --date=short`
  ).toString().trim();
}

function getLatestTag() {
  const allTags = execSync('git tag --sort=-version:refname').toString().trim().split('\n');
  const prodTags = allTags.filter(tag => tag.endsWith(TAG_SUFFIX) && tag.match(/^v\d+\.\d+\.\d+-prod$/));
  return prodTags.length > 0 ? prodTags[0] : null;
}

function generateChangelog() {
  const latestTag = getLatestTag();
  if (!latestTag) {
    console.log('⚠️ No v1.x.x-prod tags found.');
    console.log('💡 Try running with all tags to debug:');

    // Show recent tags for debugging
    const recentTags = execSync('git tag --sort=-version:refname | head -10').toString().trim().split('\n');
    console.log('📋 Recent tags:', recentTags);
    return;
  }

  let changelog = isCategorized
    ? `# 📝 Changelog (Latest ${latestTag} — MCDT tasks by category)\n\n`
    : `# 📝 Changelog (Latest ${latestTag} — MCDT PR merges)\n\n`;

  const commits = getCommits(null, latestTag);
  const date = getTagDate(latestTag);

  // Add the tag header
  changelog += `## ${latestTag} - ${date}\n`;

  if (!commits.length) {
    changelog += `\n*No MCDT commits found for this release.*\n\n`;
  } else {
    if (isCategorized) {
      // Group commits by type
      const groupedCommits = commits.reduce((acc, commit) => {
        const type = commit.type || 'other';
        if (!acc[type]) acc[type] = [];
        acc[type].push(commit);
        return acc;
      }, {});

      // Order types (chore is now mapped to refactor)
      const typeOrder = ['feat', 'fix', 'refactor', 'docs', 'style', 'test', 'other'];
      const typeLabels = {
        feat: '✨ Features',
        fix: '🐛 Bug Fixes',
        refactor: '♻️ Refactoring & Chores',
        docs: '📚 Documentation',
        style: '💄 Styling',
        test: '🧪 Tests',
        other: '📦 Other'
      };

      typeOrder.forEach(type => {
        if (groupedCommits[type]) {
          changelog += `\n### ${typeLabels[type]}\n`;

          // Sort commits by date in descending order (newest first)
          const sortedCommits = groupedCommits[type].sort((a, b) => {
            const dateA = new Date(a.date);
            const dateB = new Date(b.date);
            return dateB - dateA; // Descending order (newest first)
          });

          sortedCommits.forEach(({ taskId, message, date }) => {
            changelog += `- **${date}** - **${taskId}**: ${message.trim()}\n`;
          });
        }
      });
    } else {
      // Sort commits by date in descending order for non-categorized view
      const sortedCommits = commits.sort((a, b) => {
        const dateA = new Date(a.date);
        const dateB = new Date(b.date);
        return dateB - dateA; // Descending order (newest first)
      });

      sortedCommits.forEach(({ taskId, message, date }) => {
        const taskPrefix = taskId ? `**${taskId}**: ` : '';
        changelog += `- **${date}** - ${taskPrefix}${message.trim()}\n`;
      });
    }
  }

  fs.writeFileSync(outputFile, changelog, 'utf8');
  const mode = isCategorized ? 'categorized MCDT tasks' : 'MCDT PR merges';
  console.log(`✅ CHANGELOG.md generated for latest release ${latestTag} (${mode}).`);
}

generateChangelog();
